export enum BasicApprovalTypes {
  LEAVE_PROCESS = 'LEAVE_PROCESS',
  REST_PROCESS = 'REST_PROCESS',
  EXCHANGE_PROCESS = 'EXCHANGE_PROCESS',
  SUPPLY_CHECK_PROCESS = 'SUPPLY_CHECK_PROCESS',
  IN_DOOR = 'IN_DOOR', //"入门"
  OUT_DOOR = 'OUT_DOOR', //出门
  CHANGE = 'CHANGE', //'变更'
  VISITOR = 'VISITOR', //"人员入室申请"
  CHANGE_TEMPLATE = 'CHANGE_TEMPLATE', //变更模板
  ORDER_APPROVAL = 'ORDER_APPROVAL', //订单审批
  OBJECTION = 'OBJECTION', //账单异议
  WARRANTY = 'WARRANTY', //资产维保
  BILL_ADJUST = 'BILL_ADJUST', //调账
  BORROW = 'BORROW',
  RENEW = 'RENEW',
  TRANSFER = 'TRANSFER',
  CARD_APPLY = 'CARD_APPLY',
  CARD_CHANGE = 'CARD_CHANGE',
  CARD_EXCHANGE = 'CARD_EXCHANGE',
  MAINTENANCE_END = 'MAINTENANCE_END',
  CARD_OFF = 'CARD_OFF',
  POWER = 'POWER',
  AUTH_APPLY = 'AUTH_APPLY',
  CONSTRUCTION_APPLY = 'CONSTRUCTION_APPLY',
  OVERTIME_PROCESS = 'OVERTIME_PROCESS',
  OUTWORK_PROCESS = 'OUTWORK_PROCESS', //外勤申请
  REPAIR_END = 'REPAIR_END',
  ROLLBACK_ALTER = 'ROLLBACK_ALTER',
  EVENT_AUDIT_PROCESS = 'EVENT_AUDIT_PROCESS',
  N_EVENT_LEVEL_CHG = 'N_EVENT_LEVEL_CHG',
  INSPECT_END = 'INSPECT_END',
  ON_OFF_PROCESS = 'ON_OFF_PROCESS', //上下线申请
  COMMON_APPROVAL = 'COMMON_APPROVAL',
  EX_WAREHOUSE = 'EX_WAREHOUSE', //出库
  ASSET_CHANGE = 'ASSET_CHANGE', //固定资产编辑申请
  SALES_QUOTATION = 'SALES_QUOTATION', //销售报价
  PF_KPI = 'PF_KPI',
  POWER_END = 'POWER_END',
  RISK_REGISTER_EVAL = 'RISK_REGISTER_EVAL', // 风险单评估
  RISK_REGISTER_CLOSE = 'RISK_REGISTER_CLOSE', // 风险单关闭
  RISK_TIME_EXTENSION = 'RISK_TIME_EXTENSION', // 风险单延期
  RISK_ROC_UPGRADE = 'RISK_ROC_UPGRADE', //风险单升级ROC
  CUS_EFFECTED_APPLY = 'CUS_EFFECTED_APPLY', // 合作伙伴授权申请
  OUT_TO_REGULAR = 'OUT_TO_REGULAR', //外包转正申请
  BILL_FEE_ADD = 'BILL_FEE_ADD', // 新增费用申请
  CUST_QUOTATION_ADD = 'CUST_QUOTATION_ADD',
  CUST_QUOTATION_UPD = 'CUST_QUOTATION_UPD',
  EME_PROCESS = 'EME_PROCESS', // 演练复盘
  /** 在20240425这个窗口，取消了仅【阳高】环境才有「变更总结」 类型审批的限制*/
  CHANGE_SUMMERY = 'CHANGE_SUMMERY',
  STANDARD_CHANGE_TEMPLATE = 'STANDARD_CHANGE_TEMPLATE',
  STANDARD_CHANGE_TEMPLATE_POSTPONE = 'STANDARD_CHANGE_TEMPLATE_POSTPONE',
  CHANGE_ONLINE_APPLY = 'CHANGE_ONLINE_APPLY', //线上变更申请
  CHANGE_ONLINE_SUMMERY = 'CHANGE_ONLINE_SUMMERY', //线上变更总结
  CHANGE_ONLINE_DELAY = 'CHANGE_ONLINE_DELAY', //线上执行中延期
  CHANGE_ONLINE_TEMPLATE = 'CHANGE_ONLINE_TEMPLATE', //新建变更模版
  REPORT_AUTH_APPLY = 'REPORT_AUTH_APPLY', //报表权限申请
  PF_PERIOD_KPI_RECORD = 'PF_PERIOD_KPI_RECORD', //绩效季度加分
  OPERATION_DATA_CHANGE = 'OPERATION_DATA_CHANGE', // CRM-罗盘运营数据 操作数据变更
  CERT_APPROVAL = 'CERT_APPROVAL', //资质证书审批
}
const approvalTypes: {
  [key in BasicApprovalTypes]: string;
} = {
  /** 请假 */
  LEAVE_PROCESS: 'LEAVE_PROCESS',
  /** 顶班 */
  REST_PROCESS: 'REST_PROCESS',
  /** 换班 */
  EXCHANGE_PROCESS: 'EXCHANGE_PROCESS',
  /** 补卡 */
  SUPPLY_CHECK_PROCESS: 'SUPPLY_CHECK_PROCESS',
  IN_DOOR: 'IN_DOOR', //"入门"
  OUT_DOOR: 'OUT_DOOR', //出门
  CHANGE: 'CHANGE', //'变更'
  VISITOR: 'VISITOR', //"人员入室申请"
  CHANGE_TEMPLATE: 'CHANGE_TEMPLATE', //变更模板
  ORDER_APPROVAL: 'ORDER_APPROVAL', //订单审批
  OBJECTION: 'OBJECTION', //账单异议
  WARRANTY: 'WARRANTY', //资产维保
  BILL_ADJUST: 'BILL_ADJUST', //调账
  BORROW: 'BORROW',
  RENEW: 'RENEW',
  TRANSFER: 'TRANSFER',
  CARD_APPLY: 'CARD_APPLY',
  CARD_CHANGE: 'CARD_CHANGE',
  CARD_EXCHANGE: 'CARD_EXCHANGE',
  MAINTENANCE_END: 'MAINTENANCE_END',
  CARD_OFF: 'CARD_OFF',
  POWER: 'POWER',
  AUTH_APPLY: 'AUTH_APPLY',
  CONSTRUCTION_APPLY: 'CONSTRUCTION_APPLY',
  OVERTIME_PROCESS: 'OVERTIME_PROCESS',
  OUTWORK_PROCESS: 'OUTWORK_PROCESS', //外勤申请
  REPAIR_END: 'REPAIR_END',
  ROLLBACK_ALTER: 'ROLLBACK_ALTER',
  EVENT_AUDIT_PROCESS: 'EVENT_AUDIT_PROCESS',
  N_EVENT_LEVEL_CHG: 'N_EVENT_LEVEL_CHG',
  INSPECT_END: 'INSPECT_END',
  ON_OFF_PROCESS: 'ON_OFF_PROCESS', //上下线申请
  /** 日常通用申请 */
  COMMON_APPROVAL: 'COMMON_APPROVAL',
  EX_WAREHOUSE: 'EX_WAREHOUSE', //出库
  ASSET_CHANGE: 'ASSET_CHANGE', //固定资产编辑申请
  SALES_QUOTATION: 'SALES_QUOTATION', //销售报价
  PF_KPI: 'PF_KPI',
  POWER_END: 'POWER_END',
  RISK_REGISTER_EVAL: 'RISK_REGISTER_EVAL', // 风险单评估
  RISK_REGISTER_CLOSE: 'RISK_REGISTER_CLOSE', // 风险单关闭
  RISK_TIME_EXTENSION: 'RISK_TIME_EXTENSION', // 风险单延期
  RISK_ROC_UPGRADE: 'RISK_ROC_UPGRADE', //风险单升级ROC
  CUS_EFFECTED_APPLY: 'CUS_EFFECTED_APPLY', // 合作伙伴授权申请
  OUT_TO_REGULAR: 'OUT_TO_REGULAR', //外包转正申请
  BILL_FEE_ADD: 'BILL_FEE_ADD', // 新增费用申请
  CUST_QUOTATION_ADD: 'CUST_QUOTATION_ADD',
  CUST_QUOTATION_UPD: 'CUST_QUOTATION_UPD',
  EME_PROCESS: 'EME_PROCESS',
  CHANGE_SUMMERY: 'CHANGE_SUMMERY', // 变更总结
  STANDARD_CHANGE_TEMPLATE: 'STANDARD_CHANGE_TEMPLATE', // 变更模板录入
  STANDARD_CHANGE_TEMPLATE_POSTPONE: 'STANDARD_CHANGE_TEMPLATE_POSTPONE', // 变更模板延期
  CHANGE_ONLINE_APPLY: 'CHANGE_ONLINE_APPLY', //线上变更申请
  CHANGE_ONLINE_SUMMERY: 'CHANGE_ONLINE_SUMMERY', //线上变更总结
  CHANGE_ONLINE_DELAY: 'CHANGE_ONLINE_DELAY', //线上执行中延期
  CHANGE_ONLINE_TEMPLATE: 'CHANGE_ONLINE_TEMPLATE', //简版线上变更模版
  REPORT_AUTH_APPLY: 'REPORT_AUTH_APPLY', //报表权限申请
  PF_PERIOD_KPI_RECORD: 'PF_PERIOD_KPI_RECORD', //绩效季度加分
  OPERATION_DATA_CHANGE: 'OPERATION_DATA_CHANGE', // CRM-罗盘运营数据 操作数据变更
  CERT_APPROVAL: 'CERT_APPROVAL', //资质证书审批
};

const approvalTypeTextKeyMap = {
  [approvalTypes.LEAVE_PROCESS]: '请假申请',
  [approvalTypes.ROLLBACK_ALTER]: '销假申请',
  [approvalTypes.REST_PROCESS]: '顶班申请',
  [approvalTypes.EXCHANGE_PROCESS]: '换班申请',
  [approvalTypes.OVERTIME_PROCESS]: '加班申请',
  [approvalTypes.OUTWORK_PROCESS]: '外勤申请',
  [approvalTypes.IN_DOOR]: '入门申请',
  [approvalTypes.OUT_DOOR]: '出门申请',
  [approvalTypes.CHANGE]: '变更申请', //goc变更申请
  [approvalTypes.CHANGE_TEMPLATE]: '变更模板申请',
  [approvalTypes.VISITOR]: '人员进入',
  [approvalTypes.SUPPLY_CHECK_PROCESS]: '补卡申请',
  [approvalTypes.ORDER_APPROVAL]: '合同复核审批',
  [approvalTypes.OBJECTION]: '账单异议申请',
  [approvalTypes.WARRANTY]: '资产维保申请',
  [approvalTypes.BILL_ADJUST]: '调整申请',
  [approvalTypes.BORROW]: '借用归还申请',
  [approvalTypes.RENEW]: '续借申请',
  [approvalTypes.TRANSFER]: '转借申请',
  [approvalTypes.CARD_APPLY]: '门禁卡新增申请',
  [approvalTypes.CARD_CHANGE]: '门禁卡变更申请',
  [approvalTypes.CARD_EXCHANGE]: '门禁卡换卡申请',
  [approvalTypes.MAINTENANCE_END]: '维护关单申请',
  [approvalTypes.CARD_OFF]: '门禁卡注销申请',
  [approvalTypes.POWER]: '上下电申请',
  [approvalTypes.AUTH_APPLY]: '权限申请',
  [approvalTypes.CONSTRUCTION_APPLY]: '施工作业申请',
  [approvalTypes.REPAIR_END]: '维修关单申请',
  [approvalTypes.EVENT_AUDIT_PROCESS]: '事件评审',
  [approvalTypes.N_EVENT_LEVEL_CHG]: '事件等级校正',
  [approvalTypes.INSPECT_END]: '巡检关单申请',
  [approvalTypes.ON_OFF_PROCESS]: '上下线申请',
  [approvalTypes.COMMON_APPROVAL]: '日常通用申请',
  [approvalTypes.EX_WAREHOUSE]: '出库申请',
  [approvalTypes.ASSET_CHANGE]: '固定资产编辑申请',
  [approvalTypes.SALES_QUOTATION]: '销售报价申请',
  [approvalTypes.PF_KPI]: '绩效定制指标申请',
  [approvalTypes.POWER_END]: '上下电关单申请',
  [approvalTypes.RISK_REGISTER_EVAL]: '风险单评估',
  [approvalTypes.RISK_ROC_UPGRADE]: '风险单升级ROC',
  [approvalTypes.RISK_REGISTER_CLOSE]: '风险单关闭',
  [approvalTypes.RISK_TIME_EXTENSION]: '风险单延期',
  [approvalTypes.CHANGE_ONLINE_APPLY]: '变更申请', //阳高的变更申请
  [approvalTypes.CHANGE_ONLINE_SUMMERY]: '变更总结', //阳高变更总结
  [approvalTypes.CHANGE_ONLINE_DELAY]: '变更执行中延期', //阳高
  [approvalTypes.CHANGE_ONLINE_TEMPLATE]: '新建变更模板', //阳高
  [approvalTypes.CUS_EFFECTED_APPLY]: '合作伙伴授权申请',
  [approvalTypes.OUT_TO_REGULAR]: '外包转正申请',
  [approvalTypes.BILL_FEE_ADD]: '新增费用申请',
  [approvalTypes.CUST_QUOTATION_ADD]: '友商项目报价新增申请',
  [approvalTypes.CUST_QUOTATION_UPD]: '友商项目报价变更申请',
  [approvalTypes.EME_PROCESS]: '演练复盘',
  [approvalTypes.CHANGE_SUMMERY]: '变更总结', //GOC变更总结
  [approvalTypes.STANDARD_CHANGE_TEMPLATE]: '标准变更模版录入', //GOC
  [approvalTypes.STANDARD_CHANGE_TEMPLATE_POSTPONE]: '标准变更模版延期', //GOC
  [approvalTypes.REPORT_AUTH_APPLY]: '报表权限申请',
  [approvalTypes.PF_PERIOD_KPI_RECORD]: '绩效季度加分',
  [approvalTypes.OPERATION_DATA_CHANGE]: '罗盘数据变更',
  [approvalTypes.CERT_APPROVAL]: '资质证书审批',
};
// 该段逻辑不直接删除，用于为未来仅有【阳高】环境的审批类型做铺垫
// export enum SpecialApprovalTypes {
//   CHANGE_SUMMERY = 'CHANGE_SUMMERY',
// }
export function useApprovalTypes() {
  // const config = useSelector(selectCurrentConfig);

  // const configUtil = new ConfigUtil(config);

  // const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  // const changesTicketCreateChangeOffline =
  //   ticketScopeCommonConfigs.changes.features.createChangeOffline;
  // const isYgOffline = changesTicketCreateChangeOffline === 'full';

  // const approvalTypes = useMemo(() => {
  //   const basicApprovalTypes: {
  //     [key in BasicApprovalTypes]: string;
  //   } = {
  //     /** 请假 */
  //     LEAVE_PROCESS: 'LEAVE_PROCESS',
  //     /** 顶班 */
  //     REST_PROCESS: 'REST_PROCESS',
  //     /** 换班 */
  //     EXCHANGE_PROCESS: 'EXCHANGE_PROCESS',
  //     /** 补卡 */
  //     SUPPLY_CHECK_PROCESS: 'SUPPLY_CHECK_PROCESS',
  //     IN_DOOR: 'IN_DOOR', //"入门"
  //     OUT_DOOR: 'OUT_DOOR', //出门
  //     CHANGE: 'CHANGE', //'变更'
  //     VISITOR: 'VISITOR', //"人员入室申请"
  //     CHANGE_TEMPLATE: 'CHANGE_TEMPLATE', //变更模板
  //     ORDER_APPROVAL: 'ORDER_APPROVAL', //订单审批
  //     OBJECTION: 'OBJECTION', //账单异议
  //     WARRANTY: 'WARRANTY', //资产维保
  //     BILL_ADJUST: 'BILL_ADJUST', //调账
  //     BORROW: 'BORROW',
  //     RENEW: 'RENEW',
  //     TRANSFER: 'TRANSFER',
  //     CARD_APPLY: 'CARD_APPLY',
  //     CARD_CHANGE: 'CARD_CHANGE',
  //     CARD_OFF: 'CARD_OFF',
  //     POWER: 'POWER',
  //     AUTH_APPLY: 'AUTH_APPLY',
  //     CONSTRUCTION_APPLY: 'CONSTRUCTION_APPLY',
  //     OVERTIME_PROCESS: 'OVERTIME_PROCESS',
  //     OUTWORK_PROCESS: 'OUTWORK_PROCESS', //外勤申请
  //     REPAIR_END: 'REPAIR_END',
  //     ROLLBACK_ALTER: 'ROLLBACK_ALTER',
  //     EVENT_AUDIT_PROCESS: 'EVENT_AUDIT_PROCESS',
  //     INSPECT_END: 'INSPECT_END',
  //     ON_OFF_PROCESS: 'ON_OFF_PROCESS', //上下线申请
  //     /** 日常通用申请 */
  //     COMMON_APPROVAL: 'COMMON_APPROVAL',
  //     EX_WAREHOUSE: 'EX_WAREHOUSE', //出库
  //     ASSET_CHANGE: 'ASSET_CHANGE', //固定资产编辑申请
  //     SALES_QUOTATION: 'SALES_QUOTATION', //销售报价
  //     PF_KPI: 'PF_KPI',
  //     POWER_END: 'POWER_END',
  //     RISK_REGISTER_EVAL: 'RISK_REGISTER_EVAL', // 风险单评估
  //     RISK_REGISTER_CLOSE: 'RISK_REGISTER_CLOSE', // 风险单关闭
  //     CUS_EFFECTED_APPLY: 'CUS_EFFECTED_APPLY', // 客户白名单申请
  //     CUS_FORBIDDEN_APPLY: 'CUS_FORBIDDEN_APPLY', // 客户白名单禁用
  //     OUT_TO_REGULAR: 'OUT_TO_REGULAR', //外包转正申请
  //     BILL_FEE_ADD: 'BILL_FEE_ADD', // 新增费用申请
  //     CUST_QUOTATION_ADD: 'CUST_QUOTATION_ADD',
  //     CUST_QUOTATION_UPD: 'CUST_QUOTATION_UPD',
  //     EME_PROCESS: 'EME_PROCESS',
  //   };
  //   const specialApprovalTypes: { [key in SpecialApprovalTypes]?: string } = {};
  //   if (isYgOffline) {
  //     specialApprovalTypes['CHANGE_SUMMERY'] = 'CHANGE_SUMMERY';
  //   }
  //   return { ...basicApprovalTypes, ...specialApprovalTypes } as const;
  // }, [isYgOffline]);

  // const approvalTypeTextKeyMap = useMemo(() => {
  //   const basicApprovalTypeTextKeyMap = {
  //     [approvalTypes.LEAVE_PROCESS]: '请假申请',
  //     [approvalTypes.ROLLBACK_ALTER]: '销假申请',
  //     [approvalTypes.REST_PROCESS]: '顶班申请',
  //     [approvalTypes.EXCHANGE_PROCESS]: '换班申请',
  //     [approvalTypes.OVERTIME_PROCESS]: '加班申请',
  //     [approvalTypes.OUTWORK_PROCESS]: '外勤申请',
  //     [approvalTypes.IN_DOOR]: '入门申请',
  //     [approvalTypes.OUT_DOOR]: '出门申请',
  //     [approvalTypes.CHANGE]: '变更申请',
  //     [approvalTypes.CHANGE_TEMPLATE]: '变更模板申请',
  //     [approvalTypes.VISITOR]: '人员入室申请',
  //     [approvalTypes.SUPPLY_CHECK_PROCESS]: '补卡申请',
  //     [approvalTypes.ORDER_APPROVAL]: '合同复核审批',
  //     [approvalTypes.OBJECTION]: '账单异议申请',
  //     [approvalTypes.WARRANTY]: '资产维保申请',
  //     [approvalTypes.BILL_ADJUST]: '调整申请',
  //     [approvalTypes.BORROW]: '借用归还申请',
  //     [approvalTypes.RENEW]: '续借申请',
  //     [approvalTypes.TRANSFER]: '转借申请',
  //     [approvalTypes.CARD_APPLY]: '门禁卡申请',
  //     [approvalTypes.CARD_CHANGE]: '门禁卡变更申请',
  //     [approvalTypes.CARD_OFF]: '门禁卡注销申请',
  //     [approvalTypes.POWER]: '上下电申请',
  //     [approvalTypes.AUTH_APPLY]: '权限申请',
  //     [approvalTypes.CONSTRUCTION_APPLY]: '施工作业申请',
  //     [approvalTypes.REPAIR_END]: '维修关单申请',
  //     [approvalTypes.EVENT_AUDIT_PROCESS]: '事件评审',
  //     [approvalTypes.INSPECT_END]: '巡检关单申请',
  //     [approvalTypes.ON_OFF_PROCESS]: '上下线申请',
  //     [approvalTypes.COMMON_APPROVAL]: '日常通用申请',
  //     [approvalTypes.EX_WAREHOUSE]: '出库申请',
  //     [approvalTypes.ASSET_CHANGE]: '固定资产编辑申请',
  //     [approvalTypes.SALES_QUOTATION]: '销售报价申请',
  //     [approvalTypes.PF_KPI]: '绩效定制指标申请',
  //     [approvalTypes.POWER_END]: '上下电关单申请',
  //     [approvalTypes.RISK_REGISTER_EVAL]: '风险单评估',
  //     [approvalTypes.RISK_REGISTER_CLOSE]: '风险单关闭',
  //     [approvalTypes.CUS_EFFECTED_APPLY]: '客户白名单申请',
  //     [approvalTypes.CUS_FORBIDDEN_APPLY]: '客户白名单禁用',
  //     [approvalTypes.OUT_TO_REGULAR]: '外包转正申请',
  //     [approvalTypes.BILL_FEE_ADD]: '新增费用申请',
  //     [approvalTypes.CUST_QUOTATION_ADD]: '友商项目报价新增申请',
  //     [approvalTypes.CUST_QUOTATION_UPD]: '友商项目报价变更申请',
  //     [approvalTypes.EME_PROCESS]: '演练复盘',
  //     [approvalTypes.CHANGE_SUMMERY]: '变更总结',
  //   };
  //   if (isYgOffline && approvalTypes.CHANGE_SUMMERY) {
  //     basicApprovalTypeTextKeyMap[approvalTypes.CHANGE_SUMMERY] = '变更总结';
  //   }
  //   return basicApprovalTypeTextKeyMap;
  // }, [
  //   approvalTypes.ASSET_CHANGE,
  //   approvalTypes.AUTH_APPLY,
  //   approvalTypes.BILL_ADJUST,
  //   approvalTypes.BILL_FEE_ADD,
  //   approvalTypes.BORROW,
  //   approvalTypes.CARD_APPLY,
  //   approvalTypes.CARD_CHANGE,
  //   approvalTypes.CARD_OFF,
  //   approvalTypes.CHANGE,
  //   approvalTypes.CHANGE_SUMMERY,
  //   approvalTypes.CHANGE_TEMPLATE,
  //   approvalTypes.COMMON_APPROVAL,
  //   approvalTypes.CONSTRUCTION_APPLY,
  //   approvalTypes.CUST_QUOTATION_ADD,
  //   approvalTypes.CUST_QUOTATION_UPD,
  //   approvalTypes.CUS_EFFECTED_APPLY,
  //   approvalTypes.CUS_FORBIDDEN_APPLY,
  //   approvalTypes.EME_PROCESS,
  //   approvalTypes.EVENT_AUDIT_PROCESS,
  //   approvalTypes.EXCHANGE_PROCESS,
  //   approvalTypes.EX_WAREHOUSE,
  //   approvalTypes.INSPECT_END,
  //   approvalTypes.IN_DOOR,
  //   approvalTypes.LEAVE_PROCESS,
  //   approvalTypes.OBJECTION,
  //   approvalTypes.ON_OFF_PROCESS,
  //   approvalTypes.ORDER_APPROVAL,
  //   approvalTypes.OUTWORK_PROCESS,
  //   approvalTypes.OUT_DOOR,
  //   approvalTypes.OUT_TO_REGULAR,
  //   approvalTypes.OVERTIME_PROCESS,
  //   approvalTypes.PF_KPI,
  //   approvalTypes.POWER,
  //   approvalTypes.POWER_END,
  //   approvalTypes.RENEW,
  //   approvalTypes.REPAIR_END,
  //   approvalTypes.REST_PROCESS,
  //   approvalTypes.RISK_REGISTER_CLOSE,
  //   approvalTypes.RISK_REGISTER_EVAL,
  //   approvalTypes.ROLLBACK_ALTER,
  //   approvalTypes.SALES_QUOTATION,
  //   approvalTypes.SUPPLY_CHECK_PROCESS,
  //   approvalTypes.TRANSFER,
  //   approvalTypes.VISITOR,
  //   approvalTypes.WARRANTY,
  //   isYgOffline,
  // ]);

  return { approvalTypes, approvalTypeTextKeyMap };
}
