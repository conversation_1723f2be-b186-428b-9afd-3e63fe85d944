/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "gql/client/approval": {
        "name": "gql/client/approval",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "gql/client/approval",
        "nextVersion": {
            "version": "patch",
            "message": "init v@1.0.0",
            "username": "chenlifeng",
            "email": "<EMAIL>"
        },
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "gql/client/bpm-detail": {
        "name": "gql/client/bpm-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "gql/client/bpm-detail",
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "hook/use-approval-types": {
        "name": "hook/use-approval-types",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "hook/use-approval-types",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "model/bpm-instance": {
        "name": "model/bpm-instance",
        "scope": "manyun.bpm",
        "version": "1.0.1",
        "mainFile": "index.ts",
        "rootDir": "model/bpm-instance",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "page/auth-request-creator": {
        "name": "page/auth-request-creator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "page/auth-request-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/bpm-editor": {
        "name": "page/bpm-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "page/bpm-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/common-request-creator": {
        "name": "page/common-request-creator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "page/common-request-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/construction-request": {
        "name": "page/construction-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "page/construction-request",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/out-to-regular-request": {
        "name": "page/out-to-regular-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "page/out-to-regular-request",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/request-entry": {
        "name": "page/request-entry",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "page/request-entry",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/bpm-routes": {
        "name": "route/bpm-routes",
        "scope": "manyun.bpm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "route/bpm-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/approval-batch-operate": {
        "name": "service/approval-batch-operate",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/approval-batch-operate",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-bpm-instance": {
        "name": "service/create-bpm-instance",
        "scope": "manyun.bpm",
        "version": "1.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/create-bpm-instance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-comment": {
        "name": "service/delete-comment",
        "scope": "manyun.bpm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/delete-comment",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/edit-scenes": {
        "name": "service/edit-scenes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/edit-scenes",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-access-card-list": {
        "name": "service/fetch-access-card-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-access-card-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-bpm": {
        "name": "service/fetch-bpm",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-bpm",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-bpm-instance": {
        "name": "service/fetch-bpm-instance",
        "scope": "manyun.bpm",
        "version": "0.0.21",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-bpm-instance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-bpm-refused-records": {
        "name": "service/fetch-bpm-refused-records",
        "scope": "manyun.bpm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-bpm-refused-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-carbon-copy-list": {
        "name": "service/fetch-carbon-copy-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-carbon-copy-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-cc-to-me-todos": {
        "name": "service/fetch-cc-to-me-todos",
        "scope": "manyun.bpm",
        "version": "0.0.5",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-cc-to-me-todos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-documentation-list": {
        "name": "service/fetch-documentation-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-documentation-list",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.21": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/fetch-event-audit-reject": {
        "name": "service/fetch-event-audit-reject",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-event-audit-reject",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.22": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/fetch-my-requested-todos": {
        "name": "service/fetch-my-requested-todos",
        "scope": "manyun.bpm",
        "version": "0.0.5",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-my-requested-todos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-my-resolved-todos": {
        "name": "service/fetch-my-resolved-todos",
        "scope": "manyun.bpm",
        "version": "0.0.5",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-my-resolved-todos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-my-unresolved-todos": {
        "name": "service/fetch-my-unresolved-todos",
        "scope": "manyun.bpm",
        "version": "0.0.5",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-my-unresolved-todos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-properties-query": {
        "name": "service/fetch-properties-query",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-properties-query",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.21": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/fetch-scenes": {
        "name": "service/fetch-scenes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-scenes",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-bpm": {
        "name": "service/mutate-bpm",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-bpm",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/refer-todo-to-others": {
        "name": "service/refer-todo-to-others",
        "scope": "manyun.bpm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/refer-todo-to-others",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/resolve-todo": {
        "name": "service/resolve-todo",
        "scope": "manyun.bpm",
        "version": "0.0.5",
        "mainFile": "index.ts",
        "rootDir": "service/resolve-todo",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/withdraw-todo": {
        "name": "service/withdraw-todo",
        "scope": "manyun.bpm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/withdraw-todo",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/approval-center": {
        "name": "state/approval-center",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "state/approval-center",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/auth-request": {
        "name": "state/auth-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "state/auth-request",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "ui/approval-operation-buttons": {
        "name": "ui/approval-operation-buttons",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/approval-operation-buttons",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/approval-records-dropdown": {
        "name": "ui/approval-records-dropdown",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/approval-records-dropdown",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/approval-status-text": {
        "name": "ui/approval-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/approval-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/approval-step-card": {
        "name": "ui/approval-step-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/approval-step-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/approval-type-select": {
        "name": "ui/approval-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/approval-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/approve-modal-button": {
        "name": "ui/approve-modal-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/approve-modal-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/bpm-instance-form": {
        "name": "ui/bpm-instance-form",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/bpm-instance-form",
        "config": {
            "bitdev.react/react-env@2.0.27": {},
            "teambit.envs/envs": {
                "env": "bitdev.react/react-env"
            }
        }
    },
    "ui/bpm-instance-viewer": {
        "name": "ui/bpm-instance-viewer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/bpm-instance-viewer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/bpm-viewer": {
        "name": "ui/bpm-viewer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/bpm-viewer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/business-customization-approval-content": {
        "name": "ui/business-customization-approval-content",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/business-customization-approval-content",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/carbon-copy-search-form": {
        "name": "ui/carbon-copy-search-form",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/carbon-copy-search-form",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/comment-modal-button": {
        "name": "ui/comment-modal-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/comment-modal-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/construction-users": {
        "name": "ui/construction-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/construction-users",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/corresponding-order-link": {
        "name": "ui/corresponding-order-link",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/corresponding-order-link",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/custom-approval-types-select": {
        "name": "ui/custom-approval-types-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/custom-approval-types-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/data-table-cc": {
        "name": "ui/data-table-cc",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/data-table-cc",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/director-select": {
        "name": "ui/director-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/director-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/manual-carbon-copy-modal": {
        "name": "ui/manual-carbon-copy-modal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/manual-carbon-copy-modal",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/refused-approval-table": {
        "name": "ui/refused-approval-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/refused-approval-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/request-entry-card": {
        "name": "ui/request-entry-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/request-entry-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/variable-select": {
        "name": "ui/variable-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.bpm",
        "mainFile": "index.ts",
        "rootDir": "ui/variable-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "$schema-version": "16.0.0"
}