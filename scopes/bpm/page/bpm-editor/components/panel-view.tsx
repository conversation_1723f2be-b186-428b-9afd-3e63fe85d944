import { EditOutlined, SaveOutlined } from '@ant-design/icons';
import moment from 'moment';
import React, { useEffect, useMemo, useRef, useState } from 'react';

//@ts-ignore old service
import { fetchUsersByIdsWeb } from '@manyun/auth-hub.service.pm.fetch-users-by-ids';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { NodeType, Sequence, TaskPeople } from '@manyun/bpm.model.bpm-instance';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { fetchDocumentationList } from '@manyun/bpm.service.fetch-documentation-list';

import styles from '../bpm-editor.module.less';
import { CcPanel } from './cc-panel';
import { NUMBER_RESOURCE } from './constants';
import { NodeUser, OrSignedPanel } from './or-signed-panel.js';
import { SequencePanel } from './sequence-panel.js';

export type PanelViewProps = {
  /** 需要.d.ts文件 */
  modeler: any;
  /** 需要.d.ts文件 */
  element: any;
  handleProcessName: (value: string) => void;
  setVisible: (value: boolean) => void;
  visible: boolean;
  processName: string;
  setProcessName: (name: string) => void;
  isProcessNameSaved: boolean;
  setIsProcessNameSaved: (param: boolean) => void;
  historyParams: Record<string, string>;
};

export function PanelView({
  modeler,
  element,
  handleProcessName,
  setVisible,
  visible,
  processName,
  setProcessName,
  isProcessNameSaved,
  setIsProcessNameSaved,
  historyParams,
}: PanelViewProps) {
  const [form] = Form.useForm();
  const formRef = useRef(null);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [name, setName] = useState('');
  const [nodeType, setNodeType] = useState<NodeType>(NodeType.Process);
  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const [operationType, setOperationType] = useState(false);
  const [nodeCodeList, setNodeCodeList] = useState<Record<string, any>[]>([]);
  const [resource, setResource] = useState<Record<string, any>[]>([]);

  useEffect(() => {
    fetchDocumentationList({
      bizScenes: historyParams?.bizScenes,
      subBizScenes: historyParams?.subBizScenes,
    }).then(({ data }: Record<string, any>) => {
      setNodeCodeList(
        data?.data?.map((item: Record<string, any>) => ({
          label: item?.nodeName,
          value: item?.node,
        })) || []
      );
    });
  }, [historyParams?.bizScenes, historyParams?.subBizScenes]);

  useEffect(() => {
    const fetchUsers = async (list: string[], isCustom?: boolean) => {
      if (list && list.length) {
        const { data } = await fetchUsersByIdsWeb({ userIds: list });
        const result = data.data.map((item: { id: number; name: string }) => ({
          key: item.id,
          label: item.name,
          value: item.id,
        }));
        isCustom
          ? form.setFieldsValue({
              recommendCandidateUsers: result,
              recommendNeeded: 'needed',
            })
          : form.setFieldsValue({
              candidateUsers: result,
            });
      }
    };
    const fetchCcUsers = async (list: string[]) => {
      if (list && list.length) {
        const { data } = await fetchUsersByIdsWeb({ userIds: list });
        const ccPeoplesFormData = form.getFieldValue(['ccPeoples']);
        ccPeoplesFormData.forEach(
          (element: {
            type: string;
            codeList: { key: number; label: string; value: number }[];
          }) => {
            if (element.type === TaskPeople.Assignee) {
              element.codeList = data.data.map((item: { id: string; name: string }) => ({
                key: item.id,
                label: item.name,
                value: item.id,
              }));
            }
          }
        );
        form.setFieldsValue({
          ccPeoples: ccPeoplesFormData,
        });
      }
    };

    const currentListeners = element?.businessObject?.extensionElements?.values
      ?.map((item: Record<string, any>) => {
        const val = item?.delegateExpression?.replace(/\$\{|\}/g, '') || '';
        if (val === 'systemTaskStartListener' || val === 'systemTaskCompleteListener') {
          return val;
        }

        return '';
      })
      .filter((_: any) => _);

    //element?.businessObject?.documentation里面放着节点code，审批人和提交人是同一个
    const businessObj = element?.businessObject?.documentation?.[0];

    let nodeCode, samePersonHandleType;

    if (businessObj) {
      const businessStr = businessObj?.text;

      //处理老数据
      if (!businessStr?.includes('{')) {
        nodeCode = businessStr;
      } else {
        const obj = JSON.parse(businessStr);
        nodeCode = obj?.nodeCode;
        samePersonHandleType = obj?.samePersonHandleType;
      }
    }

    form.setFieldsValue({
      name: element?.businessObject?.name,
      documentation: nodeCode,
      samePersonHandleType: samePersonHandleType,
      taskListener:
        historyParams?.bizFlowType === 'SERVICE'
          ? ['systemTaskStartListener', 'systemTaskCompleteListener']
          : currentListeners,
    });

    setName(element?.businessObject?.name);
    // 确定传入的element类型，渲染不同的属性面板
    if (formRef.current) {
      switch (element?.businessObject.$type) {
        case 'bpmn:ServiceTask': {
          setNodeType(NodeType.CcTask);
          initializeCcPeopleData(fetchCcUsers, element);
          break;
        }
        case 'bpmn:UserTask':
          setNodeType(NodeType.UserTask);
          initializeUserTaskData(fetchUsers, element);
          break;
        case 'bpmn:SequenceFlow':
          setNodeType(NodeType.Sequence);
          let elementSourceRef = element.businessObject.sourceRef;

          form.setFieldValue('optionType', 'manual');
          setOperationType(true);

          if (
            elementSourceRef &&
            elementSourceRef.default &&
            elementSourceRef.default.id === element.id
          ) {
            form.setFieldsValue({ sequenceType: Sequence.Default });
            form.setFieldsValue({ expression: undefined });
          } else if (!element.businessObject.conditionExpression) {
            form.setFieldsValue({ sequenceType: Sequence.Normal });
            form.setFieldsValue({ expression: undefined });
          } else {
            form.setFieldsValue({ sequenceType: Sequence.Conditional });
            const expression = element.businessObject.conditionExpression?.body;
            form.setFieldsValue({ expression: expression });
          }

          break;
        case 'bpmn:Process':
          handleProcessName(element.businessObject.name);
          setNodeType(NodeType.Process);
          break;
        case 'bpmn:StartEvent':
          setNodeType(NodeType.StartNode);

          break;
        case 'bpmn:EndEvent':
          setNodeType(NodeType.EndNode);

          break;
        case 'bpmn:ExclusiveGateway':
          setNodeType(NodeType.GatewayNode);

          break;
        default:
          setNodeType(NodeType.Default);

          break;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [element, form]);

  const initializeCcPeopleData = (fetchUsers: (list: string[]) => void, element: any) => {
    const ccExpression = element?.businessObject.expression;
    if (ccExpression) {
      const { ccPeopleType, ccPeopleInfoData } = parseCcNodeExpressionInfo(ccExpression);
      const ccPeopleInfoJSONData = JSON.parse(ccPeopleInfoData);
      let ccAssigneeList: never[] = [];
      ccPeopleInfoJSONData.forEach((element: { type: string; codeList: never[] }) => {
        if (element.type === TaskPeople.Assignee) {
          ccAssigneeList = element.codeList;
        }
      });
      if (ccPeopleInfoData) {
        form.setFieldsValue({ ccPeoples: ccPeopleInfoJSONData });
        fetchUsers(ccAssigneeList);
      }
      form.setFieldsValue({
        ccApprovalType: ccPeopleType?.includes('PASS') ? 'APPROVAL_PASS_CC' : 'APPROVAL_CC',
      });
    } else {
      form.setFieldsValue({ ccPeoples: [] });
      form.setFieldsValue({ ccApprovalType: null });
    }
  };

  const initializeUserTaskData = (
    fetchUsers: (list: string[], isCustom?: boolean) => void,
    element: any
  ) => {
    form.setFieldsValue({ shiftUser: null });
    // 会签
    if (
      element?.businessObject?.loopCharacteristics !== null &&
      element?.businessObject?.loopCharacteristics !== undefined
    ) {
      form.setFieldsValue({ isCounterSignedNode: 'COUNTER_SIGN' });
      // eslint-disable-next-line no-template-curly-in-string
      if (element?.businessObject.assignee === '${assignee}') {
        initializeCounterSignedAssigneeNode(fetchUsers, element);
      } else {
        initializeCounterSignedGroupNode(element);
      }
    }
    // 或签
    else if (
      ((element?.businessObject?.loopCharacteristics === undefined ||
        element?.businessObject?.loopCharacteristics === null) &&
        element?.businessObject?.candidateUsers) ||
      element?.businessObject?.assignee ||
      element?.businessObject?.candidateGroups
    ) {
      form.setFieldsValue({ isCounterSignedNode: 'OR_SIGN', recommendNeeded: 'unneeded' });
      if (element?.businessObject.assignee) {
        initializeOrSignedAssigneeNode(fetchUsers, element);
      } else if (element?.businessObject.candidateUsers) {
        initializeOrSignedUsersNode(fetchUsers, element);
      } else if (element?.businessObject.candidateGroups) {
        initializeOrSignedGroupNode(fetchUsers, element);
      }
    } else if (
      !element?.businessObject?.candidateUsers &&
      !element?.businessObject?.assignee &&
      !element?.businessObject?.candidateGroups
    ) {
      initializeOthers();
    }
  };
  const initializeCounterSignedAssigneeNode = (
    fetchUsers: (list: string[]) => void,
    element: any
  ) => {
    // 用户
    const { approverType, approver } = getCounterSignedUserTaskApprover(
      element?.businessObject.loopCharacteristics.collection
    );

    form.setFieldsValue({ taskPeopleType: approverType });

    if (approverType === TaskPeople.Assignee) {
      if (approver?.length > 1) {
        fetchUsers(approver.split(','));
      } else {
        fetchUsers([approver]);
      }
    } else if (approverType === TaskPeople.Variable) {
      form.setFieldsValue({ shiftUser: approver });
    } else if (approverType === TaskPeople.Supervisor) {
      form.setFieldsValue({ director: approver });
    }

    form.setFieldsValue({ candidateGroups: undefined, recommendCandidateGroups: undefined });
  };
  const initializeCounterSignedGroupNode = (element: any) => {
    const { approver } = getCounterSignedUserTaskApprover(
      element?.businessObject.loopCharacteristics.collection
    );
    // 角色
    form.setFieldsValue({
      candidateGroups: approver.includes(',') ? approver.split(',') : approver,
    });

    form.setFieldsValue({ taskPeopleType: TaskPeople.Groups });

    form.setFieldsValue({ candidateUsers: [], recommendCandidateUsers: [] });
  };
  const initializeOrSignedAssigneeNode = (fetchUsers: (list: string[]) => void, element: any) => {
    //用户
    const { approverType, approver } = getOrSignedUserTaskApprover(
      element?.businessObject.assignee
    );
    form.setFieldsValue({ taskPeopleType: approverType });

    if (approverType === TaskPeople.Assignee) {
      fetchUsers([approver]);
      form.setFieldsValue({ candidateGroups: undefined, recommendCandidateGroups: undefined });
    }
  };
  const initializeOrSignedUsersNode = (
    fetchUsers: (list: string[], isCustom?: boolean) => void,
    element: any
  ) => {
    const { approverType, approver, isCustom } = getOrSignedUserTaskApprover(
      element?.businessObject.candidateUsers
    );
    form.setFieldsValue({ taskPeopleType: approverType });

    if (approverType === TaskPeople.Assignee) {
      form.setFieldsValue({ candidateGroups: undefined, recommendCandidateGroups: undefined });
      if (isCustom) {
        const { notRecommendList, recommendList } = JSON.parse(approver);
        fetchUsers(recommendList, isCustom);
        fetchUsers(notRecommendList);
      } else {
        fetchUsers(approver.split(','));
      }
    } else if (approverType === TaskPeople.Supervisor) {
      form.setFieldsValue({ candidateGroups: undefined, recommendCandidateGroups: undefined });
      form.setFieldsValue({ director: approver });
    } else {
      form.setFieldsValue({ candidateGroups: undefined, recommendCandidateGroups: undefined });
      form.setFieldsValue({ shiftUser: approver });
    }
  };
  const initializeOrSignedGroupNode = (fetchUsers: (list: string[]) => void, element: any) => {
    // 角色
    const { approverType, approver, isCustom } = getOrSignedUserTaskApprover(
      element?.businessObject.candidateGroups
    );
    form.setFieldsValue({ taskPeopleType: approverType });

    if (isCustom) {
      const { notRecommendList, recommendList } = JSON.parse(approver);
      form.setFieldsValue({
        candidateGroups: notRecommendList,
        recommendCandidateGroups: recommendList,
        recommendNeeded: 'needed',
      });
    } else {
      form.setFieldsValue({
        candidateGroups: approver.includes(',') ? approver.split(',') : approver,
      });
    }

    form.setFieldsValue({ candidateUsers: [], recommendCandidateUsers: [] });
  };
  const initializeOthers = () => {
    form.setFieldsValue({ isCounterSignedNode: null, recommendNeeded: 'unneeded' });
    form.setFieldsValue({ candidateUsers: [], recommendCandidateUsers: [] });
    form.setFieldsValue({ candidateGroups: undefined, recommendCandidateGroups: undefined });
    form.setFieldsValue({ taskPeopleType: null });
    form.setFieldsValue({ shiftUser: null });
  };

  const getCounterSignedUserTaskApprover = (approverExpression: string) => {
    // eslint-disable-next-line
    const approverRegex = /(\'|\")(.+?)(\'|\")/gm;
    let matches;
    let phrases: string[] = [];
    while ((matches = approverRegex.exec(approverExpression)) !== null) {
      // 这是避免零宽度匹配的无限循环所必需的
      if (matches.index === approverRegex.lastIndex) {
        approverRegex.lastIndex++;
      }
      matches.forEach(match => {
        if (isPhrase(match)) {
          phrases.push(match);
        }
      });
    }

    const [approverType, platformType, approver] = phrases;
    return { approverType, platformType, approver };
  };

  const getOrSignedUserTaskApprover = (approverExpression: string) => {
    const isCustom = approverExpression.includes('CUSTOM');
    if (isCustom) {
      const [, approverType, , groups] = approverExpression.split(",'");
      return {
        approverType: approverType.replace(/'/, ''),
        approver: groups.replace("')}", ''),
        isCustom,
      };
    }
    // eslint-disable-next-line no-useless-escape
    const approverRegex = /(\'|\")(.+?)(\'|\")/gm;
    let matches;
    let phrases: string[] = [];
    while ((matches = approverRegex.exec(approverExpression)) !== null) {
      if (matches.index === approverRegex.lastIndex) {
        approverRegex.lastIndex++;
      }
      matches.forEach(match => {
        if (isPhrase(match)) {
          phrases.push(match);
        }
      });
    }
    const [approverType, platformType, approver] = phrases;
    return { approverType, platformType, approver, isCustom };
  };

  const parseCcNodeExpressionInfo = (ccNodeExpression: string) => {
    // eslint-disable-next-line no-useless-escape
    const nodeParamsRegex = /(?<=\()\S+(?=\))/gm;
    let matches;
    let phrases: string[] = [];
    while ((matches = nodeParamsRegex.exec(ccNodeExpression)) !== null) {
      if (matches.index === nodeParamsRegex.lastIndex) {
        nodeParamsRegex.lastIndex++;
      }
      matches.forEach(match => {
        phrases.push(match);
      });
    }

    const [ccParams] = phrases;

    // eslint-disable-next-line no-useless-escape
    const nodePeopleInfoRegex = /(?<=\'\,\')\S+(?=\')/gm;
    let peopleInfomatches;
    let peopleInfophrases: string[] = [];
    while ((peopleInfomatches = nodePeopleInfoRegex.exec(ccNodeExpression)) !== null) {
      if (peopleInfomatches.index === nodePeopleInfoRegex.lastIndex) {
        nodePeopleInfoRegex.lastIndex++;
      }
      peopleInfomatches.forEach(match => {
        if (isPhrase(match)) {
          peopleInfophrases.push(match);
        }
      });
    }

    const [ccPeopleInfoData] = peopleInfophrases;
    const [, ccPeopleType] = ccParams.split(',');
    return { ccPeopleType, ccPeopleInfoData };
  };

  const isPhrase = (match: string) => {
    return match?.length >= 1 && !match.includes("'");
  };

  const generateCounterSignedApproverExpression = (taskPeopleType: string, value: string) => {
    return `\${countersignService.queryTaskUsersOrGroups(execution,'${taskPeopleType}','DC_BRAIN','${value}')}`;
  };

  const generateOrSignedApproverExpression = (taskPeopleType: string, value: string) => {
    return `\${taskUserService.queryTaskUsersOrGroups(execution,'${taskPeopleType}','DC_BRAIN','${value}')}`;
  };

  const generateOrSignedRecommendApproverExpression = (taskPeopleType: string, value: string) => {
    return `\${taskUserService.queryTaskUsersOrGroups(execution,'${taskPeopleType}','CUSTOM','${value}')}`;
  };

  const generateCcPeopleExpression = (ccApprovalType: string, value: string) => {
    return `\${processCcService.processCc(execution,'${ccApprovalType}','${value}')}`;
  };

  /**
   *
   * @description 调用bpm.modeling中的update方法，更新节点属性
   */
  const updateProperties = (properties: any) => {
    const modeling = modeler.get('modeling');
    modeling.updateProperties(element, properties);
  };
  /**
   *
   * @description 将输入框属性变化进行格式化，作为之后modeling中update方法的参数
   */
  const changeProperties = (event: any, type: string) => {
    const value = event.target.value;
    let properties = {} as any;
    properties[type] = value;
    element[type] = value;
    updateProperties(properties);
  };
  /**
   *
   * @description 将选择框属性变化进行格式化，作为之后modeling中update方法的参数
   */
  const changeSelectProperties = (value: string | null, type: string) => {
    let properties = {} as any;
    properties[type] = value;
    element[type] = value;
    updateProperties(properties);
  };

  const updateFlowType = (flowType: Sequence, element: any) => {
    let elementSource = element.source;
    let elementSourceRef = element.businessObject.sourceRef;

    // 正常条件类
    if (flowType === Sequence.Conditional) {
      return;
    }
    // 默认路径
    if (flowType === Sequence.Default) {
      modeler.get('modeling').updateProperties(element, {
        conditionExpression: null,
      });
      modeler.get('modeling').updateProperties(elementSource, {
        default: element,
      });
      return;
    }
    // 正常路径，如果来源节点的默认路径是当前连线时，清除父元素的默认路径配置
    if (elementSourceRef?.default && elementSourceRef.default?.id === element?.id) {
      modeler.get('modeling').updateProperties(elementSource, {
        default: null,
      });
    }
    modeler.get('modeling').updateProperties(element, {
      conditionExpression: null,
    });
  };
  /**
   *
   * @description 更新会签节点的属性
   */
  const updateLoopCompletionCondition = (
    element: any,
    taskPeopleType: TaskPeople,
    value: string
  ) => {
    let completionCondition = null;
    completionCondition = modeler
      .get('moddle') // eslint-disable-next-line no-template-curly-in-string
      .create('bpmn:FormalExpression', { body: "${countersignTaskEndTag == 'FINISH'}" });

    modeler
      .get('modeling')
      .updateModdleProperties(element, element.businessObject.loopCharacteristics, {
        completionCondition,
        collection: generateCounterSignedApproverExpression(taskPeopleType, value),
        elementVariable: taskPeopleType === TaskPeople.Groups ? 'group' : 'assignee',
        isSequential: false,
      });
  };

  const updateConditionalFlowExpression = (element: any, expression: string) => {
    modeler.get('modeling').updateProperties(element, {
      conditionExpression: modeler
        .get('moddle')
        .create('bpmn:FormalExpression', { body: expression }),
    });
  };

  const correctedValue = ({ value, components, valueType, resData }: Record<string, any>) => {
    //在处理表达式之前要对value进行矫正
    let currentVal = value;

    if (resData?.options && resData?.inputWay === 'COMPONENT' && value) {
      const optComponent = JSON.parse(resData.options);

      if (optComponent?.valueType === 'name' && typeof value === 'object' && value?.label) {
        currentVal = value?.label;
      }
    }

    if (components === 'date' && value && value !== 'null') {
      currentVal = moment(value).valueOf();
    }

    if (valueType === `CHARACTER` || !valueType) {
      if (Array.isArray(value)) {
        //假如是楼栋列表的时候要单独处理
        if (components === 'location') {
          currentVal = value[value.length - 1];
        } else {
          currentVal = value.join(',');
        }
      }

      currentVal = `'${currentVal}'`;
    }

    return currentVal;
  };

  const generateExpression = ({ condition, propertiesCode, value }: Record<string, any>) => {
    let result = '';

    //假如它是number或者string
    if (condition !== 'include' && condition !== 'notInclude') {
      const conditionSign = NUMBER_RESOURCE.find(res => res.value === condition)?.sign;

      result = `${propertiesCode}${conditionSign}${value}`;
    } else if (condition === 'include') {
      result = `${propertiesCode}.contains(${value})`;
    } else {
      result = `!${propertiesCode}.contains(${value})`;
    }

    return result;
  };

  const formateExpression = (formValue: Record<string, any>) => {
    let groupStr = '';
    const groupData = formValue?.group;

    if (!groupData) {
      return '';
    }

    groupData.forEach((item: Record<string, any>[], index: number) => {
      //且的关系
      let str = '';
      item.forEach((val: Record<string, any>, rowIndex: number) => {
        const propertiesCode = val?.propertiesCode;
        const condition = val?.condition;
        let value = val?.value === undefined ? 'null' : val?.value;
        const components = val?.components;
        const valueType = value === 'null' ? 'NUMBER' : val?.valueType; //数据类型:NUMBER : 数字、CHARACTER:文本、PROPERTIES:属性， BOOLEAN 布

        if (!propertiesCode || !condition || !value) {
          return;
        }
        const resData = resource.find(
          (item: Record<string, any>) => item?.propertiesCode === propertiesCode
        );

        if (resData && resData?.propertiesType === 'ARRAY') {
          //它里面是一个数组，并且要把值处理成并的关系
          let arrStr = '';
          value?.forEach((v: any, i: number) => {
            const currentValue = correctedValue({
              value: v,
              components,
              valueType,
              resData,
            });
            const currentStr = generateExpression({
              condition,
              propertiesCode,
              value: currentValue,
              str,
            });

            arrStr += `${currentStr}&&`;
          });

          arrStr = arrStr.slice(0, arrStr.lastIndexOf('&&'));
          str += `(${arrStr})&& `;
        } else {
          //矫正属性值
          value = correctedValue({
            value,
            components,
            valueType,
            resData,
          });
          //拼接字符串
          const currentStr = generateExpression({
            condition,
            propertiesCode,
            value,
            str,
          });

          str += `${currentStr}&&`;
        }
      });

      if (!str) {
        return;
      }

      //矫正str，如果str的结尾是 && 就应该删掉
      str = str.slice(0, str.lastIndexOf('&&'));
      groupStr += `${str}||`;
    });

    groupStr = groupStr.slice(0, groupStr.lastIndexOf('||'));
    return groupStr ? '${' + groupStr + '}' : '';
  };

  /**
   * @description 业务场景生成监听器
   */
  const createServiceListenerObject = (
    $type: string,
    event: string,
    delegateExpression: string
  ) => {
    const listenerObj = {
      event,
      delegateExpression,
    };

    return modeler.get('moddle').create($type, listenerObj);
  };

  const onFinish = () => {
    form.validateFields().then(val => {
      //删掉多余的字段
      if (val?.group) {
        const expression = formateExpression(val);
        val.expression = expression;
        delete val.group;

        // 把手工录入的表单值删掉，只留下原有的就好了
        for (let item in val) {
          if (item.includes('manual.')) {
            delete val[item];
          }
        }
      }

      // name属性是任务节点、抄送节点、顺序流节点共有的，因此不需要做条件判断
      changeProperties({ target: { value: val.name } }, 'name');
      if (nodeType === NodeType.UserTask) {
        //处理审批人
        setUserTaskNode(
          val.isCounterSignedNode,
          val.taskPeopleType,
          val.shiftUser,
          val.director,
          val.candidateGroups,
          val.candidateUsers,
          val.recommendNeeded === 'needed',
          val.recommendCandidateGroups,
          val.recommendCandidateUsers
        );

        //在业务场景下需要将节点code和监听器的值加入进去nodeCode
        if (historyParams?.bizFlowType === 'SERVICE' || historyParams?.bizFlowType === 'MIX') {
          const obj = { nodeCode: val?.documentation };

          const documentation = modeler.get('moddle').create('bpmn:Documentation', {
            text: JSON.stringify(obj),
          });
          modeler.get('modeling').updateProperties(element, {
            documentation: [documentation],
          });
        }

        //审批人和提交人是一个人
        if (val.samePersonHandleType) {
          const documentationObj = element?.businessObject?.documentation?.[0].text || '{}'; //肯定只有一个documentation，且里main是一个对象
          const obj = JSON.parse(documentationObj);
          obj.samePersonHandleType = val.samePersonHandleType;

          const documentation = modeler.get('moddle').create('bpmn:Documentation', {
            text: JSON.stringify(obj),
          });

          //documentation有多个
          modeler.get('modeling').updateProperties(element, {
            documentation: [documentation],
          });
        }

        //监听
        if (
          (historyParams?.bizFlowType === 'MIX' || historyParams?.bizFlowType === 'SERVICE') &&
          element?.businessObject.$type === 'bpmn:UserTask'
        ) {
          //extensionElements 的类型是会变的，有时候是数组，有时候是对象
          let bpmnElementListenersArr = [];
          if (
            Object.prototype.toString.call(element?.businessObject?.extensionElements) ===
            '[object Object]'
          ) {
            bpmnElementListenersArr = element?.businessObject?.extensionElements?.values;
          }

          const startListener = bpmnElementListenersArr?.find(
            (ex: Record<string, any>) => ex?.delegateExpression === '${systemTaskStartListener}'
          );
          const completeListener = bpmnElementListenersArr?.find(
            (ex: Record<string, any>) => ex?.delegateExpression === '${systemTaskCompleteListener}'
          );

          //把已有的删除掉，初始化一下监听器，以免出现bug
          const otherListener =
            startListener || completeListener
              ? bpmnElementListenersArr.filter(
                  (ex: Record<string, any>) =>
                    ex?.delegateExpression !== '${systemTaskStartListener}' &&
                    ex?.delegateExpression !== '${systemTaskCompleteListener}'
                )
              : [];

          if (
            val.taskListener &&
            val.taskListener?.find((item: string) => item === 'systemTaskStartListener')
          ) {
            otherListener.push(
              createServiceListenerObject(
                'camunda:TaskListener',
                'create',
                '${systemTaskStartListener}'
              )
            );
          }

          if (
            val.taskListener &&
            val.taskListener?.find((item: string) => item === 'systemTaskCompleteListener')
          ) {
            otherListener.push(
              createServiceListenerObject(
                'camunda:ExecutionListener',
                'end',
                '${systemTaskCompleteListener}'
              )
            );
          }

          const extensions = modeler.get('moddle').create('bpmn:ExtensionElements', {
            values: otherListener,
          });

          modeler.get('modeling').updateProperties(element, {
            extensionElements: extensions,
          });
        }
      } else if (nodeType === NodeType.Sequence) {
        updateFlowType(val.sequenceType, element);
        if (val.sequenceType === Sequence.Conditional) {
          updateConditionalFlowExpression(element, val.expression);
        }
        // eslint-disable-next-line no-template-curly-in-string
        if (val.expression === '${result == 0}') {
          changeProperties({ target: { value: '同意' } }, 'name');
        }
        // eslint-disable-next-line no-template-curly-in-string
        else if (val.expression === '${result == 1}') {
          changeProperties({ target: { value: '拒绝' } }, 'name');
        }
      } else if (nodeType === NodeType.CcTask) {
        const ccApprovalPeoples = form.getFieldValue(['ccPeoples']);
        ccApprovalPeoples.forEach(
          (element: { type: string; codeList: ({ value: string } | string)[] }) => {
            if (element.type === TaskPeople.Assignee) {
              element.codeList = element.codeList.map(code => {
                if (typeof code === 'object') {
                  return code?.value;
                } else {
                  return code;
                }
              });
            }
          }
        );
        const ccApprovalType = form.getFieldValue(['ccApprovalType']);
        refactorCcNodeId(element, ccApprovalType);
        setCcPeopleNode(ccApprovalType, JSON.stringify(ccApprovalPeoples));
      }

      message.success('保存成功！');
      setVisible(true);
    });
  };

  const refactorCcNodeId = (element: any, ccApprovalType: string) => {
    const value = element.businessObject.id.split('_');

    let pre = ccApprovalType === 'APPROVAL_PASS_CC' ? ['CC_PASS'] : ['CC_APPROVAL'];
    if (value.length < 4) {
      pre = pre.concat(value);
      let newId = pre.join('_');

      modeler.get('modeling').updateProperties(element, {
        id: newId,
        di: { id: `${newId}_di` },
      });
    } else {
      let originId = [value[2], value[3]].join('_');
      pre = pre.concat(originId);
      let newId = pre.join('_');

      modeler.get('modeling').updateProperties(element, {
        id: newId,
        di: { id: `${newId}_di` },
      });
    }
  };

  const setCcPeopleNode = (ccApprovalType: string, ccApprovalPeoples: string) => {
    changeSelectProperties(
      generateCcPeopleExpression(ccApprovalType, ccApprovalPeoples),
      'expression'
    );
  };

  const setUserTaskNode = (
    isCounterSignedNode: string,
    taskPeopleType: TaskPeople,
    shiftUser: string,
    director: string,
    candidateGroups: string,
    candidateUsers: NodeUser[],
    recommendNeeded: boolean,
    recommendCandidateGroups: string,
    recommendCandidateUsers: NodeUser[]
  ) => {
    if (isCounterSignedNode === 'COUNTER_SIGN') {
      setCounterSignedProcessNode(
        element,
        taskPeopleType,
        candidateUsers,
        candidateGroups,
        shiftUser,
        director
      );
    } else {
      setOrSignedProcessNode(
        element,
        taskPeopleType,
        candidateUsers,
        candidateGroups,
        shiftUser,
        director,
        recommendNeeded,
        recommendCandidateGroups,
        recommendCandidateUsers
      );
    }
  };

  const setOrSignedProcessNode = (
    element: any,
    taskPeopleType: TaskPeople,
    candidateUsers: string | NodeUser[],
    candidateGroups: string,
    shiftUser: string,
    director: string,
    recommendNeeded: boolean,
    recommendCandidateGroups: string,
    recommendCandidateUsers: string | NodeUser[]
  ) => {
    if (element.businessObject.loopCharacteristics) {
      modeler.get('modeling').updateProperties(element, {
        loopCharacteristics: null,
      });
    }
    // 用户
    if (taskPeopleType === TaskPeople.Assignee) {
      setOrSignedAssigneeNode(
        candidateUsers,
        taskPeopleType,
        recommendNeeded,
        recommendCandidateUsers
      );
    }
    // 角色
    else if (taskPeopleType === TaskPeople.Groups) {
      setOrSignedCandidateGroupsNode(
        candidateGroups,
        taskPeopleType,
        recommendNeeded,
        recommendCandidateGroups
      );
    } else if (taskPeopleType === TaskPeople.Variable) {
      setOrSignedVariableNode(taskPeopleType, shiftUser);
    } else {
      setOrSignedDirectorNode(taskPeopleType, director);
    }
  };

  const setOrSignedAssigneeNode = (
    candidateUsers: string | NodeUser[],
    taskPeopleType: TaskPeople,
    recommendNeeded: boolean,
    recommendCandidateUsers: string | NodeUser[]
  ) => {
    let approvers = '';
    if (
      recommendNeeded &&
      Array.isArray(recommendCandidateUsers) &&
      Array.isArray(candidateUsers)
    ) {
      changeSelectProperties(
        generateOrSignedRecommendApproverExpression(
          taskPeopleType,
          recommendNeeded
            ? JSON.stringify({
                recommendList: recommendCandidateUsers.map(({ key }) => key),
                notRecommendList: candidateUsers.map(({ key }) => key),
              })
            : approvers
        ),
        'candidateUsers'
      );
      changeSelectProperties(null, 'assignee');
      changeSelectProperties(null, 'candidateGroups');
    } else if (typeof candidateUsers !== 'string' && candidateUsers.length > 1) {
      approvers = formatMultipleApproverExpression(candidateUsers);
      changeSelectProperties(
        generateOrSignedApproverExpression(taskPeopleType, approvers),
        'candidateUsers'
      );
      changeSelectProperties(null, 'assignee');
      changeSelectProperties(null, 'candidateGroups');
    } else if (typeof candidateUsers !== 'string' && candidateUsers.length === 1) {
      approvers = '' + candidateUsers[0].key;
      changeSelectProperties(
        generateOrSignedApproverExpression(taskPeopleType, approvers),
        'assignee'
      );
      changeSelectProperties(null, 'candidateUsers');
      changeSelectProperties(null, 'candidateGroups');
    } else {
      changeSelectProperties(null, 'candidateUsers');
      changeSelectProperties(null, 'assignee');
    }
  };

  const setOrSignedCandidateGroupsNode = (
    candidateGroups: string,
    taskPeopleType: TaskPeople,
    recommendNeeded: boolean,
    recommendCandidateGroups: string
  ) => {
    if (recommendNeeded) {
      changeSelectProperties(
        generateOrSignedRecommendApproverExpression(
          taskPeopleType,
          JSON.stringify({
            recommendList: recommendCandidateGroups,
            notRecommendList: candidateGroups,
          })
        ),
        'candidateGroups'
      );
    } else {
      changeSelectProperties(
        generateOrSignedApproverExpression(taskPeopleType, candidateGroups),
        'candidateGroups'
      );
    }
    changeSelectProperties(null, 'assignee');
    changeSelectProperties(null, 'candidateUsers');
  };

  const setOrSignedVariableNode = (taskPeopleType: TaskPeople, shiftUser: string) => {
    changeSelectProperties(
      generateOrSignedApproverExpression(taskPeopleType, shiftUser),
      'candidateUsers'
    );
    changeSelectProperties(null, 'assignee');
    changeSelectProperties(null, 'candidateGroups');
  };

  const setOrSignedDirectorNode = (taskPeopleType: string, director: string) => {
    changeSelectProperties(
      generateOrSignedApproverExpression(taskPeopleType, director),
      'candidateUsers'
    );
    changeSelectProperties(null, 'assignee');
    changeSelectProperties(null, 'candidateGroups');
  };

  const setCounterSignedProcessNode = (
    element: any,
    taskPeopleType: TaskPeople,
    candidateUsers: string | NodeUser[],
    candidateGroups: string,
    shiftUser: string,
    director: string
  ) => {
    const multiLoopInstance = modeler.get('moddle').create('bpmn:MultiInstanceLoopCharacteristics');
    modeler.get('modeling').updateProperties(element, {
      loopCharacteristics: multiLoopInstance,
    });

    if (taskPeopleType === TaskPeople.Assignee) {
      setCounterSignedAssigneeNode(element, candidateUsers, taskPeopleType);
    } else if (taskPeopleType === TaskPeople.Groups) {
      setCounterSignedCandidateGroupsNode(element, candidateGroups, taskPeopleType);
    } else if (taskPeopleType === TaskPeople.Variable) {
      setCounterSignedVariableNode(element, shiftUser, taskPeopleType);
    } else {
      setCounterSignedDirectorNode(element, director, taskPeopleType);
    }
  };

  const setCounterSignedAssigneeNode = (
    element: any,
    candidateUsers: string | NodeUser[],
    taskPeopleType: TaskPeople
  ) => {
    let approvers = '';

    if (typeof candidateUsers !== 'string' && candidateUsers.length > 1) {
      approvers = formatMultipleApproverExpression(candidateUsers);
      updateLoopCompletionCondition(element, taskPeopleType, approvers);
      // eslint-disable-next-line no-template-curly-in-string
      changeSelectProperties('${assignee}', 'assignee');
      changeSelectProperties(null, 'candidateUsers');
      changeSelectProperties(null, 'candidateGroups');
    } else if (typeof candidateUsers !== 'string' && candidateUsers.length === 1) {
      approvers = '' + candidateUsers[0].key;
      updateLoopCompletionCondition(element, taskPeopleType, approvers);
      // eslint-disable-next-line no-template-curly-in-string
      changeSelectProperties('${assignee}', 'assignee');
      changeSelectProperties(null, 'candidateUsers');
      changeSelectProperties(null, 'candidateGroups');
    } else {
      changeSelectProperties(null, 'candidateUsers');
      changeSelectProperties(null, 'assignee');
    }
  };

  const setCounterSignedCandidateGroupsNode = (
    element: any,
    candidateGroups: string,
    taskPeopleType: TaskPeople
  ) => {
    updateLoopCompletionCondition(element, taskPeopleType, candidateGroups);
    // eslint-disable-next-line no-template-curly-in-string
    changeSelectProperties('${group}', 'candidateGroups');
    changeSelectProperties(null, 'assignee');
    changeSelectProperties(null, 'candidateUsers');
  };

  const setCounterSignedVariableNode = (
    element: any,
    shiftUser: string,
    taskPeopleType: TaskPeople
  ) => {
    updateLoopCompletionCondition(element, taskPeopleType, shiftUser);
    // eslint-disable-next-line no-template-curly-in-string
    changeSelectProperties('${assignee}', 'assignee');
    changeSelectProperties(null, 'candidateUsers');
    changeSelectProperties(null, 'candidateGroups');
  };

  const setCounterSignedDirectorNode = (
    element: any,
    director: string,
    taskPeopleType: TaskPeople
  ) => {
    updateLoopCompletionCondition(element, taskPeopleType, director);
    // eslint-disable-next-line no-template-curly-in-string
    changeSelectProperties('${assignee}', 'assignee');
    changeSelectProperties(null, 'candidateUsers');
    changeSelectProperties(null, 'candidateGroups');
  };

  const formatMultipleApproverExpression = (approvers: NodeUser[]) => {
    let formattedExpression = '';
    for (let i = 0; i < approvers.length; i++) {
      if (approvers[i].key) {
        formattedExpression += `${approvers[i].key},`;
      }
    }
    return formattedExpression.substring(0, formattedExpression.length - 1);
  };
  const onClose = () => {
    setVisible(false);
  };
  const handleProcessNameClick = () => {
    if (processName === '') {
      message.error('流程名称不能为空');
    } else {
      setProcessName(processName);
      handleProcessName(processName);
      const allShapes = modeler.get('elementRegistry').getAll();
      const modeling = modeler.get('modeling');
      modeling.updateProperties(allShapes[0], { name: processName });
      setIsProcessNameSaved(true);
    }
  };
  const nodeName = locales.nodeType[nodeType];
  return (
    <>
      <Drawer
        forceRender
        title={nodeName}
        size="large"
        placement="right"
        onClose={onClose}
        open={visible}
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={onFinish}>
              保存
            </Button>
          </Space>
        }
      >
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 16 }} form={form} ref={formRef}>
          <div>
            <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
              {`${locales.setPrefix}${nodeName}${locales.title}`}
            </Typography.Title>
            <Form.Item
              label={`${nodeName}${locales.title}`}
              rules={[{ required: isTaskNode(nodeType), message: locales.titlePlaceholder }]}
              name="name"
            >
              <Input maxLength={12} style={{ width: '100%' }} />
            </Form.Item>
            {(historyParams?.bizFlowType === 'SERVICE' || historyParams?.bizFlowType === 'MIX') &&
              nodeType === NodeType.UserTask && (
                <Form.Item
                  label={locales.documentation}
                  rules={[
                    {
                      required: historyParams.bizFlowType === 'SERVICE',
                      message: locales.nodeCodePlaceholder,
                    },
                  ]}
                  name="documentation"
                  extra={
                    <div style={{ color: 'var(--manyun-error-color)' }}>
                      {locales.documentationExtra}
                    </div>
                  }
                >
                  <Select
                    placeholder={locales.nodeCodePlaceholder}
                    style={{ width: '100%' }}
                    options={nodeCodeList}
                    allowClear
                  ></Select>
                </Form.Item>
              )}
          </div>
          {nodeType === NodeType.UserTask && (
            <OrSignedPanel form={form} bizFlowType={historyParams?.bizFlowType} />
          )}
          {nodeType === NodeType.CcTask && <CcPanel form={form} />}
          {nodeType === NodeType.Sequence && isStartNode(element) && (
            <SequencePanel
              form={form}
              element={element}
              operationType={operationType}
              setOperationType={setOperationType}
              historyParams={historyParams}
              resource={resource}
              setResource={setResource}
              formateExpression={formateExpression}
            />
          )}
        </Form>
      </Drawer>
      <Card id={styles.processName}>
        <Space>
          {`${locales.nodeType.PROCESS}${locales.title}:`}
          {!isProcessNameSaved ? (
            <Space>
              <Input
                value={processName}
                onChange={e => {
                  setProcessName(e.target.value);
                }}
                style={{ width: '100%' }}
                maxLength={15}
              />
              <SaveOutlined onClick={handleProcessNameClick} />
            </Space>
          ) : (
            <Space>
              {processName}
              <EditOutlined
                onClick={() => {
                  setIsProcessNameSaved(false);
                }}
              />
            </Space>
          )}
        </Space>
      </Card>
    </>
  );
}
const isStartNode = (element: any) => {
  return element?.businessObject?.sourceRef?.$type !== 'bpmn:StartEvent';
};

const isTaskNode = (nodeType: NodeType) => {
  switch (nodeType) {
    case NodeType.UserTask:
      return true;
    case NodeType.Sequence:
      return false;
    case NodeType.CcTask:
      return true;
    default:
      return false;
  }
};
