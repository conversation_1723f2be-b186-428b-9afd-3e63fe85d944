import moment from 'moment';

import { CONDITION_ARR, NUMBER_RESOURCE } from '../constants';

export function getPropertyOrUser(resData?: Record<string, any>, propertyVal?: string) {
  if (!resData?.valueType?.includes(',')) {
    return;
  }

  let index = 0;
  const options = JSON.parse(resData?.options);

  if (!options[0].value.includes(propertyVal)) {
    index = 1;
  }

  return index;
}

export function formateExpression(expression: string) {
  //先把表达式里面的 and 和 or 替换成 && 或者 ||
  //兼容老数据，吧他们处理成一样的东西
  const resultString = expression
    ?.replace(/ and /g, ' && ')
    ?.replace(/ or /g, ' || ')
    ?.replace(/>/g, '&gt;')
    ?.replace(/</g, '&lt;')
    ?.replace(/>=/g, '&gt;=')
    ?.replace(/<=/g, '&lt;=')
    ?.replace(/&amp;amp;/g, '&')
    ?.replace(/&amp;/g, '&')
    ?.replace(/\$\{|}/g, '');

  return resultString?.split('||');
}

export function formatValue(result: string[], resource: Record<string, any>[]) {
  const val = result?.[1]?.trim()?.replace(/\(|\)|'/g, '');

  if (!val) {
    return '';
  }

  const propertiesCode = result?.[0]?.trim()?.replace(/\(|\)|'/g, '');
  const rowResource = resource.find(
    (item: Record<string, any>) => item.propertiesCode === propertiesCode
  );

  if (rowResource?.options && rowResource?.inputWay === 'COMPONENT') {
    const rowOption = JSON.parse(rowResource.options);
    // 楼栋列表的数据要特殊处理
    if (rowOption?.components === 'location') {
      const arr = val.split('.');
      let res: any[] = arr.reduce((acc, part, index) => {
        acc.push(arr.slice(0, index + 1).join('.'));
        return acc;
      }, [] as string[]);

      return res;
    }

    //如果它是日期需要特殊处理
    if (rowOption?.components === 'date' && val && val !== 'null') {
      return moment(Number(val));
    }
  }

  if (val.includes(',')) {
    return val?.split(',');
  }

  if (val === 'null') {
    return undefined;
  }

  if (rowResource?.valueType === 'NUMBER' && rowResource?.inputWay !== 'COMPONENT') {
    return Number(val);
  }

  return val.toString();
}

export function formateContainsValue(val: string, resource: Record<string, any>[]) {
  let obj = null;

  // 存在和不存在都必须处理成一个

  if (val.includes('!')) {
    //说明不包含
    const itemVal = val?.replace('!', '');
    const result = itemVal?.split('.contains');

    obj = {
      propertiesCode: result?.[0]?.trim()?.replace(/\(|\)|'/g, ''),
      condition: 'notInclude',
      value: formatValue(result, resource),
    };
  } else {
    //包含
    const result = val?.split('.contains');

    obj = {
      propertiesCode: result?.[0]?.trim()?.replace(/\(|\)|'/g, ''),
      condition: 'include',
      value: formatValue(result, resource),
    };
  }

  return obj;
}

export function getCommonOptionGroupArr(
  val: string,
  resource: Record<string, any>[],
  type: string
) {
  const sign = CONDITION_ARR.find(v => val.includes(v));

  if (sign) {
    const result = val?.split(sign);
    const obj = {
      propertiesCode: result[0]?.trim(),
      condition: NUMBER_RESOURCE.find(v => v.operator == sign)?.value,
      value: formatValue(result, resource),
    };

    return type === 'or' ? [obj] : obj;
  } else {
    //处理包含不包含
    const obj = formateContainsValue(val, resource);

    const optionValue = getCorrectLocationValue(resource, obj);
    const newObj = {
      ...obj,
      value: optionValue,
    };

    return type === 'or' ? [newObj] : newObj;
  }
}

//矫正location，多选的数据
export function getCorrectLocationValue(
  resource: Record<string, any>[],
  optionItem: Record<string, any>
) {
  const resItem = resource.find(
    (v: Record<string, any>) => v?.propertiesCode === optionItem?.propertiesCode
  );
  const resOption =
    resItem?.inputWay === 'COMPONENT' && resItem?.options && JSON.parse(resItem?.options);

  if (
    resItem?.propertiesType === 'ARRAY' &&
    resOption?.components === 'location' &&
    !Array.isArray(optionItem?.value[0])
  ) {
    return [optionItem.value];
  }

  return optionItem.value;
}
