import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import RedoOutlined from '@ant-design/icons/es/icons/RedoOutlined';
import SaveOutlined from '@ant-design/icons/es/icons/SaveOutlined';
import SettingOutlined from '@ant-design/icons/es/icons/SettingOutlined';
import UndoOutlined from '@ant-design/icons/es/icons/UndoOutlined';
import VerticalAlignBottomOutlined from '@ant-design/icons/es/icons/VerticalAlignBottomOutlined';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
// 左边工具栏以及编辑节点的样式
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
import 'bpmn-js/dist/assets/diagram-js.css';
// @ts-ignore: Could not find a declaration file
import BpmnModeler from 'bpmn-js/lib/Modeler';
// @ts-ignore: Could not find a declaration file
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda';
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { generateBpmEditUrl } from '@manyun/bpm.route.bpm-routes';
import { editScenes } from '@manyun/bpm.service.edit-scenes';
import { fetchBpm } from '@manyun/bpm.service.fetch-bpm';
import { mutateBpm } from '@manyun/bpm.service.mutate-bpm';

import styles from './bpm-editor.module.less';
import CustomPaletteProvider from './components/CustomPalette';
import CustomContentPadProvider from './components/content-pad';
import { PanelView } from './components/panel-view';
import { defaultEmpty } from './components/testxml';
import customTranslate from './customTranslate/customTranslate';

export type BpmEditorProps = {
  /**
   *   根据该code的值区分当前页面是新建还是编辑
   */
  code?: string;
};

export function BpmEditor({ code }: BpmEditorProps) {
  // bpm对象
  // 需要.d.ts文件，对element类型进行类型定义
  const [bpmnModeler, setBpmnModeler] = useState<any | null>(null);
  // 传给属性面板的节点对象
  // 需要.d.ts文件，对element类型进行类型定义
  const [bpmNode, setBpmNode] = useState<any | null>(null);
  // 汉化
  const customTranslateModule = { translate: ['value', customTranslate] };
  // processName
  const [name, setName] = useState('');
  const [processName, setProcessName] = useState('');
  // 抽屉
  const [visible, setVisible] = useState(false);
  //设置的抽屉
  const [settingVisible, setSettingVisible] = useState(false);
  const [autoApprovalType, setAutoApprovalType] = useState<number>();
  const [autoType, setAutoType] = useState<number>();
  const [isProcessNameSaved, setIsProcessNameSaved] = useState<boolean>(true);
  const [bpmXml, setBpmXml] = useState(
    defaultEmpty(
      `Process_${new Date().getTime()}`,
      `Event1_${new Date().getTime()}`,
      `Event2_${new Date().getTime()}`,
      `Activity_${new Date().getTime()}`,
      `Flow1_${new Date().getTime()}`,
      `Flow2_${new Date().getTime()}`
    )
  );

  // 链接跳转
  const history = useHistory();
  const location = useLocation();
  const historyParams: Record<string, any> = {};

  if (location.search) {
    const searchParams = new URLSearchParams(location.search);

    historyParams.bizFlowType = searchParams.get('bizFlowType') || '';
    historyParams.bizScenes = searchParams.get('bizScenes') || '';
    historyParams.subBizScenes = searchParams.get('subBizScenes') || '';
    historyParams.usable = searchParams.get('usable') || '';
  }

  useEffect(() => {
    initBpmn();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const fetchBpmModelData = async (code: string, bpmnModeler: any) => {
      const res = await fetchBpm({ code });
      setBpmXml(res.data!.xml);
      setAutoApprovalType(() => res.data?.autoApprovalType);

      bpmnModeler.importXML(changeXml(res.data!.xml, 'activiti:', 'camunda:')).then(() => {
        //初始化使得流程图居中
        bpmnModeler.get('canvas').zoom('fit-viewport', 'auto');
        const allShapes = bpmnModeler.get('elementRegistry').getAll();
        setBpmNode(allShapes[0]);
        setProcessName(allShapes[0].businessObject.name);
        addEventBusListener();
      });
    };
    if (bpmnModeler) {
      if (code) {
        fetchBpmModelData(code, bpmnModeler);
      } else {
        // importxml是异步的，所以后续的操作得写进回调函数中
        bpmnModeler.importXML(bpmXml).then(() => {
          //初始化使得流程图居中
          bpmnModeler.get('canvas').zoom('fit-viewport', 'auto');
          const allShapes = bpmnModeler.get('elementRegistry').getAll();
          setBpmNode(allShapes[0]);
          setProcessName(allShapes[0].businessObject.name);

          addEventBusListener();
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bpmnModeler]);
  const handleProcessName = (value: string) => {
    setName(value);
  };
  // 初始化流程图
  const initBpmn = () => {
    setBpmnModeler(
      new BpmnModeler({
        container: '#canvas', // 这里为数组的第一个元素
        additionalModules: [
          customTranslateModule,
          CustomPaletteProvider,
          CustomContentPadProvider,
          { labelEditingProvider: ['value', ''] },
        ],
        // 键盘快捷键
        keyboard: {
          bindTo: window,
        },
        addis: { CustomPaletteProvider, CustomContentPadProvider },
        moddleExtensions: {
          camunda: camundaModdleDescriptor,
          // activiti: activitiModdleDescriptor,
        },
      })
    );
  };

  // 监听 element
  const addEventBusListener = () => {
    const eventBus = bpmnModeler.get('eventBus'); // 需要使用 eventBus
    const allShapes = bpmnModeler.get('elementRegistry').getAll();
    const eventType = ['element.click', 'element.dblclick', 'element.changed', 'shape.added']; // 需要监听的事件集合
    let addFlag = false;
    let lineFlag = false;
    eventType.forEach(eventType => {
      // 重构下面的监听事件逻辑 todo
      // 需要.d.ts文件进行类型定义
      eventBus.on(eventType, (e: any) => {
        if (e.type === 'shape.added') {
          addFlag = true;
          lineFlag = false;
        } else if (e.type === 'element.changed') {
          if (!isNewElement(e.element.id)) {
            if (
              e.element?.businessObject?.targetRef?.id &&
              e.element.type === 'bpmn:SequenceFlow'
            ) {
              addFlag = true;
            }
            if (e.element.type === 'bpmn:UserTask') {
              initailizeUserTaskName(e.element);
              // 如果是会签任务，则需要加上监听器
              if (e.element.businessObject.loopCharacteristics) {
                updateElementExtensions(e.element);
              }
              // 如果不是会签任务，则设置监听器为空
              if (!e.element.businessObject.loopCharacteristics) {
                deleteElementExtensions(e.element);
              }
            }
            if (e.element.type === 'bpmn:ServiceTask') {
              initailizeServiceTaskName(e.element);
            }
            if (addFlag && e.element.type !== 'bpmn:SequenceFlow') {
              addFlag = false;

              updateElementExtensions2(e.element);
            }
            if (
              lineFlag === false &&
              e.element.type !== 'bpmn:SequenceFlow' &&
              e.element.type !== 'bpmn:ExclusiveGateway'
            ) {
              setBpmNode(e.element);
            }
            if (e.element.type === 'bpmn:SequenceFlow') {
              setConditionalLine(e.element);
              setBpmNode(e.element);
            }
          } else {
            if (e.element?.type !== 'label') {
              setBpmNode(allShapes[0]);
            }
          }
        } else if (e.type === 'element.dblclick') {
          setBpmNode(e.element);
          if (e.element.type !== 'bpmn:Process' && e.element.type !== 'label') {
            setVisible(true);
          } else {
            setVisible(false);
          }
        } else {
          setBpmNode(allShapes[0]);
          // setVisible(true);
        }
      });
    });
  };

  const bpmUndo = () => {
    bpmnModeler.get('commandStack').undo();
  };
  const bpmRedo = () => {
    bpmnModeler.get('commandStack').redo();
  };

  const isNewElement = (id: string) => {
    const allShapes = bpmnModeler
      .get('elementRegistry')
      .getAll()
      .map((item: { id: string }) => item.id);

    return allShapes.indexOf(id) === -1;
  };

  const initailizeUserTaskName = (element: any) => {
    if (!element.businessObject?.name) {
      element['name'] = '审批';
      bpmnModeler.get('modeling').updateProperties(element, {
        name: '审批',
      });
    }
  };

  const initailizeServiceTaskName = (element: any) => {
    if (element?.name === undefined) {
      element['name'] = '抄送';
      bpmnModeler.get('modeling').updateProperties(element, {
        name: '抄送',
      });
    }
  };

  const setConditionalLine = (element: any) => {
    if (
      element?.businessObject?.sourceRef?.$type === 'bpmn:UserTask' &&
      element?.businessObject?.targetRef?.$type === 'bpmn:UserTask' &&
      element?.name === undefined
    ) {
      element['name'] = '同意';
      bpmnModeler.get('modeling').updateProperties(element, {
        name: '同意',
      });
      let condition;
      // eslint-disable-next-line no-template-curly-in-string
      let body = '${result == 0}';
      condition = bpmnModeler.get('moddle').create('bpmn:FormalExpression', { body });
      bpmnModeler.get('modeling').updateProperties(element, {
        // eslint-disable-next-line no-template-curly-in-string
        conditionExpression: condition,
      });
    }
  };

  /**
   *
   * @description 格式化生成监听器数值，用于后续update方法的调用
   */
  const createListenerObject = (isTask: boolean, prefix: string) => {
    const listenerObj = Object.create(null);
    // isTask && (listenerObj.id = options.id); // 任务监听器特有的 id 字段
    if (isTask) {
      listenerObj.event = 'complete';
      listenerObj.expression = "${countersignService.taskComplete('ALL_PASS',task)}";
    } else {
      listenerObj.event = 'start';
      listenerObj.delegateExpression = '${processFinishListener}';
    }

    return bpmnModeler
      .get('moddle')
      .create(`${prefix}:${isTask ? 'TaskListener' : 'ExecutionListener'}`, listenerObj);
  };

  /**
   * @description 业务场景生成监听器
   */
  const createServiceListenerObject = (
    $type: string,
    event: string,
    delegateExpression: string
  ) => {
    const listenerObj = {
      event,
      delegateExpression,
    };

    return bpmnModeler.get('moddle').create($type, listenerObj);
  };

  /**
   *
   * @description 删除或签用户任务的监听器
   */
  const deleteElementExtensions = (element: any) => {
    // 把和业务流程无关的监听器删掉即可, 而不是全部删除
    if (historyParams.bizFlowType === 'SERVICE' || historyParams.bizFlowType === 'MIX') {
      //extensionElements 的类型是会变的，有时候是数组，有时候是对象
      let bpmnElementListenersArr = [];

      if (element?.businessObject?.extensionElements?.values) {
        bpmnElementListenersArr = element?.businessObject?.extensionElements?.values;
      }

      if (Array.isArray(bpmnElementListenersArr)) {
        const notServiceListenerArr = bpmnElementListenersArr?.filter(
          (item: Record<string, any>) =>
            item?.delegateExpression !== '${systemTaskStartListener}' &&
            item?.delegateExpression !== '${systemTaskCompleteListener}'
        );

        if (notServiceListenerArr?.length) {
          const serviceListenerArr = bpmnElementListenersArr?.filter(
            (item: Record<string, any>) =>
              item?.delegateExpression === '${systemTaskStartListener}' ||
              item?.delegateExpression === '${systemTaskCompleteListener}'
          );

          bpmnModeler.get('modeling').updateProperties(element, {
            extensionElements: serviceListenerArr,
          });
        }
      }
    } else {
      if (element.businessObject?.extensionElements) {
        bpmnModeler.get('modeling').updateProperties(element, {
          extensionElements: null,
        });
      }
    }

    const value = element.businessObject.id.split('_');

    if (value.length === 3) {
      let newId = [value[1], value[2]].join('_');

      bpmnModeler.get('modeling').updateProperties(element, {
        id: newId,
        di: { id: `${newId}_di` },
      });
    }
  };
  /**
   *
   * @description 新增会签用户任务的监听器,并修改任务id
   */
  const updateElementExtensions = (element: any) => {
    let bpmnElementListeners =
      element.businessObject?.extensionElements?.values?.filter(
        (ex: any) => ex.$type === `camunda:TaskListener`
      ) ?? [];
    if (!bpmnElementListeners.length) {
      const lbj = createListenerObject(true, 'camunda');
      bpmnElementListeners.push(lbj);

      let otherExtensionList =
        element.businessObject?.extensionElements?.values?.filter(
          (ex: any) => ex.$type !== `camunda:TaskListener`
        ) ?? [];

      let all = otherExtensionList.concat(bpmnElementListeners);
      const extensions = bpmnModeler.get('moddle').create('bpmn:ExtensionElements', {
        values: all,
      });
      bpmnModeler.get('modeling').updateProperties(element, {
        extensionElements: extensions,
      });
    }
    const value = element.businessObject.id.split('_');

    let pre = ['Countersign'];
    if (value.length < 3) {
      pre = pre.concat(value);
      let newId = pre.join('_');

      bpmnModeler.get('modeling').updateProperties(element, {
        id: newId,
        di: { id: `${newId}_di` },
      });
    }
  };
  /**
   *
   * @description 更新开始、结束节点的监听器
   */
  const updateElementExtensions2 = (element: any) => {
    if (element.type === 'bpmn:EndEvent') {
      let bpmnElementListeners =
        element.businessObject?.extensionElements?.values?.filter(
          (ex: any) => ex.$type === `camunda:ExecutionListener`
        ) ?? [];
      if (!bpmnElementListeners.length) {
        const lbj = createListenerObject(false, 'camunda');
        bpmnElementListeners.push(lbj);

        let otherExtensionList =
          element.businessObject?.extensionElements?.values?.filter(
            (ex: any) => ex.$type !== `camunda:ExecutionListener`
          ) ?? [];

        let all = otherExtensionList.concat(bpmnElementListeners);
        const extensions = bpmnModeler.get('moddle').create('bpmn:ExtensionElements', {
          values: all,
        });

        bpmnModeler.get('modeling').updateProperties(element, {
          name: '结束',
          extensionElements: extensions,
        });
      }
    } else if (element.type === 'bpmn:StartEvent') {
      bpmnModeler.get('modeling').updateProperties(element, {
        name: '开始',
      });
    }
  };

  // xml格式处理函数
  const changeXml = (xml: string, currentKeyWord: string, targetKeyWord: string) => {
    return xml.replace(new RegExp(currentKeyWord, 'g'), targetKeyWord);
  };

  // 导出bpmn类型文件
  const exportXML = async () => {
    try {
      const allShapes = bpmnModeler.get('elementRegistry').getAll();

      const { svg } = await bpmnModeler.saveSVG();

      let { href, filename } = setEncoded('SVG', allShapes[0].businessObject.name, svg);
      downloadFunc(href, filename);
    } catch (e) {}
  };
  function downloadFunc(href: string, filename: string) {
    if (href && filename) {
      let a = document.createElement('a');
      a.download = filename; //指定下载的文件名
      a.href = href; //  URL对象
      a.click(); // 模拟点击
      URL.revokeObjectURL(a.href); // 释放URL 对象
    }
  }
  // 根据所需类型进行转码并返回下载地址
  const setEncoded = (type: string, filename = 'diagram', data: string) => {
    const encodedData = encodeURIComponent(data);
    return {
      filename: `${filename}.${type}`,
      href: `data:application/${
        type === 'svg' ? 'text/xml' : 'bpmn20-xml'
      };charset=UTF-8,${encodedData}`,
      data: data,
    };
  };
  const uploadBackEndXML = async (code: string | undefined, name: string) => {
    try {
      const result = await bpmnModeler.saveXML({ format: true });
      const { xml } = result;
      let xml2 = changeXml(JSON.parse(JSON.stringify(xml)), 'camunda:', 'activiti:');
      uploadXML(code, changeXml(xml2, '&#39;', "'"), name);
    } catch (err) {}
  };
  const judgeInvalidConfig = () => {
    let count = 0;
    const allShapes = bpmnModeler.get('elementRegistry').getAll();
    for (const item of allShapes) {
      if (item.businessObject.$type === 'bpmn:EndEvent' && item.type !== 'label') {
        count += 1;
      }
    }

    return count === 1;
  };

  const judgeInvalidConditionalFlow = () => {
    const allShapes = bpmnModeler.get('elementRegistry').getAll();
    var reg = /\$\{(.+?)}/;

    for (const item of allShapes) {
      if (item.businessObject.$type === 'bpmn:SequenceFlow' && item.type !== 'label') {
        if (item.businessObject.conditionExpression) {
          if (!item.businessObject.conditionExpression.body?.length) {
            return 1;
          } else if (!reg.test(item.businessObject.conditionExpression?.body)) {
            return 2;
          }
        }
      }
    }

    return 0;
  };

  const judgeInvalidConfigStart = () => {
    let count = 0;
    const allShapes = bpmnModeler.get('elementRegistry').getAll();
    for (const item of allShapes) {
      if (item.businessObject.$type === 'bpmn:StartEvent' && item.type !== 'label') {
        count += 1;
      }
    }

    return count === 1;
  };

  const judgeInvalidData = () => {
    const allShapes = bpmnModeler.get('elementRegistry').getAll();
    let flag = true;
    let name = true;
    let nodeCurrentName = '';
    let nodeName = true;
    let elementId = '';
    allShapes.forEach((element: any) => {
      if (element.businessObject.$type !== 'bpmn:Process') {
        if (
          element.businessObject.$type === 'bpmn:StartEvent' ||
          element.businessObject.$type === 'bpmn:EndEvent'
        ) {
          if (!element.businessObject.name) {
            nodeName = false;
            elementId = element.businessObject.id;
          }
        }

        if (element.businessObject.$type === 'bpmn:UserTask') {
          if (
            !element.businessObject.assignee &&
            !element.businessObject.candidateUsers &&
            !element.businessObject.candidateGroups
          ) {
            flag = false;
            nodeCurrentName = element.businessObject.name;
            elementId = element.businessObject.id;
          }
          if (!element.businessObject.name) {
            nodeName = false;
            elementId = element.businessObject.id;
          }
        }
      }
      if (element.businessObject.$type === 'bpmn:Process') {
        if (!element.businessObject.name) {
          name = false;
        }
      }
    });
    return { flag, id: elementId, name, nodeName, nodeCurrentName };
  };
  // 编辑或者保存
  const uploadXML = async (code: string | undefined, xml: string, name: string) => {
    const invalidData = judgeInvalidData();
    const invalidConfig = judgeInvalidConfig();
    const invalidConfigStart = judgeInvalidConfigStart();
    const invalidConditionalFlow = judgeInvalidConditionalFlow();
    if (
      invalidData.flag &&
      invalidData.name &&
      invalidData.nodeName &&
      invalidConfig &&
      invalidConfigStart &&
      invalidConditionalFlow === 0 &&
      isProcessNameSaved
    ) {
      const res = await mutateBpm({ code, xml, name, autoApprovalType });
      if (res.error || !res.data) {
        message.error(res.error?.message);
      } else if (res.data) {
        if (!code) {
          const result = await editScenes({
            subBizScenes: historyParams?.subBizScenes,
            usable: historyParams?.usable,
            remark: '',
            processCode: res.data,
          });

          if (result.error || !result.data) {
            message.error(result.error?.message);
            return;
          }
        }

        message.success('保存成功!');

        if (historyParams?.bizScenes && historyParams?.subBizScenes && historyParams.bizFlowType) {
          history.push(
            `${generateBpmEditUrl({ processCode: code ?? res.data })}?bizScenes=${historyParams.bizScenes}&subBizScenes=${historyParams.subBizScenes}&bizFlowType=${historyParams.bizFlowType}`
          );
        } else {
          history.push(generateBpmEditUrl({ processCode: code ?? res.data }));
        }
      }
    } else {
      if (!invalidConfigStart) {
        message.error('流程有且只有一个开始节点！');
      } else if (!invalidConfig) {
        message.error('流程有且只有一个结束节点！');
      } else if (invalidConditionalFlow === 1) {
        message.error('条件顺序流缺少条件表达式！');
      } else if (invalidConditionalFlow === 2) {
        message.error('条件顺序流的条件表达式不规范！');
      } else if (!invalidData.flag) {
        message.error(`名称为${invalidData.nodeCurrentName}的节点缺少用户或角色信息！`);
      } else if (!invalidData.nodeName) {
        message.error(`编号${invalidData.id}的节点缺少名称`);
      } else if (!isProcessNameSaved) {
        message.error('流程名称未保存！');
      } else {
        message.error('缺少流程名称！');
      }
    }
  };

  return (
    <div id={styles.editorPage}>
      <div id="canvas" className={styles.container}></div>
      <Card id={styles.buttonGroup}>
        <Space style={{ width: '100%' }}>
          <Button size="small" icon={<UndoOutlined />} onClick={bpmUndo}>
            撤回
          </Button>
          <Button size="small" icon={<RedoOutlined />} onClick={bpmRedo}>
            恢复
          </Button>
          <Button size="small" icon={<VerticalAlignBottomOutlined />} onClick={exportXML}>
            导出
          </Button>
          <Button
            size="small"
            onClick={() => {
              setSettingVisible(true);
              autoApprovalType ? setAutoType(autoApprovalType) : setAutoType(1);
            }}
            icon={<SettingOutlined />}
          >
            设置
          </Button>
          <Button
            size="small"
            type="primary"
            onClick={() => uploadBackEndXML(code, name)}
            icon={<SaveOutlined />}
          >
            发布
          </Button>
        </Space>
      </Card>
      {/* 设置按钮的抽屉 */}
      <Drawer
        forceRender
        title="设置"
        size="large"
        placement="right"
        extra={
          <Space>
            <Button onClick={() => setSettingVisible(false)}>取消</Button>
            {/* 这里只是暂时的页面保存，真的保存是外面的保存按钮 */}
            <Button
              type="primary"
              onClick={() => {
                setAutoApprovalType(autoType);
                message.success('保存成功！');
              }}
            >
              保存
            </Button>
          </Space>
        }
        onClose={() => setSettingVisible(false)}
        open={settingVisible}
        className={styles['setting-approval-drawer']}
      >
        <div className={styles.title}>
          审批人去重：
          <span className={styles['title-extra']}>同一审批人在流程中重复出现时：</span>
        </div>
        <Radio.Group
          style={{ marginLeft: '84px' }}
          defaultValue={1}
          value={autoType}
          onChange={e => setAutoType(e.target.value)}
        >
          <Space direction="vertical">
            <Radio value={1}>
              <span className={styles['title-extra']}>
                仅审批一次，后续重复的审批节点均自动同意
              </span>
              <Tooltip
                placement="top"
                title="审批人在流程中重复出现，只在第一个节点进行审批，后面的节点会自动同意。"
              >
                <QuestionCircleOutlined className={styles.icon} />
              </Tooltip>
            </Radio>
            <Radio value={2}>
              <span className={styles['title-extra']}>不自动同意，每个节点都需要审批</span>
            </Radio>
            <Radio value={3}>
              <span className={styles['title-extra']}>仅针对连续审批的节点自动同意</span>
              <Tooltip
                placement="top"
                title="审批人在流程中重复出现：对于连续出现的节点，只在第一个节点进行审批；不连续的节点则都需要进行审批。"
              >
                <QuestionCircleOutlined className={styles.icon} />
              </Tooltip>
            </Radio>
          </Space>
        </Radio.Group>
      </Drawer>
      {/* 流程图 */}
      {bpmnModeler && (
        <PanelView
          modeler={bpmnModeler}
          isProcessNameSaved={isProcessNameSaved}
          setIsProcessNameSaved={setIsProcessNameSaved}
          element={bpmNode}
          visible={visible}
          setVisible={setVisible}
          processName={processName}
          handleProcessName={handleProcessName}
          setProcessName={setProcessName}
          historyParams={historyParams}
        />
      )}
    </div>
  );
}
