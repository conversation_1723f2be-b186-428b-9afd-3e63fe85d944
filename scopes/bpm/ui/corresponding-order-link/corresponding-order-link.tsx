import React from 'react';

import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { useApps } from '@manyun/dc-brain.context.apps';
import { Link } from '@manyun/dc-brain.navigation.link';
import {
  generateAlterInfoDetaillocation,
  generateSupplyCheckDetaillocation,
} from '@manyun/hrm.route.hrm-routes';
import { generateBorrowAndReturnDetailLocation } from '@manyun/resource-hub.route.resource-routes';
import {
  generateChangeOfflineLocation,
  generateChangeTemplateDetail,
  generateChangeTicketDetail,
  generateDrillOrderRoutePath,
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateRiskRegisterDetailLocation,
  generateStandardChangeLibraryDetailLocation,
  generateTicketLocation,
  generateWarrantyOrderDetailUrl,
} from '@manyun/ticket.route.ticket-routes';
import { generateChangeOnlineTemplateLocation } from '@manyun/ticket.route.ticket-routes';

export type CorrespondingOrderLinkProps = {
  targetName: string;
  targetId: string;
  targetType: string;
  idcTag?: string;
  blockGuid?: string;
};

export function CorrespondingOrderLink({
  targetName,
  targetId,
  targetType,
  idcTag,
  blockGuid,
}: CorrespondingOrderLinkProps) {
  // @ts-ignore: because of incorrect TS types
  const { sales } = useApps();
  const { approvalTypes } = useApprovalTypes();

  if (targetName.includes(',')) {
    return <>--</>;
  }

  switch (targetType) {
    case approvalTypes.EME_PROCESS:
      return (
        <Link
          href={generateDrillOrderRoutePath({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.VISITOR:
    case approvalTypes.POWER:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: targetType.toLowerCase(),
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.N_EVENT_LEVEL_CHG:
      return (
        <Link target="_blank" href={generateEvnetLocation({ id: targetId })}>
          {targetName}
        </Link>
      );
    case approvalTypes.EVENT_AUDIT_PROCESS:
      return (
        <Link
          target="_blank"
          href={
            targetId.startsWith('N')
              ? generateEvnetLocation({ id: targetId })
              : generateEventDetailRoutePath({
                  id: targetId,
                })
          }
        >
          {targetName}
        </Link>
      );
    case approvalTypes.CARD_OFF:
    case approvalTypes.CARD_CHANGE:
    case approvalTypes.CARD_APPLY:
    case approvalTypes.CARD_EXCHANGE:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'access_card_auth',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );

    case approvalTypes.IN_DOOR:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'access',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.OUT_DOOR:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'access',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.EX_WAREHOUSE:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'warehouse',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.INSPECT_END:
      return (
        <Link
          target="_blank"
          href={toUrl(
            generateTicketLocation({
              id: targetId,
              idcTag: idcTag,
              blockTag: blockGuid,
              ticketType: 'inspection',
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.CHANGE:
    case approvalTypes?.CHANGE_SUMMERY:
      return (
        <Link
          href={
            targetId.startsWith('N')
              ? generateChangeOfflineLocation({ id: targetId })
              : generateChangeTicketDetail({ id: targetId })
          }
        >
          {targetName}
        </Link>
      );
    case approvalTypes.CHANGE_TEMPLATE:
      return <Link href={generateChangeTemplateDetail({ id: targetId })}>{targetName}</Link>;
    case approvalTypes.STANDARD_CHANGE_TEMPLATE:
    case approvalTypes.STANDARD_CHANGE_TEMPLATE_POSTPONE:
      return (
        <Link href={generateStandardChangeLibraryDetailLocation({ id: targetId })}>
          {targetName}
        </Link>
      );
    case approvalTypes.EXCHANGE_PROCESS:
      return (
        <Link href={toUrl(generateAlterInfoDetaillocation({ id: targetId }))}>{targetName}</Link>
      );
    case approvalTypes.LEAVE_PROCESS:
      return (
        <Link href={toUrl(generateAlterInfoDetaillocation({ id: targetId }))}>{targetName}</Link>
      );
    case approvalTypes.SUPPLY_CHECK_PROCESS:
      return <Link href={generateSupplyCheckDetaillocation({ id: targetId })}>{targetName}</Link>;
    case approvalTypes.WARRANTY:
      return <Link href={generateWarrantyOrderDetailUrl({ id: targetId })}>{targetName}</Link>;
    case approvalTypes.BORROW:
    case approvalTypes.RENEW:
    case approvalTypes.TRANSFER:
      return (
        <Link href={generateBorrowAndReturnDetailLocation({ id: targetId })}>{targetName}</Link>
      );
    case approvalTypes.REPAIR_END:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'repair',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.ON_OFF_PROCESS:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'on_off',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.POWER_END:
      return (
        <Link
          href={toUrl(
            generateTicketLocation({
              ticketType: 'power',
              id: targetId,
            })
          )}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.RISK_REGISTER_CLOSE:
    case approvalTypes.RISK_REGISTER_EVAL:
    case approvalTypes.RISK_TIME_EXTENSION:
    case approvalTypes.RISK_ROC_UPGRADE:
      return (
        <Link
          href={generateRiskRegisterDetailLocation({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.CHANGE_ONLINE_APPLY:
    case approvalTypes.CHANGE_ONLINE_SUMMERY:
    case approvalTypes.CHANGE_ONLINE_DELAY:
      return (
        <Link
          href={generateChangeTicketDetail({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.CHANGE_ONLINE_TEMPLATE:
      return (
        <Link
          href={generateChangeOnlineTemplateLocation({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case approvalTypes.SALES_QUOTATION:
      return <a href={`${sales.baseURL}page/crm/sales-quote/${targetId}/detail`}>{targetName}</a>;
    case approvalTypes.MAINTENANCE_END:
      return (
        <Link target="_blank" href={`/page/tickets/maintenance/${targetId}`}>
          {targetName}
        </Link>
      );
    default:
      return <>--</>;
  }
}

function toUrl({ pathname, search = '' }: { pathname: string; search?: string }) {
  return pathname + search;
}
