import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BasicApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import {
  getApprovalCenterAction,
  selectApprovalCenter,
  setPaginationThenGetDataActionCreator,
} from '@manyun/bpm.state.approval-center';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { CorrespondingOrderLink } from '@manyun/bpm.ui.corresponding-order-link';
import { ManualCarbonCopyModal } from '@manyun/bpm.ui.manual-carbon-copy-modal';
import { AnyTargetLink } from '@manyun/dc-brain.ui.any-target-link';
import { TargetType } from '@manyun/notification-hub.model.on-site-messages';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

export function DataTableCc() {
  const {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  } = useSelector(selectApprovalCenter);
  const dispatch = useDispatch();
  const { approvalTypes, approvalTypeTextKeyMap } = useApprovalTypes();
  useEffect(() => {
    dispatch(getApprovalCenterAction({ shouldResetPageNum: true }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const paginationChangeHandler = useCallback(
    (current: number, size: number) => {
      dispatch(setPaginationThenGetDataActionCreator({ pageNum: current, pageSize: size }));
    },
    [dispatch]
  );
  const columns: ColumnsType<BpmInstance> = useMemo(() => {
    return [
      {
        title: '审批ID',
        dataIndex: 'instId',
        fixed: 'left',
        render: (_, { instId }) => {
          return <AnyTargetLink id={instId!} name={instId!} type={TargetType.APPROVAL_DETAIL} />;
        },
      },
      {
        title: '标题',
        dataIndex: 'title',
        render: (_, { title }) => {
          return (
            <Typography.Text style={{ maxWidth: 480 }} ellipsis={{ tooltip: true }}>
              {title}
            </Typography.Text>
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'bizType',
        render: (_, { bizType }) => {
          return approvalTypeTextKeyMap[bizType as BasicApprovalTypes];
        },
      },
      {
        title: '对应单号',
        dataIndex: 'bizId',
        render: (_, record) => {
          return (
            <CorrespondingOrderLink
              targetName={record.bizId}
              targetId={record.bizId}
              targetType={record.bizType}
            />
          );
        },
      },
      {
        title: '内容',
        dataIndex: 'content',
        width: 300,
        render: (_, { content, bizType }) => {
          return (
            <Typography.Text style={{ width: 300 }} ellipsis>
              {getFormattedContent(content ?? '', bizType as BasicApprovalTypes, approvalTypes)}
            </Typography.Text>
          );
        },
      },
      {
        title: '位置',
        dataIndex: 'blockGuid',
        render(_, record) {
          if (record.room) {
            return <SpaceText guid={`${record.blockGuid}.${record.room}`} />;
          }
          return record.blockGuid || record.idc ? (
            <SpaceText guid={`${record.blockGuid || record.idc}`} />
          ) : (
            '--'
          );
        },
      },
      {
        title: '发起人',
        dataIndex: 'applyUserName',
        render: (_, { applyUser, applyUserName }) => {
          return <UserLink userId={applyUser} userName={applyUserName} />;
        },
      },
      {
        title: '发起时间',
        dataIndex: 'applyTime',
        render: (_, { applyTime }) => {
          return <span>{dayjs(applyTime).format('YYYY-MM-DD HH:mm:ss')}</span>;
        },
      },

      {
        title: '状态',
        dataIndex: 'status',
        render: (_, { status }) => {
          return <ApprovalStatusText approveStatus={status} />;
        },
      },

      {
        title: '操作',
        fixed: 'right',
        dataIndex: 'action',
        render: (_, record) => {
          const { instId, applyTime } = record;

          return <ManualCarbonCopyModal applyTime={applyTime} type="link" instId={instId!} />;
        },
      },
    ];
  }, [approvalTypes, approvalTypeTextKeyMap]);
  return (
    <Table<BpmInstance>
      loading={loading}
      scroll={{ x: 'max-content' }}
      pagination={{
        total: total,
        current: pageNum,
        pageSize: pageSize,
        onChange: paginationChangeHandler,
      }}
      columns={columns}
      dataSource={data}
    />
  );
}

const getFormattedContent = (
  content: string,
  bizType: BasicApprovalTypes,
  approvalTypes: {
    [key in BasicApprovalTypes]: string;
  }
) => {
  const arr = BpmInstance.getFormattedContent(content);
  if (arr.length === 0) {
    return;
  }
  if ([approvalTypes.BORROW, approvalTypes.TRANSFER, approvalTypes.RENEW].includes(bizType)) {
    return arr.map(item => item.label + '：' + getUserName(item.value)).join('，');
  }
  return arr.map(item => item.label + '：' + item.value).join('，');
};

function getUserName(str: string) {
  if (str.includes('|')) {
    return str.split('|')[1];
  } else {
    return str;
  }
}
