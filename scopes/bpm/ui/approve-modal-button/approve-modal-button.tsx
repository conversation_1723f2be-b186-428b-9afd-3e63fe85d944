import { UploadOutlined } from '@ant-design/icons';
import React, { useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import type { BatchOperationResult } from '@manyun/bpm.model.bpm-instance';
import { fetchEventAuditReject } from '@manyun/bpm.service.fetch-event-audit-reject';
import { resolveTodo } from '@manyun/bpm.service.resolve-todo';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';

export type ApproveModalButtonProps = {
  instId: string;
  taskId: string;
  result: BatchOperationResult;
  text: string;
  // 审批类型，如销售报价。
  approveType?: string;
  onSuccess?: () => void;
  // 操作成功时额外部分的提示文案
  extraSuccessMessage?: string;
  bizType?: string;
  needAudit?: boolean;
  bizId?: string;
} & Pick<ButtonProps, 'type' | 'compact' | 'danger'>;
export type FormType = {
  comment: string | undefined;
  users: { value: number; label: string }[] | undefined;
  fileInfoList: McUploadFileJSON[];
  targetStatus?: string;
};
export function ApproveModalButton({
  instId,
  taskId,
  result,
  text,
  onSuccess,
  approveType,
  type,
  danger,
  compact,
  extraSuccessMessage,
  bizType,
  needAudit,
  bizId,
}: ApproveModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<FormType>();
  const chooseUser = Form.useWatch('chooseUser', form);
  const [configUtil] = useConfigUtil();
  //@ts-ignore
  const approvalListType = configUtil.getScopeCommonConfigs('bpm')?.approval?.approvalListType;
  const handleSubmit = () => {
    form.validateFields().then(async value => {
      const { comment, users, fileInfoList } = value;
      setLoading(true);
      let data;
      //事件驳回需要单独调用接口
      if (
        bizType === 'EVENT_AUDIT_PROCESS' &&
        typeof needAudit === 'boolean' &&
        result === 1 &&
        approvalListType
      ) {
        data = await fetchEventAuditReject({
          eventId: bizId,
          targetStatus: value?.targetStatus || '',
          comment,
          fileInfoList,
          commentAssigner:
            Array.isArray(users) && users.length > 0
              ? users.map(user => user.value).join(',')
              : undefined,
          instId,
          taskId,
        });
      } else {
        data = await resolveTodo({
          instId,
          taskId,
          result,
          comment,
          fileInfoList,
          commentAssigner:
            Array.isArray(users) && users.length > 0
              ? users.map(user => user.value).join(',')
              : undefined,
        });
      }

      const { error } = data;
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }

      message.success(extraSuccessMessage ? `操作成功，${extraSuccessMessage}` : '操作成功');
      setVisible(false);
      onSuccess && onSuccess();
    });
  };

  const note = result === 0 ? '审批意见' : text.includes('驳回') ? '驳回原因' : '拒绝原因';

  if (needAudit === false) {
    form.setFieldValue('targetStatus', 'YG_FINISHING');
  }

  return (
    <>
      <Button
        type={type}
        compact={compact}
        danger={danger}
        onClick={() => {
          setVisible(true);
        }}
      >
        {text}
      </Button>
      <Modal
        title={text}
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        confirmLoading={loading}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form
          form={form}
          initialValues={{ chooseUser: approveType === 'SALES_QUOTATION' }}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          {bizType === 'EVENT_AUDIT_PROCESS' &&
          typeof needAudit === 'boolean' &&
          approvalListType ? (
            <Form.Item
              label="退回至"
              name="targetStatus"
              rules={[{ required: true, message: '请选择退回至' }]}
            >
              <Radio.Group
                options={[
                  { label: '事件结单阶段', value: 'YG_FINISHING' },
                  { label: '事件复盘阶段', value: 'YG_REVIEW' },
                ]}
                disabled={!needAudit}
              />
            </Form.Item>
          ) : null}
          <Form.Item
            label={note}
            name="comment"
            rules={[{ max: 500, message: '最多输入 500 个字符！' }]}
          >
            <Input.TextArea maxLength={500} />
          </Form.Item>
          <Form.Item
            label="附件"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
            extra="支持扩展名：.jpg、.png、.pdf、.xls、.xlsx、.doc、.docx、.eml"
          >
            <McUpload
              key="upload"
              accept="image/*,.pdf,.xls,.xlsx,.doc,.docx,.eml"
              maxFileSize={50}
            >
              <Button icon={<UploadOutlined />}>上传附件</Button>
            </McUpload>
          </Form.Item>
          <Form.Item>
            <Row align="middle">
              <Col span={8}>
                <Form.Item name="chooseUser" valuePropName="checked" noStyle>
                  <Checkbox>仅部分人可见</Checkbox>
                </Form.Item>
              </Col>
              <Col span={16}>
                {chooseUser && (
                  <Form.Item
                    name="users"
                    rules={[{ required: true, message: '请选择用户后再提交' }]}
                    noStyle
                  >
                    <UserSelect
                      style={{ width: '100%' }}
                      mode="multiple"
                      includeCurrentUser={false}
                      maxTagCount="responsive"
                    />
                  </Form.Item>
                )}
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
