import { DownOutlined, UpOutlined } from '@ant-design/icons';
import get from 'lodash.get';
import React, { useEffect, useState } from 'react';
import { useLatest } from 'react-use';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance, FormProps } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyBpmProcess } from '@manyun/bpm.gql.client.approval';
import type { BpmProcess, BpmProcessUserInfo } from '@manyun/bpm.gql.client.approval';
import { useLoggedInUser } from '@manyun/iam.context.logged-in-user';
import { UserAvatar } from '@manyun/iam.ui.user-avatar';

import deleteSvg from './assets/delete.svg';
import { BpmUserSelect } from './components/bpm-user-select';

export type BpmInstanceFormProps = {
  idcTag: string;
  blockGuid: string;
  riskLevel: string;
  reason: string;
  changeType: string;
  changeRespPerson: number;
  processType?: string;
  outerForm?: FormInstance;
  defaultOpen?: boolean;
  changeVersion?: number;
} & FormProps;

export type ProcessNodeInfo = {
  id: number;
  name: string;
  edit: boolean;
  userInfoList: BpmProcessUserInfo[];
  userIdList: number[];
};

export function BpmInstanceForm({
  idcTag,
  blockGuid,
  riskLevel,
  reason,
  changeType,
  changeRespPerson,
  defaultOpen = false,
  outerForm,
  processType = 'CHANGE',
  changeVersion = 2,
  ...formProps
}: BpmInstanceFormProps) {
  const [form] = Form.useForm();
  const currentForm = outerForm ?? form;
  const [getBpmProcess, { data: bpmProcess }] = useLazyBpmProcess();
  const { user } = useLoggedInUser();
  const [open, setOpen] = useState(defaultOpen);
  const processNodeInfoList: ProcessNodeInfo[] =
    Form.useWatch('processNodeInfoList', currentForm) ?? [];
  const processNodeInfoListRef = useLatest(processNodeInfoList);
  const initProcessNodeInfoList = useDeepCompareMemo(
    () => (bpmProcess?.bpmProcess?.processNodeInfoList ?? []) as BpmProcess[],
    [bpmProcess?.bpmProcess?.processNodeInfoList]
  );
  const enable = bpmProcess?.bpmProcess?.enable ?? false;
  const processVersion = bpmProcess?.bpmProcess?.processVersion;

  useEffect(() => {
    (async () => {
      const { data, error } = await getBpmProcess({
        variables: {
          processType,
          idcTag,
          blockGuidList: [blockGuid],
          processParams: {
            riskLevel,
            reason,
            changeType,
            changeRespPerson,
            changeVersion,
            riskLevel2: riskLevel,
            creatorId: user?.id,
            createTime: new Date().getTime(),
          },
        },
      });
      if (error) {
        message.error(error.message ?? '未查询到对应审批流程，请重新选择');
      } else if (data && data.bpmProcess) {
        currentForm.setFieldsValue({
          processVersion: data.bpmProcess.processVersion,
          processNodeInfoList: data.bpmProcess.processNodeInfoList ?? [],
        });
      }
    })();
  }, [
    blockGuid,
    changeVersion,
    changeRespPerson,
    changeType,
    currentForm,
    getBpmProcess,
    idcTag,
    processType,
    reason,
    riskLevel,
    user?.id,
  ]);

  if ((typeof enable === 'boolean' && !enable) || initProcessNodeInfoList.length === 0) {
    return <></>;
  }

  return (
    <Form form={currentForm} colon={false} {...formProps}>
      <Form.Item label=" ">
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Text
            style={{ cursor: 'pointer' }}
            onClick={() => {
              setOpen(prev => !prev);
            }}
          >
            审批流程 {open ? <DownOutlined /> : <UpOutlined />}
          </Typography.Text>
          {initProcessNodeInfoList.length > 0 && (
            <Form.Item hidden={!open}>
              <Form.Item name="processVersion" initialValue={processVersion} noStyle />
              <Timeline>
                <Timeline.Item>开始</Timeline.Item>
                <Form.List name="processNodeInfoList">
                  {fields =>
                    fields.map(({ key }) => {
                      const current = initProcessNodeInfoList[key];
                      if (!current) {
                        return null;
                      }
                      const { id, name, edit, userInfoList } = current;
                      const currentUserIdList: number[] =
                        get(processNodeInfoListRef.current, [key, 'userIdList']) ?? [];

                      return (
                        <Timeline.Item key={key}>
                          <Space style={{ width: '100%' }} direction="vertical">
                            <Typography.Text>{name}</Typography.Text>
                            <Form.Item name={[key, 'id']} initialValue={id} noStyle />
                            <Form.Item name={[key, 'name']} initialValue={name} noStyle />
                            {edit ? (
                              <Form.Item name={[key, 'userIdList']} noStyle>
                                <BpmUserSelect
                                  userList={userInfoList}
                                  selectedKeys={currentUserIdList}
                                  onChange={selectedKeys => {
                                    currentForm.setFieldsValue({
                                      processNodeInfoList: processNodeInfoListRef.current.map(
                                        (processNodeInfo, index) => {
                                          if (index === key) {
                                            return {
                                              ...processNodeInfo,
                                              userIdList: selectedKeys,
                                            };
                                          }
                                          return processNodeInfo;
                                        }
                                      ),
                                    });
                                  }}
                                />
                              </Form.Item>
                            ) : (
                              <Space style={{ paddingTop: 8, width: 406 }} size={[16, 24]} wrap>
                                {userInfoList.map(({ userId, userName }) => (
                                  <UserAvatar
                                    key={`avatar_${userId}`}
                                    userId={userId}
                                    username={userName}
                                    showUserName
                                  />
                                ))}
                              </Space>
                            )}
                            {currentUserIdList.length > 0 && (
                              <Space style={{ paddingTop: 8, width: 406 }} size={[16, 24]} wrap>
                                {currentUserIdList.map(userId => (
                                  <Badge
                                    key={`avatar_${userId}`}
                                    offset={[0, 32]}
                                    count={
                                      <img
                                        style={{ cursor: 'pointer' }}
                                        alt="delete"
                                        src={deleteSvg}
                                        onClick={() => {
                                          const result = currentUserIdList.filter(
                                            id => id !== userId
                                          );
                                          currentForm.setFieldsValue({
                                            processNodeInfoList: processNodeInfoListRef.current.map(
                                              (processNodeInfo, index) => {
                                                if (index === key) {
                                                  return {
                                                    ...processNodeInfo,
                                                    userIdList: result,
                                                  };
                                                }
                                                return processNodeInfo;
                                              }
                                            ),
                                          });
                                        }}
                                      />
                                    }
                                  >
                                    <UserAvatar
                                      key={`avatar_${userId}`}
                                      userId={userId}
                                      showUserName
                                    />
                                  </Badge>
                                ))}
                              </Space>
                            )}
                          </Space>
                        </Timeline.Item>
                      );
                    })
                  }
                </Form.List>
                <Timeline.Item>结束</Timeline.Item>
              </Timeline>
            </Form.Item>
          )}
        </Space>
      </Form.Item>
    </Form>
  );
}
