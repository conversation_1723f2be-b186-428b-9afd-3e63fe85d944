import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';

export type BusinessCustomizationApprovalContentProps = {
  /** 由于是通用组件，所以对于dataSource的类型是any  */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  dataSource: any[];
  columns: { dataIndex: string; renderer: string; title: string }[];
};

export function BusinessCustomizationApprovalContent({
  dataSource,
  columns,
}: BusinessCustomizationApprovalContentProps) {
  return <Table dataSource={dataSource} columns={columns?.map(generateSpecificContent)} />;
}

const generateSpecificContent = (column: {
  dataIndex: string;
  renderer: string;
  title: string;
}) => {
  const { renderer, ...rest } = column;

  switch (renderer) {
    case 'VISITOR_USER_NAME':
      return {
        ...rest,
        key: column.dataIndex,
        render: (_: string, record: { sex: number; visitorType: string; userName: string }) => {
          const { sex, visitorType, userName } = record;
          return <VisitorUserName name={userName} gender={sex} visitorType={visitorType} />;
        },
      };

    default:
      return column;
  }
};

function VisitorUserName({
  name,
  gender,
  visitorType,
}: {
  name: string;
  gender: number;
  visitorType: string;
}) {
  return (
    <>
      <Space>
        <>{name}</>
      </Space>
      <Tag style={{ marginLeft: '8px' }}>{visitorType}</Tag>
    </>
  );
}
