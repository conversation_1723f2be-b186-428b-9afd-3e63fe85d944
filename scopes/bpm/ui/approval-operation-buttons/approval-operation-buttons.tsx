/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-18
 *
 * @packageDocumentation
 */
import get from 'lodash.get';
import flatten from 'lodash/flatten';
import React, { useEffect, useState } from 'react';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { Button } from '@manyun/base-ui.ui.button';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import type { BpmInstance, ProcessRecord, ProcessUser } from '@manyun/bpm.model.bpm-instance';
import { ApprovalStatus, NodeStatus } from '@manyun/bpm.model.bpm-instance';
import { fetchBpmInstance } from '@manyun/bpm.service.fetch-bpm-instance';
import type { SvcQuery as WithdrawTodoParams } from '@manyun/bpm.service.withdraw-todo';
import { withdrawTodo } from '@manyun/bpm.service.withdraw-todo';
import { ApproveModalButton } from '@manyun/bpm.ui.approve-modal-button';
import { useTaskData } from '@manyun/bpm.ui.bpm-instance-viewer';
import { CommentModalButton } from '@manyun/bpm.ui.comment-modal-button';
import {
  ManualCarbonCopyModal,
  didHappenWithinOneMonth,
} from '@manyun/bpm.ui.manual-carbon-copy-modal';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';

import CustructionPrint from './components/custruction-print';
import { RedirectModalButton } from './components/redirect-modal-button';

export type ApprovalOperationButtonsProps = {
  baseInfo: BpmInstance;
  getDetail?: (params?: { type: 'pass' | 'refuse' | 'revoke' }) => void;
  /** 是否内置在审批中 */
  insideApproval?: boolean;
  /** 是否展示抄送按钮 */
  showCarbonCopyButton?: boolean;
  showCommentButton?: boolean;
  showRevokeButton?: boolean;
  /** 是否展示转交按钮 */
  showRedirectButton?: boolean;
  // 操作成功时额外部分的提示文案
  extraSuccessMessage?: string;
  /** 是否内置在其他底部栏组件内 */
  insideOtherFootBar?: boolean;
};

export function ApprovalOperationButtons({
  baseInfo,
  getDetail,
  insideApproval = false,
  showCarbonCopyButton = true,
  showCommentButton = true,
  showRevokeButton = true,
  showRedirectButton = true,
  extraSuccessMessage,
  insideOtherFootBar = false,
}: ApprovalOperationButtonsProps) {
  const { userId } = getUserInfo();
  const { approvalTypes } = useApprovalTypes();
  const instId = get(baseInfo, 'code');
  const applyTime = get(baseInfo, 'applyTime');
  const bizType = get(baseInfo, 'bizType');
  const records = get(baseInfo, 'operationRecords');
  const status = get(baseInfo, 'status');
  const oaType = get(baseInfo, 'oaType');
  const applyUserId = get(baseInfo, 'applyUser');
  const blockGuid = get(baseInfo, 'blockGuid');
  const processChargeUser = get(baseInfo, 'processChargeUser');
  const processChargeUserName = get(baseInfo, 'processChargeUserName');
  const approveUserList = getApproveUserIdList(get(baseInfo, 'operationRecords'));
  const bizId = get(baseInfo, 'bizId');
  const isApproveFromSales = bizType === approvalTypes.SALES_QUOTATION;
  const [errorCode, setRrrorCode] = useState<string>();

  const [configUtil] = useConfigUtil();
  const rejectBtn = (configUtil as Record<string, any>)?.getScopeCommonConfigs('bpm')?.approval
    ?.rejectBtn;

  const [{ taskData }] = useTaskData({
    operationRecords: records?.length ? records : [],
    userId: String(userId),
    processChargeUser,
    processChargeUserName,
  });

  const pdfDownloadCase =
    status === ApprovalStatus.Pass && bizType === approvalTypes.SALES_QUOTATION;
  useEffect(() => {
    (async function () {
      if (!instId) {
        return;
      }
      const { error } = await fetchBpmInstance({ code: instId });
      if (error) {
        setRrrorCode(error.code);
        return;
      }
    })();
  }, [instId]);

  //正常审批人
  const normalApproval = taskData?.userList
    ?.filter((val: Record<string, any>) => val?.type !== 'PROCESS_CHARGER')
    ?.map((item: { id: string }) => item.id);

  const commentButtonCase = status !== ApprovalStatus.Revoke && !errorCode && showCommentButton;
  const carbonCopyButtonCase =
    status === ApprovalStatus.Pass && didHappenWithinOneMonth(applyTime) && showCarbonCopyButton;

  const basicOperationButtonCaseSpecial = Boolean(
    oaType === 'LOCAL' &&
      status === ApprovalStatus.Approving &&
      taskData?.taskResult &&
      Array.isArray(taskData?.userList) &&
      normalApproval.includes(String(userId))
  );
  //所有审批人
  const basicOperationButtonCase = Boolean(
    oaType === 'LOCAL' &&
      status === ApprovalStatus.Approving &&
      taskData?.taskResult &&
      Array.isArray(taskData?.userList) &&
      taskData.userList.map((item: { id: string }) => item.id).includes(String(userId))
  );
  const printButtonCase =
    (status === ApprovalStatus.Refuse || status === ApprovalStatus.Pass) &&
    bizType === approvalTypes.CONSTRUCTION_APPLY;
  const revokeButtonCase =
    status === ApprovalStatus.Approving && applyUserId === userId && showRevokeButton;

  async function revokeApprove(params: WithdrawTodoParams) {
    const { error } = await withdrawTodo(params);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success(extraSuccessMessage ? `撤回成功，${extraSuccessMessage}` : '撤回成功');
    getDetail && getDetail({ type: 'revoke' });
    return;
  }

  const needAudit = baseInfo?.formJson?.needAudit;

  const getBasicOperationButtons = (approveUserList: string[] | number[]) => {
    const buttonVariant = !insideOtherFootBar && insideApproval;
    const prefix = buttonVariant ? '' : '审批';
    const rejectBtnText = rejectBtn ? '驳回' : '拒绝';

    return (
      <>
        {basicOperationButtonCase && taskData.taskId !== '' && (
          <>
            {basicOperationButtonCaseSpecial && (
              <>
                <ApproveModalButton
                  instId={instId}
                  taskId={taskData.taskId}
                  result={0}
                  // 目前默认这样展示文案，因为此组件还没有去审批中心完成旧组件的替换
                  text={`${prefix}同意`}
                  type={buttonVariant ? 'link' : 'primary'}
                  compact={buttonVariant}
                  extraSuccessMessage={extraSuccessMessage}
                  onSuccess={() => {
                    getDetail && getDetail({ type: 'pass' });
                  }}
                />
                <ApproveModalButton
                  instId={instId}
                  taskId={taskData.taskId}
                  result={1}
                  text={`${prefix}${rejectBtnText}`}
                  danger
                  compact={buttonVariant}
                  type={buttonVariant ? 'text' : 'default'}
                  extraSuccessMessage={extraSuccessMessage}
                  onSuccess={() => {
                    getDetail && getDetail({ type: 'refuse' });
                  }}
                  bizType={bizType}
                  needAudit={needAudit}
                  // bizType="EVENT_AUDIT_PROCESS"
                  // needAudit={false}
                  bizId={bizId}
                />
              </>
            )}
            {showRedirectButton && (
              <RedirectModalButton
                instId={instId}
                taskId={taskData.taskId}
                userId={userId}
                filterUserIdList={[...approveUserList, applyUserId]}
                blockGuid={isApproveFromSales ? null : blockGuid}
                text={`${prefix}转交`}
                type={buttonVariant ? 'text' : 'warning'}
                compact={buttonVariant}
                onSuccess={getDetail}
              />
            )}
          </>
        )}
      </>
    );
  };
  const getCarbonCopyButton = () => {
    return (
      <>
        {carbonCopyButtonCase && (
          <ManualCarbonCopyModal
            instId={instId}
            applyTime={applyTime}
            type="primary"
            onSuccess={getDetail}
          />
        )}
      </>
    );
  };
  const getCommentButton = () => {
    return (
      <>
        {commentButtonCase && (
          <CommentModalButton
            instId={instId}
            applyUserId={applyUserId}
            useCase="businessOrder"
            onSuccess={getDetail}
          />
        )}
      </>
    );
  };

  const getRevokeButton = () => {
    return (
      <>
        {revokeButtonCase && (
          <Popconfirm
            key="remove"
            title={`确认要撤回${instId}的审批吗？`}
            onConfirm={() => revokeApprove({ instId: instId, operator: String(userId) })}
          >
            <Button>撤回审批</Button>
          </Popconfirm>
        )}
      </>
    );
  };
  const getPrintButton = () => {
    return (
      <>
        {printButtonCase && (
          <CustructionPrint
            formJson={baseInfo ? baseInfo.formJson : null}
            reason={baseInfo?.reason}
            approveRecords={records}
          />
        )}
      </>
    );
  };
  const getPdfDownloadButton = () => {
    return (
      <>
        {pdfDownloadCase && (
          <DownloadPdfButton
            type="default"
            exportElement={document.getElementById('bpm-content')}
            pdfName={
              baseInfo?.formJson?.customerName
                ? `${baseInfo.formJson.customerName}的销售报价审批单`
                : '--'
            }
          />
        )}
      </>
    );
  };
  const shouldButtonGroupAppear = [
    carbonCopyButtonCase,
    printButtonCase,
    revokeButtonCase,
    basicOperationButtonCase,
    commentButtonCase,
  ].includes(true);
  const Buttons = () => (
    <Space>
      {getBasicOperationButtons(approveUserList)}
      {getRevokeButton()}
      {getPrintButton()}
      {getCarbonCopyButton()}
      {getCommentButton()}
      {getPdfDownloadButton()}
    </Space>
  );
  return (
    <>
      {shouldButtonGroupAppear &&
        (insideApproval ? (
          <Buttons />
        ) : (
          <FooterToolBar>
            <Buttons />
          </FooterToolBar>
        ))}
    </>
  );
}

function getApproveUserIdList(operationRecords: ProcessRecord[]) {
  let approveUserList: string[] = [];
  const operationTasks = getCurrentExecuteOperationTasks(operationRecords);
  if (operationTasks && operationTasks.length) {
    operationTasks.forEach(task => {
      if (Array.isArray(task.userList) && task.userList.length > 0) {
        approveUserList = approveUserList.concat(getUserIdList(task.userList));
      }
    });
    return approveUserList.length > 0
      ? approveUserList.map(userId => parseInt(userId))
      : approveUserList;
  }
  return approveUserList;
}

function getUserIdList(userList: ProcessUser[]) {
  return userList.map(user => user.id);
}
function getCurrentExecuteOperationTasks(operationRecords: ProcessRecord[]) {
  const currentExecuteOperationRecords = operationRecords?.filter(
    record => record.operationType === NodeStatus.Execute
  );
  if (currentExecuteOperationRecords && currentExecuteOperationRecords.length) {
    return currentExecuteOperationRecords[0].operationTasks;
  }
  return [];
}
