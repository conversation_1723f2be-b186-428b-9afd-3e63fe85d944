import { UploadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import flatten from 'lodash/flatten';
import React, { useEffect, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { fetchBpmInstance } from '@manyun/bpm.service.fetch-bpm-instance';
import { initiateCarbonCopy } from '@manyun/bpm.service.initiate-carbon-copy';
import { useApps } from '@manyun/dc-brain.context.apps';
import { Link } from '@manyun/dc-brain.navigation.link';
import { McUpload } from '@manyun/dc-brain.ui.upload';

const { TextArea } = Input;

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

export type ManualCarbonCopyModalProps = {
  /** 审批id */
  instId: string;
  applyTime: number;
  // 操作成功时额外部分的提示文案
  extraSuccessMessage?: string;
  /** 按钮使用场景是否需要标注仅对当前审批生效 */
  isOuterApprovalSingle?: boolean;
  /** 回调函数 */
  onSuccess?: () => void;
  onClick?: () => void;
  filterUserIdList?: number[];
} & Pick<ButtonProps, 'type' | 'onClick'>;

export type FormType = {
  ccUserIdList: { value: number; label: string }[];
  fileInfoList: Record<string, any>[];
  description: string;
};

export function ManualCarbonCopyModal({
  instId,
  applyTime,
  type,
  extraSuccessMessage,
  isOuterApprovalSingle,
  onSuccess,
  onClick,
  // filterUserIdList = [],
}: ManualCarbonCopyModalProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { dcbase } = useApps();
  const [filterUserIdList, setFilterUserIdList] = useState<number[]>([]);

  const [form] = Form.useForm<FormType>();

  const handleSubmit = () => {
    form.validateFields().then(async value => {
      const { ccUserIdList, fileInfoList, description } = value;
      setLoading(true);

      const { error } = await initiateCarbonCopy({
        instId,
        ccUserIdList: ccUserIdList?.map(user => user.value),
        fileInfoList: fileInfoList?.map((value: Record<string, any>) => ({
          fileName: value?.name,
          filePath: value?.patialPath,
          fileFormat: value?.ext,
          uploadTime: value?.lastModified,
        })),
        description: description,
      });

      if (error) {
        message.error(error.message);
        return;
      }

      setLoading(false);
      message.success(`审批已抄送成功${extraSuccessMessage ? `，${extraSuccessMessage}` : ''}`);
      setVisible(false);
      onSuccess && onSuccess();
    });
  };

  useEffect(() => {
    if (!visible) {
      return;
    }

    fetchBpmInstance({
      code: instId,
    }).then(({ data }) => {
      const ccList = data?.operationRecords?.filter(item => item.nodeId === 'CC_PERSON') || [];
      const userIds =
        (flatten(
          ccList?.map(item => {
            return item?.ccUserList?.map(user => Number(user?.id));
          })
        ) as number[]) || [];

      setFilterUserIdList(userIds);
    });
  }, [visible]);

  return (
    <>
      {didHappenWithinOneMonth(applyTime) ? (
        <Button
          type={type}
          compact={type === 'link'}
          onClick={() => {
            onClick?.();
            setVisible(true);
          }}
        >
          抄送
        </Button>
      ) : (
        '--'
      )}
      <Modal
        title="抄送"
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        confirmLoading={loading}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          {!isOuterApprovalSingle && typeof isOuterApprovalSingle === 'boolean' && (
            <Alert
              showIcon
              type="info"
              message={
                <Typography.Text>
                  抄送仅对最新审批单
                  <Link
                    target="_blank"
                    external
                    href={`${dcbase.baseURL}${generateBPMRoutePath({ id: instId }).replace(/^\/+/, '')}`}
                  >
                    {instId}
                  </Link>
                  生效
                </Typography.Text>
              }
            />
          )}
          <Form form={form} {...layout}>
            <Form.Item
              name="ccUserIdList"
              label="抄送"
              rules={[{ required: true, message: '请选择用户后再提交' }]}
            >
              <UserSelect
                mode="multiple"
                placeholder="请搜索抄送人"
                includeCurrentUser={false}
                maxTagCount="responsive"
                disabledKeys={filterUserIdList}
              />
            </Form.Item>
            <Form.Item
              label="备注"
              name="description"
              rules={[{ max: 500, message: '最多输入 500 个字符！' }]}
            >
              <TextArea rows={4} maxLength={500} />
            </Form.Item>
            <Form.Item
              label="附件"
              name="fileInfoList"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
              extra="支持扩展名：.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx"
            >
              <McUpload key="upload" accept="image/*,.xls,.xlsx,.doc,.docx" maxFileSize={50}>
                <Button icon={<UploadOutlined />}>上传附件</Button>
              </McUpload>
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
}

export function didHappenWithinOneMonth(createTime: number) {
  return dayjs().diff(dayjs(createTime), 'months') < 1;
}
