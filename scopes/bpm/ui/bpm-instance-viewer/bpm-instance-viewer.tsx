import dayjs from 'dayjs';
import flatten from 'lodash.flatten';
import uniqBy from 'lodash/uniqBy';
import React from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import { NodeType } from '@manyun/bpm.model.bpm-instance';
import type {
  BpmInstance,
  ProcessRecord,
  ProcessTask,
  ProcessUser,
  TaskRedirectInfo,
} from '@manyun/bpm.model.bpm-instance';
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

import styles from './bpm-instance-viewer.module.less';
import { AlreadyOperateUser } from './components/already-operate-user.js';
import { DeleteCommentIcon } from './components/delete-comment-icon.js';
import { ErrorMark } from './components/error-mark.js';
import { FileShow } from './components/file-show.js';
import { NoteInfo, StepTitle } from './components/note-info.js';
import { PartUser } from './components/part-user.js';
import { RedirectAlert } from './components/redirect-alert.js';
import { UserAvatar } from './components/user-avatar.js';
import { generateUserStatusInAlert, getStatus, getStatusTag, getUserList } from './utils.js';

const REVOKE_STEP_KEY = 888888;

interface IUserAlreadyOperateList {
  alreadyOperateUsers: {
    userData: ProcessUser & {
      date: string;
      taskResult: string | null;
      remark: string | null;
      taskFileInfoList: BackendMcUploadFile[];
    };
    type: string;
    redirectAlreadyUsers: TaskRedirectInfo[];
  }[];
  userExecuteListData: (ProcessUser & {
    date: string;
    taskResult: string | null;
    remark: string | null;
  })[];
  redirectAwaitUsers: TaskRedirectInfo[];
}

export type TaskProcessUser = ProcessUser & {
  date: string;
  taskResult: string | null;
  remark: string | null;
};
/**
 *  基于流程模型生成的实例属性，包含各节点具体信息
 */
export type BpmInstanceViewerProps = {
  bpmInstance: BpmInstance;
  onSuccess?: () => void;
};

export function BpmInstanceViewer({ bpmInstance, onSuccess }: BpmInstanceViewerProps) {
  const {
    operationRecords,
    status,
    applyTime,
    reason,
    applyUser,
    code,
    applyUserName,
    processChargeUser,
    processChargeUserName,
  } = bpmInstance;

  const generateCommentStep = (recordItem: ProcessRecord) => {
    const { processCommentInfo } = recordItem;
    const { commentTime, content, deleteTime, personId, personName, isDeleted, id, fileInfoList } =
      processCommentInfo!;

    return {
      title: '评论',
      description: (
        <div className={styles.bpmCollapse} style={{ position: 'relative' }}>
          <div
            style={{
              width: '100%',
              fontSize: '12px',
              textAlign: 'right',
              position: 'absolute',
              top: '-28px',
            }}
          >
            <Typography.Text type="secondary" style={{}}>
              {dayjs(commentTime).format('MM.DD HH:mm')}
            </Typography.Text>
          </div>
          <Collapse expandIconPosition="end" ghost>
            <Collapse.Panel
              className={styles.bpmCollapsePanel}
              key="1"
              header={
                <div style={{ marginBottom: 4 }}>
                  <User.Link id={personId} name={personName} className={styles.userLink} />
                </div>
              }
            >
              <div style={{ background: '#fafafa', padding: '8px 16px' }}>
                <div>
                  {/* 备注 */}
                  <NoteInfo
                    info={
                      isDeleted
                        ? `已于${dayjs(deleteTime).format('YYYY-MM-DD HH:mm:ss')}删除评论`
                        : (content ?? '')
                    }
                  />
                  {/* 附件 */}
                  <FileShow fileInfoList={fileInfoList} />
                </div>
                {!isDeleted && (
                  <DeleteCommentIcon
                    instId={code}
                    commentId={id}
                    personId={personId}
                    onSuccess={onSuccess}
                  />
                )}
              </div>
            </Collapse.Panel>
          </Collapse>
        </div>
      ),
      icon: (
        <UserAvatar
          user={{
            operationType: NodeType.CommentNode,
            isStep: true,
            userAvatarId: personId,
            applyUser,
            whetherEnable: true,
          }}
        />
      ),
    };
  };

  //  根据节点类型生成不同的Step
  const generateStep = (
    recordItem: ProcessRecord & { isCountersignNode: boolean; type: string }
  ) => {
    if (recordItem.type === NodeType.UserTask) {
      const { operationTasks, operationName, operationType, isCountersignNode } = recordItem;

      // 或签且单人
      if (operationTasks?.length === 1 && operationTasks[0]?.userList?.length === 1) {
        const { taskRedirectInfoList, date, remark, taskFileInfoList, taskResult, userList } =
          operationTasks[0];
        const redirectAlreadyUsers = taskRedirectInfoList;
        const newLocal = '-26px';

        return {
          title: operationName,
          description: (
            <>
              <div style={{ display: date ? 'flex' : 'block', justifyContent: 'space-between' }}>
                <Typography.Text type="secondary">{getUserInfo(recordItem)}</Typography.Text>
                <Typography.Text type="secondary" style={{ marginTop: newLocal, fontSize: '12px' }}>
                  {date && dayjs(date).format('MM.DD HH:mm')}
                </Typography.Text>
              </div>
              {remark || taskFileInfoList || operationTasks[0]?.commentAssigner ? (
                <div style={{ background: '#fafafa', padding: '8px 16px', margin: '8px 0' }}>
                  {/* 备注信息 */}
                  {remark ? <NoteInfo info={remark} style={{ marginBottom: '0px' }} /> : null}
                  {/* 附件 */}
                  <FileShow fileInfoList={taskFileInfoList} style={{ marginTop: '8px' }} />
                  {/* 仅谁可见 */}
                  {operationTasks[0]?.commentAssigner ? (
                    <PartUser commentAssigner={operationTasks[0]?.commentAssigner} />
                  ) : null}
                </div>
              ) : null}
              {Boolean(redirectAlreadyUsers?.length) && !operationTasks[0].missUser && (
                <Collapse expandIconPosition="end" ghost className={styles.bpmCollapse}>
                  <Collapse.Panel
                    key="1"
                    header={<NoteInfo info="转交记录"></NoteInfo>}
                    className={styles.bpmCollapsePanel}
                  >
                    <Space style={{ width: '100%' }} direction="vertical">
                      {redirectAlreadyUsers.map((item, index: number) => {
                        return item.sourceUserId && item.targetUserId ? (
                          <RedirectAlert index={index} redirectInfo={item} applyUser={applyUser} />
                        ) : null;
                      })}
                    </Space>
                  </Collapse.Panel>
                </Collapse>
              )}
            </>
          ),
          icon: (
            <UserAvatar
              user={{
                operationType: taskResult === '拒绝' ? 'REFUSE' : operationType,
                isStep: true,
                isSingleAwait: true,
                userAvatarId: Number(userList[0].id),
                userName: userList[0].userName,
                applyUser,
                taskResult,
                whetherEnable: true,
              }}
            />
          ),
        };
      }
      // 或签多人或者是会签
      else {
        let remark = '';
        let status = '';
        let refuseUserSum = 0;
        let passUserSum = 0;
        let isMultipleRefuse = false;
        let singleRefuseUserId: undefined | number = undefined;
        let singleRefuseUserName: undefined | string = undefined;
        let singlePassUserId: undefined | number = undefined;
        let singlePassUserName: undefined | string = undefined;

        let isMultiplePass = false;

        if (isCountersignNode) {
          status = generateUserStatusInAlert(
            operationType,
            operationTasks!.map(item => item.taskResult)
          );
          refuseUserSum = operationTasks!.reduce((count, item) => {
            let addParam = 0;
            if (item.taskResult === '拒绝') {
              addParam += item.userList.length;
            }
            return count + addParam;
          }, 0);
          isMultipleRefuse = refuseUserSum > 1 ? true : false;
          passUserSum = operationTasks!.reduce((count, item) => {
            let addParam = 0;
            if (item.taskResult === '通过') {
              addParam += item.userList.length;
            }
            return count + addParam;
          }, 0);
          isMultiplePass = passUserSum > 1 ? true : false;

          if (status === '已拒绝' && refuseUserSum === 1) {
            singleRefuseUserId = Number(
              operationTasks!.find(item => item.taskResult === '拒绝')?.userList[0].id
            );
            singleRefuseUserName = operationTasks!.find(item => item.taskResult === '拒绝')
              ?.userList[0].userName;
          }
          if (status === '已同意' && refuseUserSum === 1) {
            singlePassUserId = Number(
              operationTasks!.find(item => item.taskResult === '通过')?.userList[0].id
            );
            singlePassUserName = operationTasks!.find(item => item.taskResult === '通过')
              ?.userList[0].userName;
          }

          remark = `多人会签(${status})`;
        } else if (!isCountersignNode) {
          if (operationTasks && operationTasks[0].userList?.length) {
            remark = `多人或签(${generateUserStatusInAlert(operationType)})`;
          }
        }

        return {
          description: (
            <>
              <div style={{ width: '100%' }}>
                {operationTasks && getUserInfo(recordItem, remark)}
              </div>
            </>
          ),
          icon: (
            <UserAvatar
              user={{
                operationType: status === '已拒绝' ? 'REFUSE' : operationType,
                isStep: true,
                userAvatarId: singleRefuseUserId ?? singlePassUserId,
                userName: singleRefuseUserName ?? singlePassUserName,
                isMultipleRefuse,
                isMultiplePass,
                applyUser,
                whetherEnable: true,
              }}
            />
          ),
        };
      }
    } else if (recordItem.type === NodeType.CommentNode) {
      return generateCommentStep(recordItem);
    } else {
      return generateCcStep(recordItem);
    }
  };

  // 去除掉网关节点、未执行的节点并排序
  const filterAndSortRecords = (operationRecords: ProcessRecord[]) => {
    return operationRecords
      .filter(item => item.nodeId.indexOf('Gateway') === -1 && item.operationType !== 'NOT_EXECUTE')
      .map(item => {
        if (item.nodeType === NodeType.CommentNode) {
          return { ...item, type: NodeType.CommentNode };
        }
        if (item.nodeId.indexOf('CC_') === -1) {
          return {
            ...item,
            type: NodeType.UserTask,
            operationTasks: item!.operationTasks!.filter(item => item.userList),
          };
        } else {
          return { ...item, type: NodeType.CcTask };
        }
      });
  };

  // 获取流程当前执行的节点
  const getStepCurrent = () => {
    let stepCurrent = 0;

    if (status === 'REVOKE') {
      return REVOKE_STEP_KEY;
    } else if (status === 'PASS') {
      return operationRecords.length;
    } else {
      if (!operationRecords.length) {
        return stepCurrent;
      }
      for (let i = 0; i < operationRecords.length; i++) {
        if (operationRecords[i].operationType === 'EXECUTE') {
          stepCurrent = i;
        }
      }

      return stepCurrent + 1;
    }
  };

  // 获取节点描述信息
  const getUserInfo = (
    recordItem: ProcessRecord & { isCountersignNode: boolean; type: string },
    remark?: string
  ) => {
    const { operationName } = recordItem;
    let operationTasks = recordItem?.operationTasks as any[];

    if (!operationTasks) {
      return;
    }

    //找到missUser的那一项，只要是missUser有一项即可，其他略过
    const missUserItem = operationTasks?.find(item => item.missUser);

    let specialMark;
    //处理异常节点
    if (missUserItem?.missUser) {
      remark = (
        <ErrorMark
          {...{
            recordItem,
            processChargeUser,
            processChargeUserName,
            processInstanceCode: code,
            applyUserId: applyUser,
          }}
        />
      ) as any; //处理异常文案
      specialMark = remark;

      //如果存在异常，那就应该在uselist里面加上流程负责人
      operationTasks = operationTasks.map((item: ProcessTask) => {
        let userList: Record<string, any>[] = [];
        const missUserInfo = item?.missUserInfo?.filter(
          (val: Record<string, any>) => val?.type === 'ROLE_NO_USER'
        );

        if (item?.missUser && missUserInfo?.length !== item?.missUserInfo?.length) {
          userList.push({
            id: processChargeUser || '14129',
            userName: processChargeUserName || '白雪锋',
            taskResult: '负责人',
            whetherEnable: true,
          });
        }

        return { ...item, userList: [...item.userList, ...userList] };
      });
    }

    if (operationTasks?.length > 1) {
      // 获取会签用户列表
      const userAlreadyOperateList: IUserAlreadyOperateList = {
        alreadyOperateUsers: [],
        redirectAwaitUsers: [],
        userExecuteListData: [],
      };

      for (const item of operationTasks) {
        if (item.taskResult !== '待审批' && item.taskResult !== '转交') {
          for (const user of item.userList as any[]) {
            userAlreadyOperateList.alreadyOperateUsers.push({
              userData: {
                date: item.date,
                commentAssigner: item?.commentAssigner,
                taskResult: item.taskResult,
                taskFileInfoList: item.taskFileInfoList,
                remark: item.remark,
                ...user,
              },
              type: 'AlreadyExecute',
              redirectAlreadyUsers: item?.taskRedirectInfoList?.length
                ? [...item.taskRedirectInfoList]
                : [],
            });
          }
        } else {
          for (const user of item.userList as any[]) {
            //待审批的人
            userAlreadyOperateList.userExecuteListData.push({
              date: item.date,
              taskResult: user?.taskResult || item.taskResult,
              remark: item.remark,
              ...user,
            });

            //已审批的人
            userAlreadyOperateList.alreadyOperateUsers.push({
              userData: {
                date: item?.taskRedirectInfoList?.length
                  ? item.taskRedirectInfoList.slice(-1)[0].redirectTime.toString()
                  : 'MAX',
                commentAssigner: item?.commentAssigner,
                taskResult: item.taskResult,
                taskFileInfoList: item.taskFileInfoList,
                remark: item.remark,
                ...user,
              },
              type: 'AwaitExecute',
              redirectAlreadyUsers: item?.taskRedirectInfoList?.length
                ? [...item.taskRedirectInfoList]
                : [],
            });
          }
        }
      }

      // 目前userAlreadyOperateList.alreadyOperateUsers里面包含了所有的人---以下处理的场景是多个角色里面有重复的人员
      const userAlreadyData = userAlreadyOperateList.alreadyOperateUsers.filter(
        item => item?.type === 'AlreadyExecute'
      );
      const userTodoData = userAlreadyOperateList.alreadyOperateUsers.filter(
        item => item?.type === 'AwaitExecute'
      );

      const alreadyUser = uniqBy(userAlreadyData, item => item?.userData?.id);
      const todoUser = uniqBy(userTodoData, item => item?.userData?.id);
      const userAlready = alreadyUser.map(item => item?.userData?.id); //拿出已经审批过的人

      //把待审批里面已经审批的人删掉
      const userExecuteListArr = userAlreadyOperateList.userExecuteListData.filter(
        item => !userAlready.includes(item?.id)
      );

      //重新组合待审批的人
      userAlreadyOperateList.userExecuteListData = getUserList(userExecuteListArr) as any[];
      userAlreadyOperateList.alreadyOperateUsers = [...alreadyUser, ...todoUser]; //转交的在todo里面

      //处理好之后再去重--遗留问题是：如果转交的人是待审批的人，那么就会重复
      userAlreadyOperateList.userExecuteListData = uniqBy(
        userAlreadyOperateList.userExecuteListData,
        'id'
      );

      const { remarkStatus, remarKText } = getStatus(remark || '');

      //转交列表
      const redirectAlreadyUsersArr = flatten(
        userAlreadyOperateList.alreadyOperateUsers.map(
          (val: Record<string, any>) => val?.redirectAlreadyUsers
        )
      ).map(val => {
        return {
          ...val,
          redirectDate: dayjs(val?.redirectTime).format('YYYY-MM-DD HH:mm:ss'),
        };
      });

      const redirectList = uniqBy(
        redirectAlreadyUsersArr,
        (val: Record<string, any>) => val.redirectDate
      );

      return (
        <div className={styles.bpmCollapse}>
          <Collapse expandIconPosition="end" ghost>
            <Collapse.Panel
              className={styles.bpmCollapsePanel}
              key="1"
              header={
                <div className={styles.collapseTitle}>
                  <Typography.Text>{operationName}</Typography.Text>
                  <Space>
                    <Typography.Text type="secondary" style={{ fontSize: '14px' }}>
                      {remarKText}
                    </Typography.Text>
                    {remarkStatus && getStatusTag(remarkStatus, true, 12)}
                  </Space>
                </div>
              }
            >
              <Space direction="vertical" size={8} style={{ width: '100%', marginTop: 8 }}>
                {userAlreadyOperateList.userExecuteListData.length > 0 && (
                  <Space size={20} wrap>
                    {userAlreadyOperateList.userExecuteListData.map((item: Record<string, any>) => (
                      <div key={item.userName}>
                        <UserAvatar
                          user={{
                            taskResult: item?.taskResult,
                            isCollapseAvatar: true,
                            isCollapseExecuteAvatar: true,
                            userName: item?.userName,
                            userAvatarId: Number(item.id),
                            whetherEnable: item?.whetherEnable,
                            applyUser,
                          }}
                        />
                      </div>
                    ))}
                  </Space>
                )}
                {redirectList.length ? (
                  redirectList.map((item: any, index: number) => {
                    return item.sourceUserId && item.targetUserId ? (
                      <RedirectAlert index={index} redirectInfo={item} applyUser={applyUser} />
                    ) : (
                      <></>
                    );
                  })
                ) : (
                  <></>
                )}
                <AlreadyOperateUser
                  alreadyOperateUsers={userAlreadyOperateList.alreadyOperateUsers}
                  applyUser={applyUser}
                />
              </Space>
            </Collapse.Panel>
          </Collapse>
        </div>
      );
    } else if (
      operationTasks &&
      operationTasks.length === 1 &&
      operationTasks[0].userList?.length > 1
    ) {
      // 获取或签用户列表
      let userListData = [];

      for (const item of operationTasks[0].userList) {
        userListData.push({
          date: operationTasks[0].date,
          taskResult: operationTasks[0].taskResult,
          remark: operationTasks[0].remark,
          ...item,
        });
      }
      //去重
      userListData = uniqBy(userListData, 'id');
      userListData = getUserList(userListData);

      //处理多人会签和多人或签的status
      const { remarkStatus, remarKText } = getStatus(remark || '');
      //转交
      const redirectAlreadyUsers = operationTasks?.[0]?.taskRedirectInfoList;

      return (
        <div className={styles.bpmCollapse}>
          <Collapse expandIconPosition="end" ghost>
            <Collapse.Panel
              className={styles.bpmCollapsePanel}
              key="1"
              header={
                <div className={styles.collapseTitle}>
                  <Typography.Text>{operationName}</Typography.Text>
                  <Space>
                    <Typography.Text type="secondary" style={{ fontSize: '14px' }}>
                      {remarKText}
                    </Typography.Text>
                    {remarkStatus && getStatusTag(remarkStatus, true, 12)}
                  </Space>
                </div>
              }
            >
              <Space size={20} style={{ width: '100%', marginTop: 8 }} wrap>
                {userListData.map((item, index: number) => (
                  <div key={item.userName}>
                    <UserAvatar
                      user={{
                        taskResult: item.taskResult,
                        isStep: false,
                        isCollapseAvatar: true,
                        isCollapseExecuteAvatar: true,
                        userName: item.userName,
                        userAvatarId: Number(item.id),
                        whetherEnable: item?.whetherEnable,
                        applyUser,
                      }}
                    />
                  </div>
                ))}
              </Space>
              {Boolean(redirectAlreadyUsers?.length) && (
                <Space style={{ width: '100%', marginTop: '20px' }} direction="vertical">
                  {redirectAlreadyUsers?.map((item: any, index: number) => {
                    return item.sourceUserId && item.targetUserId ? (
                      <RedirectAlert index={index} redirectInfo={item} applyUser={applyUser} />
                    ) : null;
                  })}
                </Space>
              )}
            </Collapse.Panel>
          </Collapse>
        </div>
      );
    } else {
      return (
        <>
          {specialMark ? (
            <div className={styles.collapseTitle}>
              <Typography.Text>{operationName}</Typography.Text>
              <Typography.Text type="secondary">{remark}</Typography.Text>
            </div>
          ) : (
            <></>
          )}
          <Space style={{ marginBottom: '4px' }}>
            <User.Link
              id={Number(operationTasks[0].userList[0]?.id)}
              useNativeLink={true}
              className={styles.userLink}
            />
            {/* 状态 */}
            {getStatusTag(generateUserStatusInAlert(operationTasks[0].taskResult), true, 12)}
          </Space>
        </>
      );
    }
  };

  // 生成第一个step
  const generateFirstStep = () => {
    return {
      title: '发起申请',
      icon: (
        <UserAvatar
          user={{
            operationType: 'ALREADY_EXECUTE',
            isStep: true,
            userAvatarId: applyUser,
            userName: applyUserName,
            applyUser,
            whetherEnable: true,
          }}
        />
      ),
      description: (
        <>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
            {applyUser && typeof applyUser === 'number' ? (
              <User.Link id={applyUser} name={applyUserName} className={styles.userLink} />
            ) : null}
            <Typography.Text type="secondary" style={{ marginTop: '-26px', fontSize: '12px' }}>
              {dayjs(applyTime).format('MM.DD HH:mm')}
            </Typography.Text>
          </div>
          {/* 备注信息 */}
          <NoteInfo
            info={operationRecords.length === 0 ? '按流程配置规则，自动审批通过' : (reason ?? '')}
          />
        </>
      ),
    };
  };
  // 生成抄送step
  const generateCcStep = (recordItem: ProcessRecord) => {
    const ccList = recordItem.ccUserList;
    const ccInfoList = recordItem?.ccInfoList;
    const ccReadList = ccList!.filter(cc => cc.ccMsgIsRead === true);

    return {
      description: (
        <div className={styles.bpmCollapse}>
          <Collapse expandIconPosition="end" ghost>
            <Collapse.Panel
              className={styles.bpmCollapsePanel}
              key="1"
              header={
                <div className={styles.collapseTitle}>
                  <StepTitle title={recordItem.operationName} />
                  <Typography.Text
                    type="secondary"
                    style={{ fontSize: 14 }}
                  >{`已抄送${ccList?.length}人`}</Typography.Text>
                </div>
              }
            >
              <Space size={19} wrap style={{ marginTop: 8 }}>
                {ccList?.map((item, index: number) => (
                  <div key={item.userName}>
                    <UserAvatar
                      user={{
                        taskResult: item.ccMsgIsRead ? '已读' : '待审批',
                        isStep: false,
                        isCollapseAvatar: true,
                        isCollapseExecuteAvatar: true,
                        userName: item.userName,
                        userAvatarId: Number(item.id),
                        applyUser,
                        whetherEnable: true,
                      }}
                    />
                  </div>
                ))}
              </Space>
              {ccInfoList?.length ? (
                <Space
                  style={{
                    gap: '8px',
                    marginTop: '12px',
                    width: '100%',
                  }}
                  wrap
                  direction="vertical"
                >
                  {ccInfoList?.map((item, index: number) => {
                    if (!item?.description && !item?.fileInfoList) {
                      return null;
                    }

                    return (
                      <div
                        key={item.userName}
                        style={{
                          background: '#fafafa',
                          padding: '8px 16px',
                          borderRadius: '4px',
                        }}
                      >
                        {/* 备注 */}
                        {item?.description ? <NoteInfo info={item?.description} /> : null}
                        {item?.fileInfoList && item?.fileInfoList?.length > 0 ? (
                          <FileShow fileInfoList={item?.fileInfoList} />
                        ) : null}
                      </div>
                    );
                  })}
                </Space>
              ) : null}
            </Collapse.Panel>
          </Collapse>
        </div>
      ),
      icon: (
        <UserAvatar
          user={{
            operationType:
              ccList && ccList?.length !== ccReadList?.length ? 'EXECUTE' : 'ALREADY_EXECUTE',
            isStep: true,
            isCc: true,
            applyUser,
            whetherEnable: true,
          }}
        />
      ),
    };
  };

  // 生成撤回节点
  const generateRevokeStep = () => {
    return {
      title: '已撤回',
      icon: (
        <UserAvatar
          user={{ operationType: 'REVOKE', isStep: true, whetherEnable: true, applyUser }}
        />
      ),
      description: (
        <>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            {applyUser ? <User.Link id={applyUser} className={styles.userLink} /> : null}
          </div>
        </>
      ),
    };
  };

  const stepItems: {
    title?: React.ReactNode;
    subTitle?: React.ReactNode;
    description?: React.ReactNode;
    icon?: React.ReactNode;
  }[] = [generateFirstStep()];

  if (status === 'REVOKE') {
    stepItems.push(generateRevokeStep());
  } else {
    for (const item of filterAndSortRecords(operationRecords)) {
      stepItems.push(generateStep(item));
    }
  }

  return (
    <div className={styles.bpmViewer}>
      <Steps
        direction="vertical"
        current={getStepCurrent()}
        items={stepItems}
        className={styles.bpmStep}
      />
    </div>
  );
}
