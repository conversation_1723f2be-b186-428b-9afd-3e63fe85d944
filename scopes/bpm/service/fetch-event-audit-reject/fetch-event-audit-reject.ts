/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-12
 *
 * @packageDocumentation
 */

import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs,  } from './fetch-event-audit-reject.type.js';

const endpoint = '/dcom/event/audit/reject';

/**
* @see [Doc](http://yapi.manyun-local.com/project/146/interface/api/30372)
*
* @param request
* @returns
*/
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);

  };
}             
