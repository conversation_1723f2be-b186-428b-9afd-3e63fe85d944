/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-12
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-event-audit-reject.js';
import type { ApiArgs } from './fetch-event-audit-reject.type.js';

/**
 * @param args
 * @returns
 */
export function fetchEventAuditReject(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
