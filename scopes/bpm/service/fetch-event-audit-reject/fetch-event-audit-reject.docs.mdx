---
description: 'A fetchEventAuditReject HTTP API service.'
labels: ['service', 'http']
---

事件评审驳回

## Usage

### Browser

```ts
import { fetchEventAuditReject } from '@manyun/bpm.service.fetch-event-audit-reject';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchEventAuditRejectService } from '@manyun/bpm.service.fetch-event-audit-reject/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchEventAuditRejectService.from(nodeRequest);
```
