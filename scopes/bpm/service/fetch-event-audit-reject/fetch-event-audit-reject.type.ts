/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-12
 *
 * @packageDocumentation
 */
import type { WriteBackendResponse } from '@glpdev/symphony.services.request';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';

export type ApiArgs = {
  /**
   * 事件单号
   */
  eventId?: string;
  /**
   * YG_FINISHING("事件结单")，YG_REVIEW("复盘")
   */
  targetStatus: string;
  /**
   * 驳回原因
   */
  comment?: string | null;
  fileInfoList?: McUploadFileJSON[];
  commentAssigner?: any;
  instId: string;
  taskId: string;
};

export type ApiResponse = WriteBackendResponse;
