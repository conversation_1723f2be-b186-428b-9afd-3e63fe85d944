export type UserState = 'in-service' | 'leaving';

export type UserType /* 员工 */ =
  | 'STAFF'
  | /* 客户 */ 'CUSTOMER'
  | /* 供应商 */ 'VENDOR'
  | /**外包员工 */ 'OUTSOURCE_STAFF';

export type UserGender = 'FEMALE' | 'MALE';

export type UserShifts = 'SIMPLE_SHIFTS' | 'FLEXIBLE_SHIFTS' | 'QUARTER_SHIFTS';

export type BackendUser = {
  id: number;
  userName: string;
  loginName: string;
  mobile: string;
  email: string;
  enable?: boolean;
  password?: string;
  userType?: UserType;
  /**
   * 1. 用户类型是客户时，存客户简称
   * 2. 用户类型是供应商时，存厂商简称
   * 3. 用户类型是员工时，为 null
   */
  company?: string | null;
  department?: string | null;
  departmentId?: string | null;
  /** 直属主管 id */
  director?: number | null;
  /** 二级主管 id */
  directorTwo?: number | null;
  /** 职位 */
  position?: string | null;
  /** 性别 */
  sexType?: UserGender;
  /** 工作描述 */
  jobDuties?: string | null;
  /**入职时间 */
  hiredDate?: number;
  /**参加工作时间 */
  joinWorkingTime?: number;
  /** 备注 */
  remarks?: string | null;
  /** 生日：年月日 */
  birthday?: string | null;
  englishName?: string | null;
  /** 家乡（籍贯）*/
  nativePlace?: string | null;
  /** 办公地点 */
  officeLocation?: string | null;
  /** 个性签名 */
  signature?: string | null;

  gmtCreate?: number;
  gmtModified?: number | null;
  creatorId?: number;
  creatorName?: string;
  modifierId?: number | null;
  modifierName?: string | null;
  lastLoginTime?: number | null;
  userShifts?: UserShifts;
  certCount?: number;
  professionalCertNum?: number;
  /**所属机房 */
  idc?: string;
  /**所属楼栋 */
  blockGuid?: string;
  /**离职日期 */
  resignDate?: string | null;

  /**
   * 工号
   */
  jobNumber?: string;
  /**
   * 管理标签
   */
  jobLabel?: string;
  /**
   * 兼岗楼栋
   */
  partTimeJobBlockGuids?: string[];
  /**
   * 管理楼栋
   */
  blockGuids?: string[];

  /**202506 YG 新增字段 STORY#2867 */
  /**身份证号 */
  certNo?: string;
  /**所属楼栋 */
  ptBlockGuids?: string[];
  /**是否已婚 */
  married?: boolean;
  /**户籍类型，传中文 */
  householdType?: string;
  /** 政治面貌: "PARTY_MEMBER" | "LEAGUE_MEMBER" | "MASSES" */
  politicalStatus?: string;
  /** 民族，传中文 */
  ethnicGroup?: string;
  /** 紧急联系人姓名 */
  emergencyContactName?: string;
  /** 紧急联系人电话 */
  emergencyContactMobile?: string;
  /** 家庭住址 */
  homeAddress?: string;
  /** 转正日期 */
  confirmationDate?: number;
  /** 学历信息 */
  academicQualifications?: {
    /** 入学时间 */
    admissionDate?: number;
    /** 毕业时间 */
    graduationDate?: number;
    /** 学校 */
    school?: string;
    /** 学历 */
    educationalDegree?: string;
    /** 专业 */
    major?: string;
  }[];
  /** 工作经历 */
  workExperiences?: {
    /** 入职时间 */
    hiredDate?: number;
    /** 离职时间 */
    resignDate?: number;
    /** 公司 */
    company?: string;
    /** 职位 */
    position?: string;
    /** 工作内容 */
    jobDescription?: string;
  }[];
  /** 党员信息 */
  partyMemberInfo?: {
    /** 入党时间 */
    joinDate?: number;
    /** 党支部名称 */
    partyBranchName?: string;
    /** 党内职务 */
    partyPosition?: string;
    /** 是否为流动党员 */
    floating?: boolean;
    /** 组织关系转入/转出 */
    partyRelationshipInfo?: string;
    /** 备注 */
    remark?: string;
  };
};

type SimpleUser = {
  id: number;
  name: string;
};

export type UserJSON = {
  id: number;
  name: string;
  login?: string;
  mobileNumber?: string;
  email?: string;
  state?: UserState;
  type?: UserType;
  nameEn?: string | null;
  gender?: UserGender;
  title?: string | null;
  company?: string | null;
  department?: string | null;
  departmentId?: string | null;
  supervisorUid?: number | null;
  subSupervisorUid?: number | null;
  jobDescriptions?: string | null;
  remarks?: string | null;
  birthday?: string | null;
  birthPlace?: string | null;
  workplace?: string | null;
  signature?: string | null;
  hiredDate?: number;
  joinWorkingDate?: number;
  gmtCreate?: number;
  gmtModified?: number | null;
  lastLoginTime?: number | null;
  createUser?: SimpleUser;
  modifyUser?: SimpleUser;
  userShifts?: UserShifts;
  certCount?: number;
  professionalCertNum?: number;
  idc?: string;
  blockGuid?: string;
  resignDate?: string | null;
  jobNumber?: string;
  jobLabel?: string;
  partTimeJobBlockGuids?: string[];
  blockGuids?: string[];

  //Add by 202506
  certNo?: string;
  ptBlockGuids?: string[];
  married?: boolean;
  householdType?: string;
  politicalStatus?: string;
  ethnicGroup?: string;
  emergencyContactName?: string;
  emergencyContactMobile?: string;
  homeAddress?: string;
  confirmationDate?: number;
  academicQualifications?: BackendUser['academicQualifications'];
  workExperiences?: BackendUser['workExperiences'];
  partyMemberInfo?: BackendUser['partyMemberInfo'];
};

export class User {
  constructor(
    /**
     * 用户 ID（数据库自增 ID）
     */
    public id: number,
    /**
     * 用户名
     */
    public name: string,
    /**
     * 用户登录名
     */
    public login?: string,
    /**
     * 用户手机号码
     */
    public mobileNumber?: string,
    /**
     * 用户邮箱
     */
    public email?: string,
    /** 是否在职 */
    public state?: UserState,
    public type?: UserType,
    public nameEn?: string | null,
    public gender?: UserGender,
    /** 职位 */
    public title?: string | null,
    public company?: string | null,
    public department?: string | null,
    public departmentId?: string | null,
    /** 直属主管 id */
    public supervisorUid?: number | null,
    /** 二级主管 id */
    public subSupervisorUid?: number | null,
    /** 备注 */
    public remarks?: string | null,
    /** 工作描述 */
    public jobDescriptions?: string | null,
    /** 入职日期*/
    public hiredDate?: number,
    /**参加工作日期 */
    public joinWorkingDate?: number,
    public birthday?: string | null,
    /** 籍贯 */
    public birthPlace?: string | null,
    /** 办公地点 */
    public workplace?: string | null,
    public signature?: string | null,
    public gmtCreate?: number,
    public gmtModified?: number | null,
    /**最后一次登陆时间 */
    public lastLoginTime?: number | null,
    public createUser?: SimpleUser,
    public modifyUser?: SimpleUser,
    public userShifts?: UserShifts,
    public certCount?: number,
    public professionalCertNum?: number,
    public idc?: string,
    public blockGuid?: string,
    public resignDate?: string | null,
    public jobNumber?: string,
    public jobLabel?: string,
    public partTimeJobBlockGuids?: string[],
    public blockGuids?: string[],

    //Add By 202506
    public certNo?: string,
    public ptBlockGuids?: string[],
    public married?: boolean,
    public householdType?: string,
    public politicalStatus?: string,
    public ethnicGroup?: string,
    public emergencyContactName?: string,
    public emergencyContactMobile?: string,
    public homeAddress?: string,
    public confirmationDate?: number,
    public academicQualifications?: BackendUser['academicQualifications'],
    public workExperiences?: BackendUser['workExperiences'],
    public partyMemberInfo?: BackendUser['partyMemberInfo']
  ) {}

  static fromApiObject(object: BackendUser) {
    return new User(
      object.id,
      object.userName,
      object.loginName,
      object.mobile,
      object.email,
      User.fromApiState(object),
      object.userType,
      object.englishName,
      object.sexType,
      object.position,
      object.company,
      object.department,
      object.departmentId,
      object.director,
      object.directorTwo,
      object.remarks,
      object.jobDuties,
      object.hiredDate,
      object.joinWorkingTime,
      object.birthday,
      object.nativePlace,
      object.officeLocation,
      object.signature,
      object.gmtCreate,
      object.gmtModified,
      object.lastLoginTime,
      object.creatorId && object.creatorName
        ? { id: object.creatorId, name: object.creatorName }
        : undefined,
      object.modifierId && object.modifierName
        ? { id: object.modifierId, name: object.modifierName }
        : undefined,
      object.userShifts,
      object.certCount,
      object.professionalCertNum,
      object.idc,
      object.blockGuid,
      object.resignDate,
      object.jobNumber,
      object.jobLabel,
      object.partTimeJobBlockGuids,
      object.blockGuids,

      object.certNo,
      object.ptBlockGuids,
      object.married,
      object.householdType,
      object.politicalStatus,
      object.ethnicGroup,
      object.emergencyContactName,
      object.emergencyContactMobile,
      object.homeAddress,
      object.confirmationDate,
      object.academicQualifications,
      object.workExperiences,
      object.partyMemberInfo
    );
  }

  static toApiObject(user: User): BackendUser {
    return {
      id: user.id,
      userName: user.name,
      loginName: user.login as string,
      mobile: user.mobileNumber as string,
      email: user.email as string,
      enable: User.toApiState(user),
      userType: user.type,
      company: user.company,
      department: user.department,
      departmentId: user.departmentId,
      director: user.supervisorUid,
      directorTwo: user.subSupervisorUid,
      position: user.title,
      sexType: user.gender,
      jobDuties: user.jobDescriptions,
      remarks: user.remarks,
      birthday: user.birthday,
      englishName: user.nameEn,
      nativePlace: user.birthPlace,
      officeLocation: user.workplace,
      signature: user.signature,
      gmtCreate: user.gmtCreate,
      gmtModified: user.gmtModified,
      lastLoginTime: user.lastLoginTime,
      creatorId: user.createUser?.id,
      creatorName: user.createUser?.name,
      modifierId: user.modifyUser?.id,
      modifierName: user.modifyUser?.name,
      hiredDate: user.hiredDate,
      joinWorkingTime: user.joinWorkingDate,
      userShifts: user.userShifts,
      certCount: user.certCount,
      professionalCertNum: user.professionalCertNum,
      idc: user.idc,
      blockGuid: user.blockGuid,
      resignDate: user.resignDate,
      jobNumber: user.jobNumber,
      jobLabel: user.jobLabel,
      partTimeJobBlockGuids: user.partTimeJobBlockGuids,
      blockGuids: user.blockGuids,

      //Add By 202506
      certNo: user.certNo,
      ptBlockGuids: user.ptBlockGuids,
      married: user.married,
      householdType: user.householdType,
      politicalStatus: user.politicalStatus,
      ethnicGroup: user.ethnicGroup,
      emergencyContactName: user.emergencyContactName,
      emergencyContactMobile: user.emergencyContactMobile,
      homeAddress: user.homeAddress,
      confirmationDate: user.confirmationDate,
      academicQualifications: user.academicQualifications,
      workExperiences: user.workExperiences,
      partyMemberInfo: user.partyMemberInfo,
    };
  }

  toJSON(): UserJSON {
    const user = this;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const userJson: any = {};
    Object.keys(user).forEach(key => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (typeof (user as any)[key] !== 'function') {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (userJson as any)[key] = (user as any)[key];
      }
    });
    return userJson as UserJSON;
  }

  static fromApiState(backendUser: BackendUser): UserState {
    if (backendUser.enable) {
      return 'in-service';
    }

    return 'leaving';
  }

  static toApiState(user: User): boolean {
    return user.state === 'in-service';
  }
}
