/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "cache/user": {
        "name": "cache/user",
        "scope": "auth-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "cache/user",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "gql/client/customer-white-list": {
        "name": "gql/client/customer-white-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "gql/client/customer-white-list",
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "hook/use-authorized": {
        "name": "hook/use-authorized",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-authorized",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-common-authorized": {
        "name": "hook/use-common-authorized",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "hook/use-common-authorized",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-roles": {
        "name": "hook/use-roles",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-roles",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-user-groups": {
        "name": "hook/use-user-groups",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-user-groups",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-users": {
        "name": "hook/use-users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-users",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "model/audit-log": {
        "name": "model/audit-log",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "model/audit-log",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.18": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "model/authorization-record": {
        "name": "model/authorization-record",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "model/authorization-record",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.18": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "model/operation-log": {
        "name": "model/operation-log",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "model/operation-log",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.18": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "model/user": {
        "name": "model/user",
        "scope": "auth-hub",
        "version": "0.0.15",
        "mainFile": "index.ts",
        "rootDir": "model/user",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.18": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "model/user-group": {
        "name": "model/user-group",
        "scope": "auth-hub",
        "version": "0.0.9",
        "mainFile": "index.ts",
        "rootDir": "model/user-group",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.18": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "page/403": {
        "name": "page/403",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/403",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/audit-log-list": {
        "name": "page/audit-log-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/audit-log-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/customer-white-detail": {
        "name": "page/customer-white-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.iam",
        "mainFile": "index.ts",
        "rootDir": "page/customer-white-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/customer-white-list": {
        "name": "page/customer-white-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/customer-white-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/customer-white-list-mutator": {
        "name": "page/customer-white-list-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/customer-white-list-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/login": {
        "name": "page/login",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/login",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-profile": {
        "name": "page/user-profile",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/user-profile",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-role-authorization": {
        "name": "page/user-role-authorization",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/user-role-authorization",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/users": {
        "name": "page/users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/users",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/users-creator": {
        "name": "page/users-creator",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "page/users-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/auth-routes": {
        "name": "route/auth-routes",
        "scope": "auth-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "route/auth-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/add-role-permissions": {
        "name": "service/add-role-permissions",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/add-role-permissions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/bind-role-permissions": {
        "name": "service/bind-role-permissions",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/bind-role-permissions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-role": {
        "name": "service/create-role",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-role",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-users": {
        "name": "service/create-users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-user": {
        "name": "service/delete-user",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-users": {
        "name": "service/export-users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/export-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-audit-log-list": {
        "name": "service/fetch-audit-log-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-audit-log-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-authorization-record-list": {
        "name": "service/fetch-authorization-record-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-authorization-record-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-dept-detail": {
        "name": "service/fetch-dept-detail",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-dept-detail",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-dept-list": {
        "name": "service/fetch-dept-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-dept-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-dept-users-by-key": {
        "name": "service/fetch-dept-users-by-key",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-dept-users-by-key",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-influence-resources": {
        "name": "service/fetch-influence-resources",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-influence-resources",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-modify-type": {
        "name": "service/fetch-modify-type",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-modify-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-my-permissions": {
        "name": "service/fetch-my-permissions",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-my-permissions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-operation-log-list": {
        "name": "service/fetch-operation-log-list",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-operation-log-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-user-groups": {
        "name": "service/fetch-paged-user-groups",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-user-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-user-groups-on-user": {
        "name": "service/fetch-paged-user-groups-on-user",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-user-groups-on-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-users": {
        "name": "service/fetch-paged-users",
        "scope": "auth-hub",
        "version": "0.0.12",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-role-permissions-tree": {
        "name": "service/fetch-role-permissions-tree",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-role-permissions-tree",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user": {
        "name": "service/fetch-user",
        "scope": "auth-hub",
        "version": "0.0.12",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-email": {
        "name": "service/fetch-user-email",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-email",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-permissions": {
        "name": "service/fetch-user-permissions",
        "scope": "auth-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-permissions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-punches": {
        "name": "service/fetch-user-punches",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-punches",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-punches-statistics-by-month": {
        "name": "service/fetch-user-punches-statistics-by-month",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-punches-statistics-by-month",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-shift-schedule": {
        "name": "service/fetch-user-shift-schedule",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-shift-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-users-by-ids": {
        "name": "service/fetch-users-by-ids",
        "scope": "auth-hub",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-users-by-ids",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-users-by-key": {
        "name": "service/fetch-users-by-key",
        "scope": "auth-hub",
        "version": "0.0.4",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-users-by-key",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-users-by-resource-code": {
        "name": "service/fetch-users-by-resource-code",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-users-by-resource-code",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-my-password": {
        "name": "service/mutate-my-password",
        "scope": "auth-hub",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-my-password",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-user": {
        "name": "service/mutate-user",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-user-company-on-user": {
        "name": "service/mutate-user-company-on-user",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-user-company-on-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-user-groups-on-user": {
        "name": "service/mutate-user-groups-on-user",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-user-groups-on-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/pm/fetch-exclusive-users": {
        "name": "service/pm/fetch-exclusive-users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/pm/fetch-exclusive-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/pm/fetch-roles": {
        "name": "service/pm/fetch-roles",
        "scope": "auth-hub",
        "version": "0.0.13",
        "mainFile": "index.ts",
        "rootDir": "service/pm/fetch-roles",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/pm/fetch-user-groups": {
        "name": "service/pm/fetch-user-groups",
        "scope": "auth-hub",
        "version": "0.0.11",
        "mainFile": "index.ts",
        "rootDir": "service/pm/fetch-user-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/pm/fetch-users-by-ids": {
        "name": "service/pm/fetch-users-by-ids",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/pm/fetch-users-by-ids",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/sync-users": {
        "name": "service/sync-users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/sync-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-role": {
        "name": "service/update-role",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-role",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-user-login-settings": {
        "name": "service/update-user-login-settings",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-user-login-settings",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-user-state": {
        "name": "service/update-user-state",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-user-state",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/valid-staff-infos-complete": {
        "name": "service/valid-staff-infos-complete",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.iam",
        "mainFile": "index.ts",
        "rootDir": "service/valid-staff-infos-complete",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/roles": {
        "name": "state/roles",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "state/roles",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/user": {
        "name": "state/user",
        "scope": "auth-hub",
        "version": "0.0.11",
        "mainFile": "index.ts",
        "rootDir": "state/user",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/user-groups": {
        "name": "state/user-groups",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "state/user-groups",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/users": {
        "name": "state/users",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "state/users",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "ui/audit-log-type": {
        "name": "ui/audit-log-type",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/audit-log-type",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/authorization-record-table": {
        "name": "ui/authorization-record-table",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/authorization-record-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/complex-users-picker": {
        "name": "ui/complex-users-picker",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/complex-users-picker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customer-white-list-application-mutator": {
        "name": "ui/customer-white-list-application-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/customer-white-list-application-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customer-white-staff-table": {
        "name": "ui/customer-white-staff-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.iam",
        "mainFile": "index.ts",
        "rootDir": "ui/customer-white-staff-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dept-text": {
        "name": "ui/dept-text",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/dept-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dept-tree-select": {
        "name": "ui/dept-tree-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/dept-tree-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/empty-no-auth": {
        "name": "ui/empty-no-auth",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/empty-no-auth",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/gql/resource-cascader": {
        "name": "ui/gql/resource-cascader",
        "scope": "auth-hub",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "ui/gql/resource-cascader",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/gql/role-select": {
        "name": "ui/gql/role-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/gql/role-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/logs-action-type-select": {
        "name": "ui/logs-action-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/logs-action-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/operation-log-table": {
        "name": "ui/operation-log-table",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/operation-log-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/resource-type-select": {
        "name": "ui/resource-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/resource-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/role-editor-drawer": {
        "name": "ui/role-editor-drawer",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/role-editor-drawer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/role-select": {
        "name": "ui/role-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/role-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/role-text": {
        "name": "ui/role-text",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/role-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user": {
        "name": "ui/user",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-company-connector": {
        "name": "ui/user-company-connector",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-company-connector",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-email-input": {
        "name": "ui/user-email-input",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/user-email-input",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-email-text": {
        "name": "ui/user-email-text",
        "scope": "auth-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/user-email-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-gender": {
        "name": "ui/user-gender",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-gender",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-group-select": {
        "name": "ui/user-group-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-group-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-groups-card": {
        "name": "ui/user-groups-card",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-groups-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-groups-data-table": {
        "name": "ui/user-groups-data-table",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-groups-data-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-info-card": {
        "name": "ui/user-info-card",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-info-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-mentions": {
        "name": "ui/user-mentions",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-mentions",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-profile-prompt-modal-view": {
        "name": "ui/user-profile-prompt-modal-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.iam",
        "mainFile": "index.ts",
        "rootDir": "ui/user-profile-prompt-modal-view",
        "config": {
            "bitdev.react/react-env@4.1.4": {},
            "teambit.envs/envs": {
                "env": "bitdev.react/react-env"
            }
        }
    },
    "ui/user-select": {
        "name": "ui/user-select",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-state": {
        "name": "ui/user-state",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-state",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-type": {
        "name": "ui/user-type",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-type",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/users-data-table": {
        "name": "ui/users-data-table",
        "scope": "",
        "version": "",
        "defaultScope": "auth-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/users-data-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "$schema-version": "17.0.0"
}