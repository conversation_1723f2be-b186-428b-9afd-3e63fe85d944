/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { BackendUser, UserJSON } from '@manyun/auth-hub.model.user';
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  id: number /**用户id */;
  avatar?: McUploadFile /**头像 */;
} & Partial<
  Pick<
    UserJSON,
    | 'birthPlace'
    | 'birthday'
    | 'company'
    | 'department'
    | 'departmentId'
    | 'email'
    | 'gender'
    | 'jobDescriptions'
    | 'login'
    | 'mobileNumber'
    | 'name'
    | 'nameEn'
    | 'signature'
    | 'state'
    | 'supervisorUid'
    | 'subSupervisorUid'
    | 'title'
    | 'type'
    | 'workplace'
    | 'remarks'
    | 'hiredDate'
    | 'joinWorkingDate'
    | 'userShifts'
    | 'idc'
    | 'blockGuid'
    | 'jobLabel'
    | 'blockGuids'
    | 'certNo'
    | 'ptBlockGuids'
    | 'married'
    | 'householdType'
    | 'politicalStatus'
    | 'ethnicGroup'
    | 'emergencyContactName'
    | 'emergencyContactMobile'
    | 'homeAddress'
    | 'academicQualifications'
    | 'workExperiences'
    | 'partyMemberInfo'
    | 'confirmationDate'
    | 'jobNumber'
  >
>;

export type SvcRespData = boolean;

export type RequestRespData = boolean;

export type ApiQ = {
  id: number;
  avatar?: BackendMcUploadFile;
} & Partial<
  Pick<
    BackendUser,
    | 'birthday'
    | 'company'
    | 'department'
    | 'departmentId'
    | 'director'
    | 'directorTwo'
    | 'email'
    | 'enable'
    | 'englishName'
    | 'loginName'
    | 'mobile'
    | 'nativePlace'
    | 'officeLocation'
    | 'position'
    | 'sexType'
    | 'signature'
    | 'userName'
    | 'userType'
    | 'jobDuties'
    | 'remarks'
    | 'joinWorkingTime'
    | 'hiredDate'
    | 'userShifts'
    | 'idc'
    | 'blockGuid'
    | 'jobLabel'
    | 'blockGuids'
    | 'certNo'
    | 'ptBlockGuids'
    | 'married'
    | 'householdType'
    | 'politicalStatus'
    | 'ethnicGroup'
    | 'emergencyContactName'
    | 'emergencyContactMobile'
    | 'homeAddress'
    | 'academicQualifications'
    | 'workExperiences'
    | 'partyMemberInfo'
    | 'confirmationDate'
    | 'jobNumber'
  >
>;

export type ApiR = Response<undefined>;
