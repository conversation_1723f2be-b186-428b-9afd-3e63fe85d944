/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './mutate-user';
import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './mutate-user.type';

/**
 * 更新用户
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1968)
 *
 * @param variant
 * @returns
 */
export async function mutateUser(user: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  // 更新之前先获取一遍用户信息，以让更新单个字段时，更新接口参数也为全传递
  const {
    data: originalUser,
    error: fetchUserError,
    ...others
  } = await fetchUser({ id: user.id, needValid: false });
  if (fetchUserError) {
    return {
      ...others,
      error: fetchUserError,
      data: false,
    };
  }
  user = { ...originalUser, ...user, email: user.email };
  const params: ApiQ = {
    id: user.id,
    avatar: user.avatar ? McUploadFile.toApiObject(user.avatar) : undefined,
    birthday: user.birthday,
    company: user.company,
    department: user.department,
    departmentId: user.departmentId,
    director: user.supervisorUid,
    directorTwo: user.subSupervisorUid,
    email: user.email,
    enable: user.state ? user.state === 'in-service' : undefined,
    englishName: user.nameEn,
    loginName: user.login,
    mobile: user.mobileNumber,
    nativePlace: user.birthPlace,
    officeLocation: user.workplace,
    position: user.title,
    jobDuties: user.jobDescriptions,
    sexType: user.gender,
    signature: user.signature,
    userName: user.name,
    userType: user.type,
    remarks: user.remarks,
    hiredDate: user.hiredDate,
    joinWorkingTime: user.joinWorkingDate,
    userShifts: user.userShifts,
    idc: user.idc,
    blockGuid: user.blockGuid,
    jobLabel: user.jobLabel,
    blockGuids: user.blockGuids,

    certNo: user.certNo,
    ptBlockGuids: user.ptBlockGuids,
    married: user.married,
    householdType: user.householdType,
    politicalStatus: user.politicalStatus,
    ethnicGroup: user.ethnicGroup,
    emergencyContactName: user.emergencyContactName,
    emergencyContactMobile: user.emergencyContactMobile,
    homeAddress: user.homeAddress,
    confirmationDate: user.confirmationDate,
    academicQualifications: user.academicQualifications,
    workExperiences: user.workExperiences,
    partyMemberInfo: user.partyMemberInfo,
    jobNumber: user.jobNumber,
  };

  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, ApiQ>(
    endpoint,
    params
  );
  if (error) {
    return {
      ...rest,
      error,
      data: false,
    };
  }

  return { error, data: true, ...rest };
}
