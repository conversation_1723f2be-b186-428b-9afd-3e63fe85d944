/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { Encrypt, generateEncryptPassword } from '@manyun/auth-hub.util.encrypt-password';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './create-users';
import type { ApiQ, SvcQuery, SvcRespData } from './create-users.type';

/**
 * 用户管理 新建用户
 *
 * @see [新建用户](http://172.16.0.17:13000/project/78/interface/api/1888)
 *
 * @param query
 * @returns
 */
export async function createUsers({
  multiFactorAuthentication,
  passwordType,
  password,
  resetPassword,
  userInfos,
  whetherEnable,
}: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  // In-case the `env.RSA_ENCRYPTION_PUBLIC_KEY` changes
  const encryptPassword = generateEncryptPassword(new Encrypt(env.RSA_ENCRYPTION_PUBLIC_KEY!));
  // 这里只能用 RSA 加密，因为创建用户后后端需要解密出明文密码发送通知给用户
  const encryptedPassword = encryptPassword(password!, 'RSA');
  const apiQ: ApiQ = {
    multiFactorAuthentication,
    passwordType,
    resetPassword,
    whetherEnable,
    userInfos: userInfos.map(user => {
      return {
        userName: user.name!,
        loginName: user.login!,
        mobile: user.mobileNumber!,
        email: user.email!,
        password: encryptedPassword,
        userType: user.type!,
        company: user.company!,
        department: user.department!,
        departmentId: user.departmentId!,
        director: user.supervisorUid!,
        directorTwo: user.subSupervisorUid ?? undefined,
        position: user.title!,
        sexType: user.gender!,
        userShifts: user.userShifts!,
        idc: user.idc,
        blockGuid: user.blockGuid,
        ptBlockGuids: user.ptBlockGuids,
        jobNumber: user.jobNumber,
        hiredDate: user.hiredDate,
      };
    }),
  };

  return webRequest.tryPost<SvcRespData, ApiQ>(endpoint, apiQ);
}
