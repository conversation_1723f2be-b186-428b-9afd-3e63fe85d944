/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { UserGender, UserJSON, UserShifts, UserType } from '@manyun/auth-hub.model.user';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type PasswordType = 'DEFAULT' | 'UNCHANGED' | 'CUSTOM';

export type SvcQuery = {
  multiFactorAuthentication?: boolean;
  passwordType: PasswordType;
  resetPassword?: boolean;
  userInfos: Partial<UserJSON>[];
  whetherEnable?: boolean;
  password?: string;
};

export type UserInfo = {
  userName: string;
  loginName: string;
  mobile: string;
  email: string;
  password: string;
  userType: UserType;
  company?: string;
  department?: string;
  departmentId?: string;
  director?: number;
  directorTwo?: number;
  position?: string;
  sexType: UserGender;
  userShifts?: UserShifts;
  idc?: string;
  blockGuid?: string;
  ptBlockGuids?: string[];
  jobNumber?: string;
  hiredDate?: number;
};

export type SvcRespData = boolean;

export type ApiQ = {
  multiFactorAuthentication?: boolean;
  passwordType: PasswordType;
  resetPassword?: boolean;
  userInfos: UserInfo[];
  whetherEnable?: boolean;
};

export type ApiR = WriteResponse;
