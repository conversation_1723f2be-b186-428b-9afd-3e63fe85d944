/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-21
 *
 * @packageDocumentation
 */
import type { BackendOperationLog, OperationLogJSON } from '@manyun/auth-hub.model.operation-log';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = Partial<{
  /** 日志 ID */
  id: number;
  /** 业务 ID */
  targetId: string;
  /** 业务模块 */
  targetType: string;
  /** 业务类型 */
  modifyType: string;
  /** 操作人名称 */
  operatorName: string;
  /** 操作内容 */
  content: string;
  startedAt: number;
  endedAt: number;
  operatorTimeAscSort?: boolean;
}> & {
  page: number;
  pageSize: number;
};

export type SvcRespData = {
  data: OperationLogJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendOperationLog[] | null;
  total: number;
} | null;

export type ApiQ = Partial<{
  /** 日志 ID */
  id: number;
  /** 业务 ID */
  targetId: string;
  /** 业务模块 */
  targetType: string;
  /** 业务类型 */
  modifyType: string;
  /** 操作人名称 */
  operatorName: string;
  /** 操作内容 */
  content: string;
  operatorTimeStart: number;
  operatorTimeEnd: number;
  operatorTimeAscSort?: boolean;
}> & {
  pageNum: number;
  pageSize: number;
};

export type ApiR = ListResponse<OperationLogJSON[] | null>;
