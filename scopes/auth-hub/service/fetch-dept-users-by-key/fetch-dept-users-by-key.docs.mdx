---
description: 'A fetchDeptUsersByKey HTTP API service.'
labels: ['service', 'http']
---

根据关键字查询部门用户

## Usage

### Browser

```ts
import { fetchDeptUsersBy<PERSON>ey } from '@manyun/auth-hub.service.fetch-dept-users-by-key';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchDeptUsersByKeyService } from '@manyun/auth-hub.service.fetch-dept-users-by-key/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchDeptUsersByKeyService.from(nodeRequest);
```
