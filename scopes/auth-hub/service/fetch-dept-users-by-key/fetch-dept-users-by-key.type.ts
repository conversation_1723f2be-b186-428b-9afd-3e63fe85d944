/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 关键字
   */
  key?: string;
};

export type SimperUSer = {
  /**
   * id
   */
  id: number;
  /**
   * 姓名
   */
  userName: string;
  /**
   * 登录名
   */
  loginName: string;
  /**
   * 工号
   */
  jobNumber: string;
  /**
   * 手机
   */
  mobile: string;
  /**
   * 邮箱
   */
  email: string;
};

export type ApiResponse = ListBackendResponse<SimperUSer[]>;
