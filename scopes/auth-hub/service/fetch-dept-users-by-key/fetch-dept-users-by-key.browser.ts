/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-dept-users-by-key.js';
import type { ApiArgs, ApiResponse } from './fetch-dept-users-by-key.type.js';

/**
 * @param args
 * @returns
 */
export function fetchDeptUsersByKey(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
