/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { BackendUser, UserJSON } from '@manyun/auth-hub.model.user';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  /**用户登陆名 */
  name?: string;
  /**用户id */
  id?: number;
  /**
   * 是否需要限制 当前用户为员工类型才能查看用户信息,不传默认为true
   *  - `true`: 当前登录人用户类型为员工接口返回`UserJSON`，否则返回`null`
   *  - `false`: 接口返回`UserJSON`
   */
  needValid?: boolean;
};

export type SvcRespData =
  | (UserJSON & {
      location?: string | null;
      /** 是否自 MDM 同步的数据 */
      fromMdm: boolean;
      //工龄 单位：月
      workingAge?: number | null;
      //司龄 单位：月
      serviceAge?: number | null;
    })
  | null;

export type RequestRespData =
  | (BackendUser & {
      location?: string | null;
      /**
       * 创建渠道
       * - LOCAL（手动创建）
       * - MDM（MDM同步）
       */
      createChannel: 'LOCAL' | 'MDM';
      workingAge?: number | null;
      serviceAge?: number | null;
    })
  | null;

export type ApiQ = {
  loginName?: string;
  userId?: number;
  needValid?: boolean;
};

export type ApiR = Response<RequestRespData>;
