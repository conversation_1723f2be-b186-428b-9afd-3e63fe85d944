/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { User } from '@manyun/auth-hub.model.user';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-user';
import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-user.type';

/**
 * 查询用户信息详情
 *
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/2016)
 *
 * @param variant
 * @returns
 */
export async function fetchUser({
  id,
  name,
  needValid,
}: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = {
    loginName: name,
    userId: id,
    needValid,
  };

  const { error, data, ...rest } = await webRequest.tryGet<RequestRespData, ApiQ>(endpoint, {
    params,
  });

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: null,
    };
  }

  return {
    error,
    data: {
      ...User.fromApiObject(data).toJSON(),
      workingAge: data.workingAge,
      serviceAge: data.serviceAge,
      location: data.location,
      fromMdm: data.createChannel === 'MDM',
    },
    ...rest,
  };
}
