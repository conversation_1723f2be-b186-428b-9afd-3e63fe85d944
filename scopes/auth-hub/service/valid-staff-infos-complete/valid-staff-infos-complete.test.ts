/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */

import {
  WebRequest,
  request,
  setupRemoteMocks,
  setupRequestSingleton,
} from '@glpdev/symphony.services.request';
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';

import { validStaffInfosComplete as webService } from './valid-staff-infos-complete.browser.js';
import { ValidStaffInfosCompleteService } from './valid-staff-infos-complete.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ValidStaffInfosCompleteService.from(nodeRequest);

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  setupRequestSingleton(WebRequest.from());
  webMockOff = setupRemoteMocks(request!);
  nodeMockOff = setupRemoteMocks(nodeRequest);
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService();

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService();

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService();

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
