/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './valid-staff-infos-complete.type.js';

const endpoint = '/pm/validComplete';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30724)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> => {
    return request.tryPost<ApiResponse, ApiArgs>(endpoint, args);
  };
}
