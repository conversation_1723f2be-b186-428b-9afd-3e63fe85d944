---
description: 'A validStaffInfosComplete HTTP API service.'
labels: ['service', 'http']
---

校验用户信息是否完整

## Usage

### Browser

```ts
import { validStaffInfosComplete } from '@manyun/iam.service.valid-staff-infos-complete';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { ValidStaffInfosCompleteService } from '@manyun/iam.service.valid-staff-infos-complete/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ValidStaffInfosCompleteService.from(nodeRequest);
```
