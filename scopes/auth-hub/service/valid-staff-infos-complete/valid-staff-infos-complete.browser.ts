/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './valid-staff-infos-complete.js';
import type { ApiArgs, ApiResponse } from './valid-staff-infos-complete.type.js';

/**
 * @param args
 * @returns
 */
export function validStaffInfosComplete(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
