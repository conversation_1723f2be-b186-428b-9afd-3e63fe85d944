/* eslint-disable @typescript-eslint/no-explicit-any */
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useApolloClient } from '@apollo/client';
import omit from 'lodash.omit';
import moment from 'moment';
import React from 'react';
import { useHistory } from 'react-router-dom';
import shortid from 'shortid';

import type { UserJSON } from '@manyun/auth-hub.model.user';
import { DEFAULT_USER_PASSWORD_VALIDATION_RULES } from '@manyun/auth-hub.model.user';
import { USERS_ROUTE_PATH } from '@manyun/auth-hub.route.auth-routes';
import type { SvcQuery } from '@manyun/auth-hub.service.create-users';
import { createUsers } from '@manyun/auth-hub.service.create-users';
import { DeptText } from '@manyun/auth-hub.ui.dept-text';
import { DeptTreeSelect } from '@manyun/auth-hub.ui.dept-tree-select';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserGenderSelect, UserGenderText } from '@manyun/auth-hub.ui.user-gender';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserTypeSelect, UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { EditableTable } from '@manyun/base-ui.ui.editable-table';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { usePerformanceDefaultDepartments } from '@manyun/hrm.gql.client.hrm';
import { UserShiftsSelect, UserShiftsText } from '@manyun/hrm.ui.user-shifts';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import type { Space as SpaceType } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';

import styles from './users-creator.module.less';

export type UsersCreatorProps = {};

const DEFAULT_PASSWORD = '123456';
export type EditUser = Partial<UserJSON> & { draftKey: string };

// eslint-disable-next-line no-empty-pattern
export function UsersCreator({}: UsersCreatorProps) {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = React.useState<EditUser[]>([]);
  const [editingRowKey, setEditingRowKey] = React.useState<string | null>(null);
  const [deleteByCancel, setDeleteByCancel] = React.useState<boolean>(false);
  const [loading, setLoading] = React.useState(false);
  const [idcs, setIdcs] = React.useState<SpaceType[]>();
  const history = useHistory();
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  const { data: performanceDefaultDepartments } = usePerformanceDefaultDepartments();
  const [configUtil] = useConfigUtil();
  const isPlusUser =
    (configUtil.getScopeCommonConfigs as any)('iam')?.userProfile?.version === 'plus';

  const onAdd = () => {
    const draftKey = shortid();
    setDataSource([{ draftKey, type: 'STAFF' }, ...dataSource]);
    setEditingRowKey(draftKey);
    setDeleteByCancel(true);
  };

  const onHandleClick = React.useCallback(() => {
    const params: SvcQuery = {
      passwordType: 'CUSTOM',
      userInfos: dataSource.map(user => {
        const trimmedData = trimData(omit(user, ['draftKey', 'ptBlockGuids', 'hiredDate']));
        return {
          ...trimmedData,
          jobNumber: trimmedData?.jobNumber === '' ? undefined : trimmedData.jobNumber,
          hiredDate: user.hiredDate ? moment(user.hiredDate).startOf('day').valueOf() : undefined,
          ptBlockGuids:
            (user.ptBlockGuids ?? []).length > 0 ? user.ptBlockGuids?.flat() : undefined,
        };
      }),
      password: DEFAULT_PASSWORD,
    };
    form
      .validateFields()
      .then(values => {
        params.password = values.password;
        create(params);
      })
      .catch(err => {
        form.scrollToField(err.errorFields[0].name.toString(), {
          behavior: actions => {
            actions.forEach(action => {
              action.el.scrollTop = action.top - 55;
            });
          },
        });
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource]);
  const formPassword = Form.useWatch('password', form);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card title="新建用户">
        <Space style={{ width: '100%' }} direction="vertical">
          <Form form={form}>
            <Form.Item
              label="登录密码"
              name="password"
              validateFirst
              extra={
                !formPassword
                  ? '长度至少 8 位，至多 64 位，必须同时包含大小写字母、特殊字符和数字，不允许有空格'
                  : undefined
              }
              rules={DEFAULT_USER_PASSWORD_VALIDATION_RULES}
            >
              <Input.Password style={{ width: 320 }} />
            </Form.Item>
          </Form>

          <div style={{ paddingBottom: 24 }}>用户信息</div>
          <Button type="primary" disabled={deleteByCancel} onClick={onAdd}>
            添加
          </Button>
          <EditableTable
            size="small"
            rowKey="draftKey"
            showActionsColumn
            mergeProp="draftKey"
            scroll={{ x: true }}
            columns={
              [
                {
                  title: withRequiredMarkText('用户ID'),
                  dataIndex: 'login',
                  editable: true,
                  fixed: 'left',
                  editingCtrl: (
                    <Input style={{ width: 220 }} placeholder="如为正式员工，请输入邮箱前缀" />
                  ),
                  formItemProps: {
                    rules: [
                      { required: true, whitespace: true, message: '请输入用户ID' },
                      { max: 24, message: '最多输入 24 个字符！' },
                      {
                        pattern: /^[a-zA-Z0-9-_]+$/,
                        message: '用户ID必须是英文、数字、“-”或“_”',
                      },
                      () => ({
                        validator(_: any, value: string | undefined) {
                          const data = dataSource.find(({ login }) => login === value);
                          if (data && data.draftKey !== editingRowKey) {
                            return Promise.reject('重复请重新输入');
                          }
                          return Promise.resolve();
                        },
                      }),
                    ],
                  },
                },
                {
                  title: withRequiredMarkText('姓名'),
                  dataIndex: 'name',
                  editable: true,
                  fixed: 'left',
                  editingCtrl: <Input style={{ width: 120 }} />,
                  formItemProps: {
                    rules: [
                      { required: true, whitespace: true, message: '请输入姓名' },
                      { max: 10, message: '最多输入 10 个字符！' },
                    ],
                  },
                },

                {
                  title: withRequiredMarkText('性别'),
                  dataIndex: 'gender',
                  editable: true,
                  editingCtrl: <UserGenderSelect style={{ width: 120 }} />,
                  formItemProps: {
                    rules: [{ required: true, message: '请选择性别' }],
                  },
                  render: (_: any, { gender }: any) => <UserGenderText userGender={gender} />,
                },
                {
                  title: withRequiredMarkText('手机号码'),
                  dataIndex: 'mobileNumber',
                  editable: true,
                  editingCtrl: <Input style={{ width: 120 }} />,
                  formItemProps: {
                    rules: [
                      { required: true, message: '请输入手机号码' },
                      {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '手机号格式错误，请重新输入',
                      },
                      { max: 11, message: '最多输入 11 个字符！' },
                    ],
                  },
                },
                {
                  title: withRequiredMarkText('邮箱'),
                  dataIndex: 'email',
                  editable: true,
                  editingCtrl: <Input style={{ width: 200 }} />,
                  formItemProps: {
                    rules: [
                      { required: true, message: '请输入邮箱' },
                      {
                        type: 'email',
                        message: '请输入正确邮箱',
                      },
                      { max: 40, message: '最多输入 40 个字符！' },
                    ],
                  },
                },
                {
                  title: (
                    <>
                      {withRequiredMarkText('类型 ')}
                      <Tooltip title="正式员工请选择“员工”，外包员工请选择“外包人员”">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </>
                  ),
                  dataIndex: 'type',
                  editable: true,
                  editingCtrl: (_: any, { form }: any) => {
                    return (
                      <UserTypeSelect
                        style={{ width: 120 }}
                        onChange={value => {
                          forceUpdate();
                          form.setFieldsValue({ company: undefined });
                          if (value === 'CUSTOMER' || value === 'VENDOR') {
                            form.setFieldsValue({
                              idc: undefined,
                              blockGuid: undefined,
                              ptBlockGuids: undefined,
                            });
                          }
                        }}
                      />
                    );
                  },
                  formItemProps: {
                    rules: [{ required: true, message: '请选择类型' }],
                  },
                  render: (_: any, { type }: any) => <UserTypeText userType={type} />,
                },
                {
                  title: '工号',
                  dataIndex: 'jobNumber',
                  editable: true,
                  editingCtrl: <Input style={{ width: 120 }} />,
                  formItemProps: {
                    dependencies: ['type'],
                    rules: [
                      ({ getFieldValue }: any) => ({
                        validator: (_: any, value: any) => {
                          const type = getFieldValue('type');
                          if (type === 'STAFF' && (!value || value.trim().length === 0)) {
                            return Promise.reject('请输入工号');
                          }

                          return Promise.resolve();
                        },
                      }),
                      { max: 20, message: '最多输入 20 个字符！' },
                    ],
                  },
                  show: isPlusUser,
                  render: (val: string) => (val?.trim() === '' ? '--' : (val ?? '--')),
                },
                {
                  title: '入职日期',
                  dataIndex: 'hiredDate',
                  editable: true,
                  editingCtrl: (_: any, { form }: any) => {
                    return (
                      <DatePicker
                        style={{ width: 140 }}
                        disabledDate={current => {
                          if (!current) {
                            return false;
                          }
                          const today = moment().endOf('day');
                          return current.isAfter(today);
                        }}
                      />
                    );
                  },
                  formItemProps: {
                    dependencies: ['type'],
                    rules: [
                      ({ getFieldValue }: any) => ({
                        validator: (_: any, value: any) => {
                          const type = getFieldValue('type');
                          if (type === 'STAFF' && !value) {
                            return Promise.reject('请选择入职日期');
                          }

                          return Promise.resolve();
                        },
                      }),
                    ],
                  },
                  render: (_: any, { hiredDate }: any) =>
                    hiredDate ? moment(hiredDate).format('YYYY-MM-DD') : '--',
                  show: isPlusUser,
                },
                {
                  title: '所属机房',
                  dataIndex: 'idc',
                  editable: true,
                  formItemProps: {
                    dependencies: ['departmentId', 'type'],
                    rules: [
                      ({ getFieldValue }: any) => ({
                        validator: (_: any, value: any) => {
                          const type = getFieldValue('type');
                          const departmentId = getFieldValue('departmentId');

                          if (isPlusUser && type === 'STAFF' && !value) {
                            return Promise.reject('请选择所属机房');
                          }

                          if (
                            departmentId &&
                            (
                              performanceDefaultDepartments?.performanceDefaultDepartments?.data ??
                              []
                            ).includes(departmentId) &&
                            type === 'STAFF' &&
                            !value
                          ) {
                            //类型为员工且部门所属为年度绩效部门，所属机房必填
                            return Promise.reject('请选择所属机房');
                          }

                          return Promise.resolve();
                        },
                      }),
                    ],
                  },
                  editingCtrl: ({ type }: any, { form }: any) => (
                    <LocationCascader
                      style={{ width: 200 }}
                      nodeTypes={['IDC']}
                      disabled={type === 'CUSTOMER' || type === 'VENDOR'}
                      allowClear
                      onTreeDataChange={(data: SpaceType[]) => {
                        setIdcs(data);
                      }}
                      onChange={() => {
                        form.setFieldsValue({ blockGuid: undefined, ptBlockGuids: undefined });
                      }}
                    />
                  ),
                  render: (idc: any) =>
                    idcs?.find((i: { value: string }) => i.value === idc)?.label || '--',
                },
                {
                  title: '主楼栋',
                  dataIndex: 'blockGuid',
                  editable: true,
                  show: !isPlusUser,
                  editingCtrl: ({ idc }: any) => {
                    return (
                      <LocationCascader
                        style={{ width: 200 }}
                        idc={Array.isArray(idc) ? idc[0] : idc}
                        nodeTypes={['BLOCK']}
                        disabled={!idc}
                        allowClear
                      />
                    );
                  },
                  render: (blockGuid: any) =>
                    blockGuid ? <RenderBlockLabel blockGuid={blockGuid} /> : '--',
                },
                {
                  title: '所属楼栋',
                  dataIndex: 'ptBlockGuids',
                  editable: true,
                  show: isPlusUser,
                  formItemProps: {
                    dependencies: ['type', 'idc'],
                    rules: [
                      ({ getFieldValue }: any) => ({
                        validator: (_: any, value: any) => {
                          const type = getFieldValue('type');
                          const isEmpty = Array.isArray(value) ? value.length === 0 : !value;
                          if (type === 'STAFF' && isEmpty) {
                            return Promise.reject('请选择所属楼栋');
                          }

                          return Promise.resolve();
                        },
                      }),
                    ],
                  },
                  editingCtrl: ({ idc }: any, { form }: any) => {
                    return (
                      <LocationCascader
                        style={{ width: 200 }}
                        idc={Array.isArray(idc) ? idc[0] : idc}
                        nodeTypes={['BLOCK']}
                        disabled={!idc}
                        maxTagCount="responsive"
                        multiple
                        allowClear
                      />
                    );
                  },
                  render: (ptBlockGuids: string[]) => {
                    return ptBlockGuids ? (
                      <Space size={4} split="｜">
                        {ptBlockGuids.flat().map((blockGuid: string) => (
                          <RenderBlockLabel key={blockGuid} blockGuid={blockGuid} />
                        ))}
                      </Space>
                    ) : (
                      '--'
                    );
                  },
                },
                {
                  title: '所属公司',
                  dataIndex: 'company',
                  editable: true,
                  editingCtrl: ({ type }: any) => {
                    return <Input disabled={type === 'STAFF'} />;
                    // return type === 'CUSTOMER' ? (
                    //   <CustomersOnRacksSelect style={{ width: 200 }} fieldNames={{ value: 'name' }} />
                    // ) : (
                    //   <VendorSelect
                    //     disabled={type === 'STAFF'}
                    //     style={{ width: 200 }}
                    //     getPopupContainer={undefined}
                    //   />
                    // );
                  },
                  formItemProps: {
                    dependencies: ['type'],
                    rules: [{ max: 16, message: '最多输入 16 个字符！' }],
                    // rules: [
                    //   ({ getFieldValue }) => ({
                    //     validator(_, value) {
                    //       if (!value && getFieldValue('type') !== 'STAFF') {
                    //         return Promise.reject('请选择所属公司');
                    //       }
                    //       return Promise.resolve();
                    //     },
                    //   }),
                    // ],
                  },
                  render: (val: string) => val ?? '--',
                },
                {
                  title: '部门',
                  dataIndex: 'departmentId',
                  editable: true,
                  editingCtrl: <DeptTreeSelect style={{ width: 200 }} />,
                  render: (_: any, { departmentId }: any) => <DeptText deptId={departmentId} />,
                },
                {
                  title: '岗位',
                  dataIndex: 'title',
                  editable: true,
                  editingCtrl: isPlusUser ? (
                    <MetaTypeSelect
                      style={{ width: 200 }}
                      metaType={'POSITION_YG' as any}
                      allowClear
                    />
                  ) : (
                    <Input style={{ width: 200 }} />
                  ),
                  render: (_: any, { title }: any) =>
                    isPlusUser ? (
                      <MetaTypeText metaType={'POSITION_YG' as any} code={title} defaultShow />
                    ) : (
                      title
                    ),
                  formItemProps: {
                    dependencies: ['type'],
                    rules: [
                      ({ getFieldValue }: any) => ({
                        validator: (_: any, value: any) => {
                          const type = getFieldValue('type');
                          if (isPlusUser && type === 'STAFF' && !value) {
                            return Promise.reject('请选择岗位');
                          }
                          if (!isPlusUser && value && value.length > 20) {
                            return Promise.reject('最多输入 20 个字符！');
                          }
                          return Promise.resolve();
                        },
                      }),
                    ],
                  },
                },
                {
                  title: '直线经理1',
                  dataIndex: 'supervisorUid',
                  editable: true,
                  formItemProps: {
                    dependencies: ['type'],
                    rules: [
                      ({ getFieldValue }: any) => ({
                        validator: (_: any, value: any) => {
                          const type = getFieldValue('type');
                          if (isPlusUser && type === 'STAFF' && !value) {
                            return Promise.reject('请选择直线经理1');
                          }

                          return Promise.resolve();
                        },
                      }),
                    ],
                  },
                  editingCtrl: (
                    <UserSelect
                      style={{ width: 120 }}
                      allowClear
                      labelInValue={false}
                      userState="in-service"
                    />
                  ),
                  render: (_: any, { supervisorUid }: any) => <UserLink id={supervisorUid} />,
                },
                {
                  title: '直线经理2',
                  dataIndex: 'subSupervisorUid',
                  editable: true,
                  editingCtrl: (
                    <UserSelect
                      style={{ width: 120 }}
                      allowClear
                      labelInValue={false}
                      userState="in-service"
                    />
                  ),
                  render: (_: any, { subSupervisorUid }: any) => <UserLink id={subSupervisorUid} />,
                },
                {
                  title: '工时类型',
                  dataIndex: 'userShifts',
                  editable: true,
                  editingCtrl: <UserShiftsSelect allowClear style={{ width: 200 }} />,
                  render: (_: any, { userShifts }: any) =>
                    userShifts ? <UserShiftsText userShifts={userShifts} /> : '--',
                },
              ].filter(item => item.show !== false) as any[]
            }
            dataSource={dataSource}
            editingRowKey={editingRowKey}
            canDelete={_ => true}
            onEdit={(rowKey: string) => {
              setEditingRowKey(rowKey);
              setDeleteByCancel(true);
            }}
            onCancel={() => {
              if (deleteByCancel) {
                setDataSource(prevData => {
                  const nextData = [...prevData];
                  const idx = nextData.findIndex(record => record.draftKey === editingRowKey);
                  if (idx > -1) {
                    nextData.splice(idx, 1);
                  }

                  return nextData;
                });
              }
              setEditingRowKey(null);
              setDeleteByCancel(false);
            }}
            onSave={(__, data: EditUser[]) => {
              setDataSource(
                data.map(i => ({
                  ...i,
                  idc: Array.isArray(i.idc) ? i.idc.at(-1) : i.idc,
                  blockGuid: Array.isArray(i.blockGuid) ? i.blockGuid[0] : i.blockGuid,
                }))
              );
              setEditingRowKey(null);
              setDeleteByCancel(false);
            }}
            onDelete={(rowKey: string, data: EditUser[]) => {
              setDataSource(data);
              if (rowKey === editingRowKey) {
                setEditingRowKey(null);
              }
            }}
          />
        </Space>
      </Card>
      <Card bodyStyle={{ display: 'flex', alignContent: 'center', justifyContent: 'center' }}>
        <Button
          type="primary"
          style={{ marginRight: 24 }}
          loading={loading}
          disabled={deleteByCancel || dataSource.length === 0}
          onClick={() => {
            onHandleClick();
          }}
        >
          提交
        </Button>
        <Button
          onClick={() => {
            history.goBack();
          }}
        >
          取消
        </Button>
      </Card>
    </Space>
  );

  async function create(params: SvcQuery) {
    setLoading(true);
    const { error } = await createUsers(params);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('新建成功');
    history.push(USERS_ROUTE_PATH);
  }
}

function trimData(data: Object) {
  const params = Object.entries(data).reduce((map, item) => {
    const [fieldName, value] = item;
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (fieldName === 'supervisorUid' || fieldName === 'subSupervisorUid') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (map as any)[fieldName] = value;
    } else {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (map as any)[fieldName] = value.trim();
    }
    return map;
  }, {});
  return params as EditUser;
}

function withRequiredMarkText(text: React.ReactNode) {
  return (
    <>
      <span className={styles.requiredMark}>*</span>
      {text}
    </>
  );
}

function RenderBlockLabel({ blockGuid }: { blockGuid: string }) {
  const client = useApolloClient();
  const blockLabel = readSpace(client, blockGuid);
  return <>{blockLabel ? blockLabel?.label : (blockGuid ?? '--')}</>;
}
