/* eslint-disable @typescript-eslint/no-explicit-any */
import orderBy from 'lodash.orderby';
import moment from 'moment';
import { nanoid } from 'nanoid';
import React from 'react';

import type { OperationLogJSON } from '@manyun/auth-hub.model.operation-log';
import { fetchOperationLogList } from '@manyun/auth-hub.service.fetch-operation-log-list';
import type { SvcRespData as UserDetail } from '@manyun/auth-hub.service.fetch-user';
import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  PoliticalStatusTextMapper,
  StaffProfileEditor,
  isLongTerm,
} from '@manyun/hrm.ui.staff-profile-editor';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';

import { UserContact } from '../user-contact';

export type UserBioPlusProps = {
  user?: UserDetail;
  supervisorName?: string;
  canEdit?: boolean;
  openUserEditorModal?: boolean;
  onRefresh?: () => void;
  onClose?: () => void;
};

export function UserBioPlus({
  user,
  supervisorName,
  openUserEditorModal,
  canEdit,
  onRefresh,
  onClose,
}: UserBioPlusProps) {
  const showPartInfos = React.useMemo(() => user?.politicalStatus === 'PARTY_MEMBER', [user]);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        gap: '24px',
      }}
    >
      <Space
        style={{
          flex: 1,
          height: canEdit ? 'calc(100% - 52px)' : 'calc(100% - 16px)',
          overflowY: 'auto',
        }}
        direction="vertical"
        size={32}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            基本信息
          </Typography.Title>
          <DescriptionsWrapper
            items={[
              {
                key: 'userName',
                label: '姓名',
                value: user?.name ?? '--',
              },
              {
                key: 'idNo',
                label: '身份证号',
                value: maskIdCard(user?.certNo),
              },
              {
                key: 'email',
                label: '联系方式',
                value: user ? <UserContact user={user} editable={false} /> : '--',
              },
              {
                key: 'maritalStatus',
                label: '婚姻状况',
                value:
                  user?.married !== undefined && user?.married !== null
                    ? user?.married
                      ? '已婚'
                      : '未婚'
                    : '--',
              },
              {
                key: 'birthday',
                label: '出生年月',
                value: user?.birthday ? toMoment(user.birthday, 'YYYY-MM') : '--',
              },

              {
                key: 'region',
                label: '户籍类型',
                value: user?.householdType ?? '--',
              },
              {
                key: 'politicalStatus',
                label: '政治面貌',
                value: user?.politicalStatus
                  ? (PoliticalStatusTextMapper[user.politicalStatus] ?? '--')
                  : '--',
              },
              {
                key: 'ethnic',
                label: '民族',
                value: user?.ethnicGroup ?? '--',
              },
              {
                key: 'emergencyContactName',
                label: '紧急联系人姓名',
                ellipsis: true,
                value: user?.emergencyContactName ?? '--',
              },
              {
                key: 'emergencyContactPhone',
                label: '紧急联系人电话',
                value: user?.emergencyContactMobile ?? '--',
              },
              {
                key: 'address',
                label: '家庭住址',
                ellipsis: true,
                value: user?.homeAddress ?? '--',
                span: 2,
              },
            ]}
          />
        </Space>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            岗位信息
          </Typography.Title>
          <DescriptionsWrapper
            items={[
              {
                key: 'ptBlockGuids',
                label: '所属楼栋',
                ellipsis: true,
                value:
                  (user?.ptBlockGuids ?? []).length > 0
                    ? (user?.ptBlockGuids ?? []).join('｜')
                    : '--',
              },
              {
                key: 'position',
                label: '岗位',
                value: user?.title ? (
                  <MetaTypeText metaType={'POSITION_YG' as any} code={user.title} defaultShow />
                ) : (
                  '--'
                ),
                ellipsis: true,
              },
              {
                key: 'supervisorUid',
                label: '直线经理',
                value: user?.supervisorUid ? supervisorName : '--',
              },
              { key: 'workPlace', label: '职称', value: user?.jobDescriptions || '--' },
              {
                key: 'staffId',
                label: '工号',
                value: user?.jobNumber || '--',
              },
              {
                key: 'entryDate',
                label: '入职日期',
                value: user?.hiredDate ? moment(user.hiredDate).format('YYYY-MM-DD') : '--',
              },
              {
                key: 'startWorkTime',
                label: '开始工作时间',
                value: user?.joinWorkingDate
                  ? moment(user.joinWorkingDate).format('YYYY-MM')
                  : '--',
              },
              {
                key: 'regularDate',
                label: '转正日期',
                value: user?.confirmationDate
                  ? moment(user.confirmationDate).format('YYYY-MM-DD')
                  : '--',
              },
              {
                key: 'workingAge',
                label: '工龄',
                value: formatMonthsToYearMonth(user?.workingAge ?? undefined),
              },
              {
                key: 'companyAge',
                label: '司龄',
                value: formatMonthsToYearMonth(user?.serviceAge ?? undefined),
              },
            ]}
          />
        </Space>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            学历信息
          </Typography.Title>
          {user && (user?.academicQualifications ?? []).length > 0 ? (
            <Space style={{ width: '100%' }} direction="vertical" size="middle">
              {(user.academicQualifications ?? []).map(item => {
                return (
                  <div key={nanoid()} style={{ width: '100%', display: 'flex', gap: '24px' }}>
                    <Typography.Text style={{ width: 140 }}>
                      {item.admissionDate
                        ? `${moment(item.admissionDate).format('YYYY-MM')}`
                        : '--'}
                      ～
                      {item.graduationDate
                        ? `${moment(item.graduationDate).format('YYYY-MM')}`
                        : '--'}
                    </Typography.Text>
                    <Typography.Text style={{ maxWidth: 200 }} ellipsis={{ tooltip: item.school }}>
                      {item.school ?? '--'}
                    </Typography.Text>
                    <Typography.Text
                      style={{ maxWidth: 200 }}
                      ellipsis={{ tooltip: item.educationalDegree }}
                    >
                      {item.educationalDegree ?? '--'}
                    </Typography.Text>
                    <Typography.Text
                      style={{ flex: 1, display: 'inline-block', width: 0, minWidth: 0 }}
                      ellipsis={{ tooltip: item.major }}
                    >
                      {item.major ?? '--'}
                    </Typography.Text>
                  </div>
                );
              })}
            </Space>
          ) : (
            <Empty description="暂无学历信息" />
          )}
        </Space>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            工作经历
          </Typography.Title>
          {user && (user?.workExperiences ?? []).length > 0 ? (
            <Space style={{ width: '100%' }} direction="vertical" size="middle">
              {(user.workExperiences ?? []).map(item => {
                return (
                  <div key={nanoid()} style={{ width: '100%', display: 'flex', gap: '24px' }}>
                    <Typography.Text style={{ width: 140 }}>
                      {item.hiredDate ? `${moment(item.hiredDate).format('YYYY-MM')}` : '--'}～
                      {item.resignDate
                        ? isLongTerm(item.resignDate)
                          ? '至今'
                          : `${moment(item.resignDate).format('YYYY-MM')}`
                        : '--'}
                    </Typography.Text>
                    <Typography.Text style={{ maxWidth: 200 }} ellipsis={{ tooltip: item.company }}>
                      {item.company ?? '--'}
                    </Typography.Text>
                    <Typography.Text
                      style={{ maxWidth: 200 }}
                      ellipsis={{ tooltip: item.position }}
                    >
                      {item.position ?? '--'}
                    </Typography.Text>
                    <Typography.Text
                      style={{ flex: 1, display: 'inline-block', width: 0, minWidth: 0 }}
                      ellipsis={{ tooltip: item.jobDescription }}
                    >
                      {item.jobDescription ?? '--'}
                    </Typography.Text>
                  </div>
                );
              })}
            </Space>
          ) : (
            <Empty description="暂无工作经历" />
          )}
        </Space>
        {showPartInfos && (
          <Space style={{ width: '100%' }} direction="vertical" size={16}>
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              党员信息
            </Typography.Title>
            <DescriptionsWrapper
              items={[
                {
                  key: 'partyEntryDate',
                  label: '入党时间',
                  value: user?.partyMemberInfo?.joinDate
                    ? moment(user.partyMemberInfo.joinDate).format('YYYY-MM-DD')
                    : '--',
                },
                {
                  key: 'partyBranchName',
                  label: '所在党支部名称',
                  value: user?.partyMemberInfo?.partyBranchName ?? '--',
                  ellipsis: true,
                },
                {
                  key: 'partyPosition',
                  label: '党内职务',
                  value: user?.partyMemberInfo?.partyPosition ?? '--',
                  ellipsis: true,
                },
                {
                  key: 'partyMemberStatus',
                  label: '是否流动党员',
                  value:
                    user?.partyMemberInfo?.floating !== null &&
                    user?.partyMemberInfo?.floating !== undefined
                      ? user?.partyMemberInfo?.floating
                        ? '是'
                        : '否'
                      : '--',
                  ellipsis: true,
                },
                {
                  key: 'partyTransfer',
                  label: '组织关系转入/转出',
                  value: user?.partyMemberInfo?.partyRelationshipInfo ?? '--',
                  ellipsis: true,
                },
                {
                  key: 'remarks',
                  label: '备注',
                  value: user?.partyMemberInfo?.remark ?? '--',
                  ellipsis: true,
                },
              ]}
            />
          </Space>
        )}
        {user && canEdit && (
          <StaffProfileEditor
            staffId={user.id}
            defaultOpen={openUserEditorModal}
            onSuccess={() => {
              onRefresh?.();
            }}
            onClose={() => {
              onClose?.();
            }}
          >
            <Button style={{ position: 'absolute', bottom: 16, left: 24 }} type="primary">
              编辑个人信息
            </Button>
          </StaffProfileEditor>
        )}
      </Space>
      <Container style={{ width: 280, height: 'calc(100% - 52px)' }} size="large" color="default">
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            gap: 24,
          }}
        >
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            员工动态时间轴
          </Typography.Title>
          {user && (
            <LogSteps
              staffId={user.id.toString()}
              style={{
                height: 'calc(100% - 52px )',
                overflowY: 'auto',
              }}
            />
          )}
        </div>
      </Container>
    </div>
  );
}

const DescriptionsWrapper = ({
  items,
  labelStyle,
}: {
  items: {
    key: string;
    label: string;
    value: React.ReactNode;
    span?: number;
    ellipsis?: boolean;
  }[];
  labelStyle?: React.CSSProperties;
}) => {
  return (
    <Descriptions colon={false} column={2}>
      {items.map(item => (
        <Descriptions.Item
          key={item.key}
          label={<Typography.Text type="secondary">{item.label}:</Typography.Text>}
          span={item.span || 1}
          labelStyle={
            labelStyle
              ? labelStyle
              : {
                  width: 133,
                }
          }
        >
          {item.ellipsis ? (
            <Typography.Text
              style={{ display: 'inline-block', flex: 1, width: 0, minWidth: 0 }}
              ellipsis={{ tooltip: item.value }}
            >
              {item.value}
            </Typography.Text>
          ) : (
            item.value
          )}
        </Descriptions.Item>
      ))}
    </Descriptions>
  );
};

/**
 * 将月份转换为年月显示
 * @param months 月份数
 * @returns 格式化后的年月字符串
 */
const formatMonthsToYearMonth = (months: number | undefined): string => {
  if (months === undefined || months === null) {
    return '--';
  }

  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;

  if (years && remainingMonths) {
    return `${years}年${remainingMonths}个月`;
  } else if (years) {
    return `${years}年`;
  } else {
    return `${remainingMonths}个月`;
  }
};

const toMoment = (date: string | number | undefined, format: string): string | undefined => {
  if (!date) {
    return undefined;
  }

  if (!isNaN(Number(date))) {
    return moment(Number(date)).format(format);
  }

  const parsed = moment(date);
  return parsed.isValid() ? parsed.format(format) : undefined;
};

const LogSteps = ({ staffId, style }: { staffId: string; style?: React.CSSProperties }) => {
  const [loading, setLoading] = React.useState(false);
  const [list, setList] = React.useState<OperationLogJSON[]>([]);

  React.useEffect(() => {
    if (staffId) {
      (async () => {
        setLoading(true);
        const { error, data } = await fetchOperationLogList({
          page: 1,
          pageSize: 1000,
          targetId: staffId,
          targetType: 'USER_PROFILE',
          operatorTimeAscSort: true,
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setList(orderBy(data.data, 'operation.time', 'asc'));
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div style={style}>
      <Spin spinning={loading}>
        {list.length === 0 ? (
          <Empty description="暂无数据" />
        ) : (
          <Steps
            progressDot
            direction="vertical"
            items={list.map(item => ({
              title: <div style={{ fontSize: 13 }}>{item.modifyType.name}</div>,
              description: (
                <Space direction="vertical" size={0}>
                  <Typography.Text style={{ fontSize: 12 }} type="secondary">
                    {item.operation.content}
                  </Typography.Text>
                  <Typography.Text style={{ fontSize: 12 }} type="secondary">
                    操作时间：{item.operation.time}
                  </Typography.Text>
                </Space>
              ),
              status: 'finish',
            }))}
          />
        )}
      </Spin>
    </div>
  );
};

const maskIdCard = (idCard?: string): string => {
  if (!idCard) {
    return '--';
  }
  if (idCard.length < 18) {
    return idCard;
  }
  return `${idCard.slice(0, 6)}******${idCard.slice(12)}`;
};
