/* eslint-disable @typescript-eslint/no-explicit-any */
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import type { UserProfileParams } from '@manyun/auth-hub.route.auth-routes';
import type { SvcRespData as User } from '@manyun/auth-hub.service.fetch-user';
import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { fetchUsersByIds } from '@manyun/auth-hub.service.fetch-users-by-ids';
import { UserGroupsCard } from '@manyun/auth-hub.ui.user-groups-card';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import type { TabsProps } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { BackCertificate } from '@manyun/hrm.service.fetch-cert-list';
import { fetchCertList } from '@manyun/hrm.service.fetch-cert-list';

import { UserBio } from './user-bio';
import { UserBioPlus } from './user-bio-plus/user-bio-plus';
import styles from './user-profile.module.less';
import { UserSkillCertificate } from './user-skill-certificate';

export function UserProfile() {
  const { id } = useParams<UserProfileParams>();
  const { search } = useLocation();
  const { tabKey, openUserEditorModal } = getLocationSearchMap(search);
  const [loading, setLoading] = useState(false);
  const [activeKey, setActiveKey] = useState<string>(tabKey ?? 'default');
  const [user, setUserProfile] = useState<User>();
  const [, { checkUserId, checkCode }] = useAuthorized();
  const certTabVisible = checkCode('element_view-user-certificate-tab');
  const [certDataList, setCertData] = useState<BackCertificate[]>([]);

  const openUserEditorModalRef = useRef(openUserEditorModal);
  const [configUtil] = useConfigUtil();
  const isPlusUser =
    (configUtil.getScopeCommonConfigs as any)('iam')?.userProfile?.version === 'plus';

  const [supervisorName, onLoadDirectorName] = useUserName();

  useEffect(() => {
    /**获取用户详情 */
    _fetchUser();
    _fetchCertList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const _fetchUser = async (needLoading = true) => {
    if (needLoading) {
      setLoading(true);
    }
    const { data, error } = await fetchUser({ id: Number(id) });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data === null) {
      message.error('不存在该用户！');
      return;
    }
    if (data.supervisorUid) {
      onLoadDirectorName(data.supervisorUid);
    }
    setUserProfile(data);
  };

  const _fetchCertList = async () => {
    const info = {
      userId: Number(id),
    };
    const { error, data } = await fetchCertList(info);

    if (error) {
      message.error(error.message);
      return;
    }

    setCertData(data.data);
  };

  const onChangeTabs = (key: string) => {
    if (key === 'skill') {
      _fetchCertList();
    }
  };

  /**当前档案是否是登陆人的档案 */
  const isOwner = checkUserId(Number(id));

  if (loading || !user) {
    return <Skeleton avatar />;
  }

  const tabBarExtraContent = (
    <div>
      {user.lastLoginTime ? (
        <span style={{ marginRight: 8 }}>
          最近登录：{dayjs(user.lastLoginTime).format('YYYY-MM-DD HH:mm:ss')}{' '}
        </span>
      ) : null}
      {user.location ? <span>IP：{user.location} </span> : null}
    </div>
  );

  const tabsItems = (() => {
    const items: TabsProps['items'] = [
      {
        key: 'default',
        label: '简介',
        children:
          user &&
          (isPlusUser ? (
            <UserBioPlus
              user={user}
              supervisorName={supervisorName ?? undefined}
              openUserEditorModal={String(openUserEditorModalRef.current) === 'true'}
              canEdit={isOwner}
              onRefresh={() => _fetchUser(false)}
              onClose={() => {
                setLocationSearch({ tabKey: activeKey });
              }}
            />
          ) : (
            <UserBio
              user={user}
              onChange={(value, filed) => {
                if (filed in user) {
                  const __user = {
                    ...user,
                  };
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  (__user as any)[filed] = value;
                  setUserProfile(__user as User);
                }
              }}
            />
          )),
      },
    ];
    if (certTabVisible) {
      items.push({
        key: 'skill',
        label: '资质认证',
        children: (
          <UserSkillCertificate
            userId={Number(id)}
            certDataList={[...certDataList]}
            fetchCertList={_fetchCertList}
          />
        ),
      });
    }
    return items;
  })();

  return (
    <Space className={styles.userProfileContainer} direction="horizontal" align="start">
      <Space className={styles.userProfileLeft} direction="vertical">
        {user && <UserInfoCard user={user} supervisorName={supervisorName ?? undefined} />}
        <UserGroupsCard userId={Number(id)} showModal />
      </Space>
      <Space className={styles.userProfileRight}>
        <Card
          bordered={false}
          bodyStyle={{ overflowY: 'auto', height: 'calc(100% - 42px)' }}
          tabList={tabsItems.map(item => ({
            key: item.key,
            tab: item.label,
          }))}
          tabBarExtraContent={isOwner ? tabBarExtraContent : null}
          activeTabKey={activeKey}
          onTabChange={key => {
            setActiveKey(key);
            setLocationSearch({ tabKey: key });
            onChangeTabs(key);
            openUserEditorModalRef.current = false;
          }}
        >
          {tabsItems.find(item => item.key === activeKey)?.children}
        </Card>
      </Space>
    </Space>
  );
}

export function useUserName() {
  const [userName, setUserName] = React.useState<string | null>(null);
  const onLoadUserName = React.useCallback(async (userId: number) => {
    const { error, data } = await fetchUsersByIds({
      ids: [userId],
    });
    if (error) {
      return;
    }
    setUserName(data?.[0]?.name ?? null);
  }, []);

  return [userName, onLoadUserName] as const;
}
