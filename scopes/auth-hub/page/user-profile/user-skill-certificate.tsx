import React, { useEffect, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackCertificate } from '@manyun/hrm.service.fetch-cert-list';
import { CoverEditCardList } from '@manyun/hrm.ui.cover-edit-card';
import { fetchUserSkills } from '@manyun/knowledge-hub.service.dcexam.fetch-user-skills';
import type { BackendUserSkill } from '@manyun/knowledge-hub.service.dcexam.fetch-user-skills';
import { Skill as SkillCard } from '@manyun/knowledge-hub.ui.skill';
import { SkillsCategoryCascader } from '@manyun/knowledge-hub.ui.skills-category';

import style from './user-skill-certificate.module.less';

export type UserSkillCertificateProps = {
  certDataList: BackCertificate[];
  fetchCertList: Function;
  userId: number;
};

export const UserSkillCertificate = ({
  userId,
  certDataList,
  fetchCertList,
}: UserSkillCertificateProps) => {
  const [, { checkCode }] = useAuthorized();
  const authEditAble = checkCode('element_user-certificate');
  const [skillsData, setSkillsData] = useState<BackendUserSkill[]>();
  const [category, setCategory] = useState<string[]>();

  const fetchSkillList = async ({
    id,
    categoryCodeList,
  }: {
    id: number;
    categoryCodeList?: string[];
  }) => {
    const { error, data } = await fetchUserSkills({ userId: id, categoryCodeList });

    if (error) {
      message.error(error.message);
      return;
    }
    setSkillsData(data.data);
  };

  useEffect(() => {
    fetchSkillList({ id: userId, categoryCodeList: category });
  }, [userId, category]);

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={24}>
      <Space style={{ width: '100%' }} direction="vertical">
        <Typography.Title level={5} showBadge>
          资质证书
        </Typography.Title>
        <div className={style.userSkillCertificate}>
          <CoverEditCardList
            userId={userId}
            certLength={certDataList.length}
            certDataList={[...certDataList]}
            fetchCertList={fetchCertList}
            isAuthEdit={authEditAble}
          />
        </div>
      </Space>
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Space style={{ justifyContent: 'space-between', width: '100%' }} align="center">
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            技能认证
          </Typography.Title>
          <SkillsCategoryCascader
            style={{ width: 200 }}
            size="small"
            placeholder="请选择技能分类"
            onChange={(value: string[]) => {
              setCategory(value);
            }}
          />
        </Space>
        <Space size={16} wrap>
          {Array.isArray(skillsData) &&
            skillsData.length > 0 &&
            skillsData.map(item =>
              item.relateExam ? (
                <SkillCard
                  key={item.id}
                  style={{ height: 180, width: 232 }}
                  id={item.id}
                  userSkillsData={item}
                  type="personalCenter"
                />
              ) : null
            )}
        </Space>
      </Space>
    </Space>
  );
};
