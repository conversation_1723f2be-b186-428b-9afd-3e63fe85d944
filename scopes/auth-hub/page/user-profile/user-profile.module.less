@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.userProfileContainer {
  width: 100%;
  height: 100%;

  > :global(.manyun-space) {
    width: 100%;
    height: 100%;
  }

  > :global(.manyun-space-item) {
    height: 100%;
    width: 100%;

    &:first-child {
      width: 336px;
    }

    &:last-child {
      flex: 1;
      display: flex;
    }
  }

  .userProfileLeft {
    width: 100%;
    height: 100%;

    :global(.manyun-space-item) {
      &:last-child {
        flex: 1;
        overflow: hidden;
      }
    }
  }

  .userProfileRight {
    width: 100%;
    height: 100%;

    > :global(.manyun-space-item) {
      height: 100%;
      width: 100%;
    }

    :global(.manyun-card) {
      height: 100%;
    }
  }
}

.userBioContainer {
  :global {
    .manyun-card-head {
      border: none;
      padding: 0;
    }

    .manyun-card-body {
      padding: 2px 0;
    }

    .manyun-typography {
      margin-bottom: 0;
    }

    .manyun-descriptions-item-container .manyun-descriptions-item-label,
    .manyun-descriptions-item-container .manyun-descriptions-item-content {
      margin-left: 6px;
    }

    .manyun-descriptions-item {
      padding: 7px 0;
    }

    .manyun-card-head {
      min-height: 32px;
    }

    .manyun-card-head-title {
      padding: 8px 0;
    }
  }

  .userConcatRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 4px;
    border-radius: @border-radius-base;
    gap: 10;

    .userConcatRowEdit {
      display: none;
    }

    &:hover {
      background-color: @background-color-light;

      .userConcatRowEdit {
        display: flex;
      }
    }
  }
}

.userConcatRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 4px;
  border-radius: var(--manyun-border-radius-base);
  gap: 10;
}
