import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  clearLocalStorage,
  getLastUsedUserLoginName,
  removeLastUsedLoginName,
  setLastUsedUserLoginName,
  setUserInfo,
} from '@manyun/auth-hub.cache.user';
import {
  getMyPermissionsAction,
  getMyResourcesAction,
  selectMe,
  userSliceActions,
} from '@manyun/auth-hub.state.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useApps } from '@manyun/dc-brain.context.apps';
import type { AppsContextType } from '@manyun/dc-brain.context.apps';
// import { useClientLogger } from '@manyun/dc-brain.gql.client.client-logs';
import { useLocation, useNavigate } from '@manyun/dc-brain.navigation.link';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { useLogin } from '@manyun/iam.gql.client.iam';

import styles from './login.module.less';

// const globalNavigator = window.navigator as Navigator & {
//   userAgentData?: { toJSON(): void };
// };

function structuralProcessing(
  {
    url,
    app,
  }: { url: string | null | undefined; app: 'sales' | 'finances' | 'dcbase' | null | undefined },
  apps: AppsContextType
): { baseURL: string; code: string; redirectUrl: string } {
  let _appConfig = { ...apps.dcbase, redirectUrl: apps.dcbase.baseURL };
  if (url) {
    _appConfig = { ..._appConfig, redirectUrl: url };
  }
  if (app) {
    _appConfig = { ...apps[app], redirectUrl: apps[app].baseURL };
    if (url) {
      const _url = new URL(url);
      const _baseURL = new URL(_appConfig.baseURL);
      _url.protocol = _baseURL.protocol;
      _url.hostname = _baseURL.hostname;
      _appConfig = { ..._appConfig, redirectUrl: _url.toString() };
    }
  }
  return _appConfig;
}
export type LoginFormProps = {};

export function LoginForm() {
  const navigate = useNavigate();
  const location = useLocation();
  const apps = useApps();
  const { redirect_url: redirectUrl, redirect_app: redirectApp } = getLocationSearchMap<{
    redirect_url?: string | null;
    redirect_app?: 'sales' | 'finances' | null | undefined;
  }>(location?.search ?? window.location.search);
  const dispatch = useDispatch();
  const {
    common: { homeUrl },
  } = useSelector(selectCurrentConfig);
  const { errMsg, geolocation } = useSelector(selectMe);

  const [loginType, setLoginType] = useState<string>('DEFAULT');
  const [form] = Form.useForm();
  const [loginUser, setLoginUser] = useState(getLastUsedUserLoginName());
  const [loginLDAP, setLDAP] = useState();
  const [loginLoading, setLoginLoading] = useState(false);

  useEffect(() => {
    const login = getLastUsedUserLoginName();
    form.setFieldsValue({
      login,
      remeberMe: !!login,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (errMsg) {
      form.setFields([{ name: 'password', errors: [errMsg] }]);
    }
  }, [errMsg, form]);

  const changeLoginType = (key: string) => {
    form.setFieldsValue({
      login: key === 'DEFAULT' ? loginUser : loginLDAP,
      remeberMe: !!getLastUsedUserLoginName(),
      password: null,
    });
    setLoginType(key);
  };

  // const [remoteLog] = useClientLogger();
  const [login] = useLogin();

  const handleLogin = useCallback(() => {
    form.validateFields().then(values => {
      setLoginLoading(true);
      const appConfig = structuralProcessing({ url: redirectUrl, app: redirectApp }, apps);
      login({
        variables: {
          type: loginType,
          client: 'BROWSER',
          login: values.login.trim(),
          password: values.password,
          location: geolocation,
          website: 'DCBASE',
          generateAppIdentity: false,
        },
        onCompleted: ({ login }) => {
          if (!login || !login.success) {
            setLoginLoading(false);
            return;
          }

          clearLocalStorage();

          // remoteLog({
          //   variables: {
          //     type: 'info',
          //     message: 'User logged in from Web/DC Base',
          //     metas: [
          //       JSON.stringify({
          //         user: {
          //           type: loginType,
          //           id: login.user.id,
          //           login: login.user.loginName,
          //           name: login.user.userName,
          //         },
          //         client: {
          //           ua: globalNavigator.userAgent,
          //           uaData:
          //             globalNavigator.userAgentData &&
          //             typeof globalNavigator.userAgentData == 'object'
          //               ? globalNavigator.userAgentData.toJSON()
          //               : undefined,
          //           windowSize: {
          //             width: window.innerWidth,
          //             height: window.innerHeight,
          //             zoom: window.devicePixelRatio,
          //           },
          //           visualViewport: window.visualViewport
          //             ? {
          //                 width: window.visualViewport.width,
          //                 height: window.visualViewport.height,
          //                 scale: window.visualViewport.scale,
          //               }
          //             : undefined,
          //         },
          //       }),
          //     ],
          //   },
          // });
          if (login.isRequiredChangePassword) {
            message.warning('您当前登录密码有泄漏风险，请点击头像进行修改密码');
          }
          dispatch(getMyPermissionsAction());
          dispatch(getMyResourcesAction());
          dispatch(
            syncCommonDataAction({
              strategy: {
                space: 'FORCED',
                allVirtualType: 'FORCED',
                accessCardTypes: 'FORCED',
                changeTypes: 'FORCED',
                citiesTree: 'FORCED',
                currentUser: 'FORCED',
                deviceCategory: 'FORCED',
                eventTypes: 'FORCED',
                roomTypes: 'FORCED',
                ticketTypes: 'FORCED',
              },
            })
          );

          setUserInfo({
            type: loginType,
            id: login.user.id,
            login: login.user.loginName,
            name: login.user.userName,
            mobileNumber: login.user.mobile,
            expiredTime: login.expiredAt,
            company: login.user.company,
            needResetPassword: login.isRequiredChangePassword,
            deptId: login.user.deptId,
            department: login.user.deptName,
          });
          if (login.user.roleCode !== null) {
            localStorage.setItem('roleCode', login.user.roleCode);
          }

          if (loginType === 'DEFAULT') {
            if (values.remeberMe) {
              setLastUsedUserLoginName(login.user.loginName);
            } else {
              removeLastUsedLoginName();
            }
          }
          //用于标识刚调用登录接口登录成功
          localStorage.setItem('isFirstLogin', 'true');
          dispatch(
            userSliceActions.loginSuccess({
              userId: login.user.id,
              username: login.user.loginName,
              name: login.user.userName,
              company: login.user.company ?? null,
              callback: async () => {
                localStorage.setItem('sessionId', login.sessionId);

                let ready = false;
                while (!ready) {
                  const persisted = localStorage.getItem('CLOUD_BASE_UI_STATE');
                  try {
                    ready =
                      !!persisted &&
                      typeof (
                        JSON.parse(persisted) as {
                          user?: { userId: number };
                        }
                      ).user?.userId !== 'undefined';
                    await new Promise(resolve => window.setTimeout(resolve, 500));
                  } catch (error) {
                    // ignored...
                    ready = true;
                  }
                }
                setLoginLoading(false);
                if (redirectUrl || redirectApp) {
                  window.location.assign(appConfig.redirectUrl);
                } else {
                  navigate(homeUrl as string);
                }
              },
            })
          );
        },
        onError: error => {
          // eslint-disable-next-line no-console
          console.log(error);
          setLoginLoading(false);
        },
      });
    });
  }, [
    form,
    login,
    loginType,
    geolocation,
    redirectUrl,
    redirectApp,
    apps,
    // remoteLog,
    dispatch,
    navigate,
    homeUrl,
  ]);

  return (
    <div className={styles.loginForm}>
      <Tabs size="large" centered onChange={key => changeLoginType(key)} />
      <Form
        className={styles.formContainer}
        form={form}
        onValuesChange={values => {
          if (values.login) {
            if (loginType === 'DEFAULT') {
              setLoginUser(values.login);
            } else {
              setLDAP(values.login);
            }
          }
          if (values.password) {
            // 清空密码时会显示 `请填写密码！` 的错误信息
            // 此时不必尝试清空登录失败的错误信息
            if (!values.password) {
              return;
            }
            if (errMsg != null) {
              dispatch(userSliceActions.tryResetLoginFailedAttrs());
            }
          }
        }}
      >
        <>
          <Form.Item
            name="login"
            rules={[{ required: true, whitespace: true, message: '请填写账号或用户名!' }]}
          >
            <Input placeholder="账号或用户名" maxLength={24} />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[{ required: true, whitespace: true, message: '请填写密码!' }]}
          >
            <Input.Password placeholder="密码" maxLength={64} />
          </Form.Item>
          <Form.Item style={{ display: 'felx' }}>
            <Form.Item name="remeberMe" valuePropName="checked" noStyle>
              <Checkbox className={styles.formCheckBox}>记住用户名</Checkbox>
            </Form.Item>
          </Form.Item>
        </>
        <Form.Item>
          <Button
            loading={loginLoading}
            className={styles.formBtn}
            htmlType="submit"
            shape="round"
            onClick={handleLogin}
          >
            登录
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
