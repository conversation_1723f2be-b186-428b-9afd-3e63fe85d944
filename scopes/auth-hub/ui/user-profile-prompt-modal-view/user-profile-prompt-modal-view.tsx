/* eslint-disable @typescript-eslint/no-explicit-any */
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { message } from 'antd';
import React from 'react';

import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { useNavigate } from '@manyun/dc-brain.navigation.link';
import { useLoggedInUser } from '@manyun/iam.context.logged-in-user';
import { validStaffInfosComplete } from '@manyun/iam.service.valid-staff-infos-complete';

export type UserProfilePromptModalViewProps = {};

const IsFirstLoginKey = 'isFirstLogin';
export function UserProfilePromptModalView() {
  const navigate = useNavigate();
  const { user } = useLoggedInUser();

  const [open, setOpen] = React.useState(false);
  const [needSkill, setNeedSkill] = React.useState(false);
  const [needDefault, setNeedDefault] = React.useState(false);

  const isFirstLoginFlag = localStorage.getItem(IsFirstLoginKey) === 'true';

  const showTips = React.useMemo(() => {
    const tips: { key: string; context: string; show: boolean }[] = [
      {
        key: 'default',
        context: '您尚有个人信息未补充完整，请进入「个人中心-简介」完善信息',
        show: needDefault,
      },
      {
        key: 'skill',
        context: '您尚有必传资质未上传，请进入「个人中心-资质认证」上传证书',
        show: needSkill,
      },
    ];
    return tips.filter(tip => tip.show !== false);
  }, [needDefault, needSkill]);
  const [configUtil] = useConfigUtil();
  const needPrompt = (configUtil.getScopeCommonConfigs as any)('iam')?.userProfile?.needPrompt;

  const onClose = React.useCallback(() => {
    setOpen(false);
    localStorage.removeItem(IsFirstLoginKey);
  }, []);

  React.useEffect(() => {
    if (isFirstLoginFlag && needPrompt) {
      (async () => {
        const { error, data } = await validStaffInfosComplete({
          userId: user.id,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        if (data) {
          if (data.userInfoComplete === false || data.certComplete === false) {
            setOpen(true);
            setNeedDefault(data.userInfoComplete === false);
            setNeedSkill(data.certComplete === false);
          }
        }
      })();
    } else {
      onClose();
    }
  }, [isFirstLoginFlag, needPrompt, onClose, user.id]);

  return (
    <Modal
      width={524}
      open={open}
      footer={null}
      closable={false}
      maskClosable={false}
      onCancel={() => {
        onClose();
      }}
    >
      <Space style={{ width: '100%' }} direction="vertical">
        <Space style={{ width: '100%' }} align="start" size="middle">
          <ExclamationCircleOutlined
            style={{ fontSize: 22, color: 'var(--manyun-primary-color)' }}
          />
          <Space style={{ width: '100%' }} direction="vertical">
            <Typography.Text style={{ fontSize: 16 }} strong>
              待补充员工信息
            </Typography.Text>
            <ul style={{ margin: 0, padding: 0, marginLeft: showTips.length > 1 ? 16 : 0 }}>
              {showTips.map(tip =>
                showTips.length > 1 ? (
                  <li key={tip.key}>
                    <Typography.Text>{tip.context}</Typography.Text>
                  </li>
                ) : (
                  <Typography.Text key={tip.key}>{tip.context}</Typography.Text>
                )
              )}
            </ul>
          </Space>
        </Space>
        <Space style={{ width: '100%', justifyContent: 'flex-end', marginTop: 8 }} align="center">
          <Button
            onClick={() => {
              onClose();
            }}
          >
            稍后填写
          </Button>
          {needDefault && (
            <Button
              type="primary"
              onClick={() => {
                onClose();
                navigate(
                  `${generateUserProfileRoutePath({ id: user.id.toString(), tabKey: 'default' })}&openUserEditorModal=true`
                );
              }}
            >
              填写个人信息
            </Button>
          )}
          {needSkill && (
            <Button
              type="primary"
              onClick={() => {
                onClose();
                navigate(
                  `${generateUserProfileRoutePath({ id: user.id.toString(), tabKey: 'skill' })}`
                );
              }}
            >
              上传资质证书
            </Button>
          )}
        </Space>
      </Space>
    </Modal>
  );
}
