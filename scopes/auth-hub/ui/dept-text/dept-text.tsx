import React, { useEffect, useState } from 'react';

import type { User } from '@manyun/auth-hub.model.user';
import { fetchDeptDetail } from '@manyun/auth-hub.service.fetch-dept-detail';
import { Typography } from '@manyun/base-ui.ui.typography';

const BLANK_PLACEHOLDER = '--';

export type DeptTextProps = {
  deptId?: User['departmentId'];
  /** 是否自 MDM 同步的数据 */
  fromMdm?: boolean;
};

export function DeptText({ deptId, fromMdm = false }: DeptTextProps) {
  const [text, setText] = useState<string>(BLANK_PLACEHOLDER);
  useEffect(() => {
    if (deptId === undefined || deptId === null) {
      return;
    }
    fetchDeptDetail({
      deptId,
    }).then(({ data, error }) => {
      if (!error && data !== null) {
        if (fromMdm) {
          // 产品要求：同步数据则排除第一个部门（即数据中心）
          setText(data.fullDeptName.split('-').slice(1).join('-'));
        } else {
          setText(data.nameZh);
        }
      }
    });
  }, [deptId, fromMdm]);

  return <Typography.Text>{text || BLANK_PLACEHOLDER}</Typography.Text>;
}

export function useLazyDeptText() {
  const [text, setText] = useState<string>(BLANK_PLACEHOLDER);

  const onLoadText = React.useCallback(async (deptId?: User['departmentId'], fromMdm = false) => {
    if (deptId === undefined || deptId === null) {
      return;
    }

    const { data, error } = await fetchDeptDetail({
      deptId,
    });

    if (!error && data !== null) {
      const processedText = fromMdm ? data.fullDeptName.split('-').slice(1).join('-') : data.nameZh;

      setText(processedText);
    } else {
      setText(BLANK_PLACEHOLDER);
    }
  }, []);

  return [text, onLoadText] as const;
}
