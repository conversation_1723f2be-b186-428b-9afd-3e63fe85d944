/* eslint-disable @typescript-eslint/no-explicit-any */
import debounce from 'lodash.debounce';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useShallowCompareEffect } from 'react-use';

import { useLazyGetUsersForUserSelect } from '@manyun/auth-hub.gql.client.users';
import type { User, UserState, UserType } from '@manyun/auth-hub.model.user';
import { fetchUsersByResourceCode } from '@manyun/auth-hub.service.fetch-users-by-resource-code';
import type { UserInfo as UserInfoByResourceCode } from '@manyun/auth-hub.service.fetch-users-by-resource-code';
import { fetchUsersByIdsWeb } from '@manyun/auth-hub.service.pm.fetch-users-by-ids';
import { selectMe } from '@manyun/auth-hub.state.user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import type { TooltipProps } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackendResourceType } from '@manyun/iam.model.auth-resource';

export type Callback = (option: any) => React.ReactNode;

export type UserSelectProps<T = any> = {
  reserveSearchValue?: boolean; // 如果为 `true`，将会在用户输入关键字时触发 `onChange`
  showSystem?: boolean; // 如果为 `true`，将会展示 `系统` 候选项
  disabledKeys?: number[];
  optionDisabledTooltip?: TooltipProps;
  blockGuid?: string;
  /** 是否仅需要当前用户授权的 */
  authorized?: boolean;
  /**
   * 资源类型数组
   * 仅当 authorized 为 true 时生效
   * 设置后可筛选当前用户授权资源内的用户（取并集）
   * */
  resourceTypes?: BackendResourceType[];
  /**
   * 组装的资源参数（和当前用户无关）
   * 设置后可筛选指定资源内的用户（取并集）
   * */
  resourceParams?: {
    resourceType: BackendResourceType;
    resourceCodes: string[];
  }[];
  /** 是否包含当前登陆用户，默认包含 */
  includeCurrentUser?: boolean;
  labelInValue?: boolean;
  onChange?: (option: any, node?: any) => void;
  /** 用户状态过滤参数，默认为 undefined，即不过滤 */
  userState?: UserState;
  userType?: UserType;
  maxLength?: number;
} & SelectProps<T>;

type UserInfo = Pick<User, 'id' | 'name' | 'login'>;

export const UserSelect = React.forwardRef(
  (
    {
      reserveSearchValue,
      showSystem,
      disabledKeys,
      optionDisabledTooltip,
      blockGuid,
      onChange,
      placeholder = generatePlaceHolder(blockGuid),
      value,
      authorized,
      resourceTypes,
      resourceParams,
      includeCurrentUser = true,
      labelInValue = true,
      userState,
      userType,
      maxLength,
      ...selectProps
    }: UserSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    /** 是否支持系统外用户 */
    const supportExternalUser = !!(reserveSearchValue && labelInValue);
    const [getUsers, { loading: loadingSearchedUsers, data }] = useLazyGetUsersForUserSelect();
    const searchedUsers = useDeepCompareMemo(() => {
      if (userType && data?.usersForUserSelect) {
        return data.usersForUserSelect.filter(item => item.type === userType);
      }
      return data?.usersForUserSelect || [];
    }, [data?.usersForUserSelect, userType]);
    const [initialUsers, setInitialUsers] = useState<UserInfo[]>([]); // 初始根据 value 获取的 users
    const [loadingInitialUsers, setLoadingInitialUsers] = useState<boolean>(false);
    const [loadingUsersByResourceCode, setLoadingUsersByResourceCode] = useState<boolean>(false);
    const [usersByResourceCode, setUsersByResourceCode] = useState<UserInfoByResourceCode[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');

    /** 处理后内部使用的 value，以支持系统外用户 */
    const innerValue = React.useMemo(() => {
      if (!supportExternalUser || value === undefined || value === null) {
        return value;
      }
      if (Array.isArray(value)) {
        value.forEach(item => {
          if (!isInternalUser(item)) {
            item.value = `external-${item.value}`;
          }
        });
        return value;
      }
      return isInternalUser(value) ? value : { ...value, value: `external-${value.value}` };
    }, [supportExternalUser, value]);

    /** 根据 value 获取的 userIds，里面只有系统内的用户 */
    const userIds = React.useMemo(() => {
      // 当为多选模式下（即 value 为数组），此时取数组中系统内用户 labelInValue 时取 value 属性值）
      // 当为单选模式下，此时还需要判断值是否为 undefined | null
      if (value === undefined || value === null) {
        return [];
      }
      if (Array.isArray(value)) {
        return value.reduce((acc, cur) => {
          const arr = labelInValue
            ? isInternalUser(cur) || !supportExternalUser
              ? [cur.value]
              : []
            : [cur];
          return [...acc, ...arr];
        }, []);
      }
      return labelInValue
        ? isInternalUser(value) || !supportExternalUser
          ? [value.value]
          : []
        : [value];
    }, [labelInValue, supportExternalUser, value]);

    useShallowCompareEffect(() => {
      if (userIds.length !== 0) {
        setLoadingInitialUsers(true);
        fetchUsersByIdsWeb({
          userIds,
        }).then(({ data }) => {
          setInitialUsers(data.data);
          setLoadingInitialUsers(false);
        });
      }
      if (blockGuid) {
        setSearchValue('');
      }
      //  eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value, labelInValue]);

    React.useEffect(() => {
      setUsersByResourceCode([]);
    }, [blockGuid]);

    const [searchKeyword, setSearchKeyword] = React.useState<string>('');
    React.useEffect(() => {
      if (typeof innerValue === 'string' && innerValue.startsWith('external-')) {
        setSearchKeyword(innerValue.replace('external-', ''));
        return;
      }
      if (Array.isArray(innerValue)) {
        for (const item of innerValue) {
          if (typeof item === 'string' && item.startsWith('external-')) {
            setSearchKeyword(item.replace('external-', ''));
          }
        }
        return;
      }
    }, [innerValue, supportExternalUser, value]);
    const searchHandler = (keyword: string) => {
      setSearchKeyword(keyword);
      if (blockGuid) {
        setSearchValue(keyword);
        return;
      }
      if (!keyword) {
        return;
      }
      if (loadingSearchedUsers) {
        return;
      }
      getUsers({
        variables: {
          key: keyword,
          userState,
          authorized,
          resourceTypes,
          resourceParams,
        },
      });
    };

    const renderOptionLabel = (option: any) => {
      const { id, name, login } = option;

      return disabledKeys?.includes(id) && optionDisabledTooltip ? (
        <Tooltip placement="topLeft" mouseEnterDelay={0.5} {...optionDisabledTooltip}>
          {defaultLabelRenderer(name, login)}
        </Tooltip>
      ) : (
        defaultLabelRenderer(name, login)
      );
    };

    const onSearch = debounce(searchHandler, 200);

    const options = React.useMemo(
      () => (searchedUsers.length !== 0 ? searchedUsers : initialUsers),
      [initialUsers, searchedUsers]
    );

    const optionsByResourceCode = React.useMemo(
      () =>
        usersByResourceCode.length !== 0
          ? searchValue
            ? usersByResourceCode.filter(
                user => user?.loginName?.includes(searchValue) || user.name.includes(searchValue)
              )
            : usersByResourceCode
          : initialUsers,
      [initialUsers, usersByResourceCode, searchValue]
    );

    const getUsersByResourceCode = async (blockGuid: string) => {
      setLoadingUsersByResourceCode(true);
      const { data, error } = await fetchUsersByResourceCode({
        resourceCode: blockGuid,
        containOneself: true,
      });
      if (error) {
        setLoadingUsersByResourceCode(false);
        return;
      }
      setLoadingUsersByResourceCode(false);
      setUsersByResourceCode(data.data);
    };

    const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
    const list = showSystem ? [SYSTEM_OPTION, ...options] : options;
    const optionsView = (blockGuid ? optionsByResourceCode : list)
      .filter(i => i.name)
      .map(node => {
        const { name, _name, id } = node;
        const disabled = disabledKeys?.includes(id);
        if (!includeCurrentUser && id === userId) {
          return null;
        }
        return (
          <Select.Option
            key={id}
            value={id}
            disabled={disabled}
            title={name}
            data-label={_name || name}
            data-user={node}
          >
            {renderOptionLabel(node)}
          </Select.Option>
        );
      });

    const changeHandler: UserSelectProps['onChange'] = (value, node) => {
      // 若 labelInValue 为 false 或清空时，直接执行 onChange
      if (!labelInValue || value === undefined) {
        onChange?.(value, node);
        return;
      }
      // labelInValue 为 true 且非清空时，需对 value 增加用户信息以及处理系统外用户
      if (Array.isArray(value)) {
        const arr = value.map((item, index) => {
          if (isInternalUser(item) || !supportExternalUser) {
            return {
              ...item,
              ...node[index]['data-user'],
              ...(supportExternalUser ? { type: 'internal' } : null),
            };
          } else {
            return {
              ...item,
              value: item?.value.replace('external-', ''),
              type: 'external',
            };
          }
        });
        onChange?.(arr, node);
      } else {
        if (isInternalUser(value) || !supportExternalUser) {
          onChange?.(
            {
              ...value,
              ...node['data-user'],
              ...(supportExternalUser ? { type: 'internal' } : null),
            },
            node
          );
        } else {
          onChange?.({ ...value, value: value?.value.replace('external-', ''), type: 'external' });
        }
      }
    };

    return (
      <Select
        ref={ref}
        {...selectProps}
        value={innerValue}
        showSearch
        labelInValue={labelInValue}
        filterOption={false}
        optionLabelProp="title"
        loading={loadingSearchedUsers || loadingInitialUsers || loadingUsersByResourceCode}
        placeholder={placeholder}
        onSearch={v => {
          if (maxLength) {
            onSearch(v?.slice(0, maxLength));
            return;
          }
          onSearch(v);
        }}
        onChange={changeHandler}
        onFocus={() => {
          if (blockGuid) {
            getUsersByResourceCode(blockGuid);
          }
        }}
      >
        {supportExternalUser && searchKeyword ? (
          <>
            <Select.OptGroup key="external" label="系统外人员姓名">
              <Select.Option value={`external-${searchKeyword}`} title={searchKeyword}>
                <Typography.Text>{searchKeyword}</Typography.Text>
              </Select.Option>
            </Select.OptGroup>
            <Select.OptGroup key="internal" label="系统内人员账号">
              {optionsView}
            </Select.OptGroup>
          </>
        ) : (
          optionsView
        )}
      </Select>
    );
  }
);

UserSelect.displayName = 'UserSelect';

const SYSTEM_OPTION: any = {
  id: 0,

  // 切记：
  // `系统` 是前端把后端返回的 `SYSTEM` 翻译后的文案，不能拿这个文案当做用户名去查询
  name: '系统',
  // 如果业务场景只能使用用户名查询，那么优先取 `_name` 字段
  _name: 'SYSTEM',

  login: null,
};

function defaultLabelRenderer(userName: string, loginName: string) {
  return (
    <div>
      <Typography.Text>{userName}</Typography.Text>
      {loginName && <br />}
      {loginName && <Typography.Text type="secondary">{loginName}</Typography.Text>}
    </div>
  );
}

const generatePlaceHolder = (blockGuid?: string) => {
  return blockGuid ? '请根据用户名或id搜索' : '请根据用户id,用户名或邮箱搜索';
};

/** 是否为内部用户 */
function isInternalUser(value: { value: unknown; type?: string }) {
  return (
    typeof value.value === 'number' ||
    (value.type !== 'external' && !`${value.value}`.startsWith('external-'))
  );
}
