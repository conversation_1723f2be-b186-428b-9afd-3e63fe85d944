import type { ApolloClient } from '@apollo/client';
import { useApolloClient } from '@apollo/client';
import type { ColumnType, TableRowSelection } from 'antd/es/table/interface';
import React from 'react';

import type { UserGender, UserState, UserType } from '@manyun/auth-hub.model.user';
import type { CustomizedUser } from '@manyun/auth-hub.service.fetch-paged-users';
import { UserGenderText } from '@manyun/auth-hub.ui.user-gender';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserStateText } from '@manyun/auth-hub.ui.user-state';
import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { Table } from '@manyun/base-ui.ui.table';
import type { TableProps } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { StaffCertificateModalView } from '@manyun/hrm.ui.staff-certificate-modal-view';
import { readSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';

import { SkillModal } from './components/skill-modal';

type ColumnKeys =
  | 'name'
  | 'login'
  | 'gender'
  | 'mobileNumber'
  | 'email'
  | 'userGroup'
  | 'resourceCodes'
  | 'certCount'
  | 'type'
  | 'company'
  | 'state';

export type UsersDataTableProps = {
  data: CustomizedUser[];
  loading: boolean;
  rowSelection?: TableRowSelection<CustomizedUser>;
  columnPicks?: ColumnKeys[];
  operateColumn?: {
    column: ColumnType<CustomizedUser>;
  };
  pagination: TableProps<CustomizedUser>['pagination'];
};

function InternalUsersDataTable({
  data,
  loading,
  rowSelection,
  columnPicks,
  operateColumn,
  pagination,
}: UsersDataTableProps) {
  const client = useApolloClient();
  const columns = React.useMemo(() => {
    let columnsArr = getColumns(client);
    if (columnPicks) {
      columnsArr = columnsArr.filter(
        column => column.dataIndex && columnPicks.includes(column.dataIndex as ColumnKeys)
      );
    }
    if (operateColumn !== undefined) {
      columnsArr.push(operateColumn.column);
    }
    return columnsArr;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Table
      scroll={{ x: 'max-content' }}
      rowKey="id"
      loading={loading}
      columns={columns}
      dataSource={data}
      rowSelection={rowSelection}
      pagination={pagination}
    />
  );
}

export const UsersDataTable = (props: UsersDataTableProps) => {
  const { loading } = useSpaces({
    variables: {
      nodeTypes: ['REGION'],
      includeNoIdcRegions: true,
    },
  });

  if (loading) {
    return null;
  }

  return <InternalUsersDataTable {...props} />;
};

export const getColumns = (client: ApolloClient<unknown>) => {
  const columns: Array<ColumnType<CustomizedUser>> = [
    {
      title: '姓名',
      dataIndex: 'name',
      ellipsis: true,
      fixed: 'left',
      render: (text: string, record: CustomizedUser) => (
        <UserLink userId={record.id} userName={text} />
      ),
    },
    {
      title: '用户ID',
      dataIndex: 'login',
      ellipsis: true,
    },
    {
      title: '性别',
      dataIndex: 'gender',
      ellipsis: true,
      render: (value: UserGender) => <UserGenderText userGender={value} />,
    },
    {
      title: '手机号码',
      dataIndex: 'mobileNumber',
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      ellipsis: true,
    },
    {
      title: '所属机房楼栋',
      dataIndex: 'resourceCodes',
      ellipsis: true,
      render: (_, record: CustomizedUser) => {
        if (!record.resourceCodes) {
          return null;
        }

        return (
          <Typography.Text ellipsis={{ tooltip: record.resourceCodes.join(' ｜ ') }}>
            {record.resourceCodes.map(resourceCode => {
              let text = resourceCode;
              const space = readSpace(client, resourceCode);
              if (space && space.type === 'REGION') {
                text = space.label;
              }
              return <Tag key={resourceCode}>{text}</Tag>;
            })}
          </Typography.Text>
        );
      },
    },
    {
      title: '资质证书',
      dataIndex: 'certCount',
      ellipsis: true,
      render: (_, record: CustomizedUser) => (
        <StaffCertificateModalView userId={record.id} allCertCount={record.certCount} />
      ),
    },
    {
      title: '技能认证',
      dataIndex: 'professionalCertNum',
      ellipsis: true,
      render: (_, record: CustomizedUser) => (
        <SkillModal userId={record.id} professionalCertNum={record.professionalCertNum} />
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      ellipsis: true,
      render: (value: UserType) => <UserTypeText userType={value} />,
    },
    {
      title: '所属公司',
      dataIndex: 'company',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'state',
      ellipsis: true,
      render: (value: UserState) => <UserStateText userState={value} />,
    },
  ];
  return columns;
};
