import debounce from 'lodash.debounce';
import React, { useEffect, useState } from 'react';

import { fetchDeptList } from '@manyun/auth-hub.service.fetch-dept-list';
import type { DeptInfo } from '@manyun/auth-hub.service.fetch-dept-list';
import { Transfer } from '@manyun/base-ui.ui.transfer';
import { Tree } from '@manyun/base-ui.ui.tree';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';
import type { GenerateTreeDataOptions } from '@manyun/base-ui.util.generate-tree-data';
import { flattenTreeData } from '@manyun/dc-brain.util.flatten-tree-data';

import { useComplexUsersPicker } from './complex-users-picker-context';

type DeptTreeNode = DeptInfo & {
  key: string;
  label: string;
  title: string;
  children: DeptTreeNode[] | undefined;
};

export function ComplexUsersPickerDepts() {
  const [{ selectedDeptKeys }, { onChange }] = useComplexUsersPicker();
  const [treeData, setTreeData] = useState<DeptTreeNode[]>([]);
  const [originalTreeData, setOriginalTreeData] = useState<DeptTreeNode[]>([]);

  const isChecked = (selectedKeys: (string | number)[], eventKey: string | number) =>
    selectedKeys.includes(eventKey);

  const generateTree = (
    treeNodes: DeptTreeNode[] = [],
    checkedKeys: string[] = []
  ): DeptTreeNode[] =>
    treeNodes.map(({ children, ...props }) => ({
      ...props,
      disabled: checkedKeys.includes(props.key),
      children: generateTree(children, checkedKeys),
    }));

  useEffect(() => {
    fetchDeptList().then(({ data }) => {
      const tree = arrayToTreeData(data.data);
      setTreeData(tree);
      setOriginalTreeData(JSON.parse(JSON.stringify(tree))); // 深拷贝
    });
  }, []);

  const transferDataSource = flattenTreeData(treeData);

  const handleSearch = debounce((dir: string, value: string) => {
    // 如果是回车事件 或 搜索值为空，则重置为原始数据
    const event = arguments[2]; // 获取第三个参数：事件对象
    if ((event instanceof KeyboardEvent && event.key === 'Enter') || !value.trim()) {
      setTreeData(originalTreeData);
      return;
    }

    const filterFn = (node: DeptTreeNode): DeptTreeNode | null => {
      const match = node.title.includes(value);
      if (match) {
        return {
          ...node,
          children: node.children?.map(filterFn).filter(Boolean) as DeptTreeNode[],
        };
      }

      if (node.children) {
        const filteredChildren = node.children.map(filterFn).filter(Boolean) as DeptTreeNode[];

        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren,
          };
        }
      }

      return null;
    };

    if (dir === 'left') {
      const filteredTree = originalTreeData
        .map(item => filterFn(item))
        .filter(Boolean) as DeptTreeNode[];

      setTreeData(filteredTree);
    } else if (dir === 'right') {
      setTreeData(originalTreeData);
    }
  }, 500);

  return (
    <Transfer
      style={{ height: 440 }}
      targetKeys={selectedDeptKeys}
      dataSource={transferDataSource}
      render={item => item.title}
      showSelectAll={false}
      showSearch
      onChange={(keys: string[]) => {
        if (onChange) {
          onChange(keys, { selected: true, keys, variant: 'dept' });
        }
      }}
      onSearch={handleSearch}
    >
      {({ direction, onItemSelect, selectedKeys }) => {
        if (direction === 'left' && treeData.length > 0) {
          const checkedKeys = [...selectedKeys, ...selectedDeptKeys];
          return (
            <Tree
              height={340}
              blockNode
              checkable
              checkStrictly
              defaultExpandAll
              checkedKeys={checkedKeys}
              treeData={generateTree(treeData, selectedDeptKeys)}
              onCheck={(_, { node: { key } }) => {
                onItemSelect(key, !isChecked(checkedKeys, key));
              }}
              onSelect={(_, { node: { key } }) => {
                onItemSelect(key, !isChecked(checkedKeys, key));
              }}
            />
          );
        }
        return;
      }}
    </Transfer>
  );
}

function arrayToTreeData(arr: DeptInfo[]) {
  const options: GenerateTreeDataOptions<DeptTreeNode, DeptInfo> = {
    key: 'deptId',
    parentKey: 'parentId',
    isRootNode(node) {
      return node.parentId === '0';
    },
    getNode(node, children) {
      return {
        ...node,
        key: node.deptId,
        title: node.fullDeptName,
        label: node.nameZh,
        value: node.deptId,
        children: children || undefined,
      };
    },
  };
  return generateTreeData<DeptTreeNode, DeptInfo>(arr, options);
}
