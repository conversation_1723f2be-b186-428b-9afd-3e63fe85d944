/* eslint-disable @typescript-eslint/no-explicit-any */
import ManOutlined from '@ant-design/icons/es/icons/ManOutlined';
import WomanOutlined from '@ant-design/icons/es/icons/WomanOutlined';
import dayjs from 'dayjs';
import React from 'react';
import { useShallowCompareEffect } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import type { UserJSON } from '@manyun/auth-hub.model.user';
import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { User as UserAvatar } from '@manyun/auth-hub.ui.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';

import styles from './user-info-card.module.less';

const BLANK_PLACEHOLDER = '--';

export type UserInfoCardProps = {
  style?: React.CSSProperties;
  className?: string;
  editable?: boolean;
  needValid?: boolean;
  showTitle?: boolean;
  showDivider?: boolean;
  extra?: React.ReactNode;
  supervisorName?: string;
} & (
  | {
      user: UserJSON;
    }
  | {
      userId: number;
    }
);

export function UserInfoCard({
  style,
  className,
  editable = true,
  needValid,
  showTitle = true,
  showDivider = true,
  supervisorName,
  extra,
  ...rest
}: UserInfoCardProps) {
  const [user, setUser] = React.useState<UserJSON>();
  useShallowCompareEffect(() => {
    (async () => {
      if ('user' in rest) {
        setUser(rest.user);
        return;
      }
      const userId = rest.userId;
      const { data, error } = await fetchUser({ id: userId, needValid: needValid });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data === null) {
        message.error('不存在该用户！');
        return;
      }
      setUser(data);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rest]);

  const [, { checkUserId }] = useAuthorized();
  const userEditable = editable && user && checkUserId(user.id);
  const [configUtil] = useConfigUtil();
  const isYGUser =
    (configUtil.getScopeCommonConfigs as any)('iam')?.userProfile?.version === 'plus';
  return (
    <Card style={style} className={className}>
      {user && (
        <>
          {showTitle && (
            <Typography.Title style={{ marginBottom: '1.5em' }} level={5} showBadge>
              员工信息
            </Typography.Title>
          )}

          <Row>
            <UserAvatar id={user.id} size={56} showName={false} editable={userEditable} />
            <main className={styles.main}>
              <Space direction="vertical" size={4}>
                <Space>
                  <Typography.Title level={4} style={{ margin: 0 }}>
                    {user.name}
                  </Typography.Title>
                  {user.gender === 'MALE' ? (
                    <ManOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
                  ) : (
                    <WomanOutlined style={{ color: `var(--${prefixCls}-error-color)` }} />
                  )}
                </Space>
                <span>
                  {user.title && (
                    <Typography.Text type="secondary">
                      {isYGUser ? (
                        <MetaTypeText
                          metaType={'POSITION_YG' as any}
                          code={user.title}
                          defaultShow
                        />
                      ) : (
                        user.title
                      )}
                    </Typography.Text>
                  )}
                  {user.title && user.type && <Divider type="vertical" spaceSize="mini" />}
                  {user.type && (
                    <Typography.Text type="secondary">
                      <UserTypeText userType={user.type} />
                    </Typography.Text>
                  )}
                </span>
              </Space>
            </main>
            {extra}
          </Row>
          {showDivider && (
            <>
              <Divider />
              <Space direction="vertical">
                <Space size={4}>
                  <Typography.Text type="secondary">直线经理：</Typography.Text>
                  <SupervisorText user={user} supervisorName={supervisorName} />
                </Space>
                <Space size={4}>
                  <Typography.Text type="secondary">入职日期：</Typography.Text>
                  <Typography.Text>
                    {user.hiredDate
                      ? dayjs(user.hiredDate).format('YYYY-MM-DD')
                      : BLANK_PLACEHOLDER}
                  </Typography.Text>
                </Space>
              </Space>
            </>
          )}
        </>
      )}
    </Card>
  );
}

function SupervisorText({ user, supervisorName }: { user: UserJSON; supervisorName?: string }) {
  const { supervisorUid, subSupervisorUid } = user;

  if (supervisorUid) {
    if (subSupervisorUid) {
      return (
        <span>
          <UserLink userId={supervisorUid} userName={supervisorName} />
          <Divider type="vertical" spaceSize="mini" />
          <UserLink userId={subSupervisorUid} />
        </span>
      );
    } else {
      return <UserLink userId={supervisorUid} />;
    }
  }

  if (subSupervisorUid) {
    return <UserLink userId={subSupervisorUid} />;
  }

  return <>{BLANK_PLACEHOLDER}</>;
}
