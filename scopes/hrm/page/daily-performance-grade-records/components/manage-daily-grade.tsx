import React from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { fetchDailyGradeLockInfos } from '@manyun/hrm.service.fetch-daily-grade-lock-infos';
import type { DailyGradeLockInfos } from '@manyun/hrm.service.fetch-daily-grade-lock-infos';
import { updateDailyGradeLockInfos } from '@manyun/hrm.service.update-daily-grade-lock-infos';

export type ManageDailyGradeProps = {
  year: string;
  onSuccess?: () => void;
};

export const ManageDailyGrade: React.FC<ManageDailyGradeProps> = ({ year, onSuccess }) => {
  const [controlModalOpen, setControlModalOpen] = React.useState(false);
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-control-daily-performance-grade-records',
  });
  const [selectedPeriods, setSelectedPeriods] = React.useState<DailyGradeLockInfos[]>([]);
  const [submitLoading, setSubmitLoading] = React.useState(false);

  const options = React.useMemo(
    () => [
      { label: `锁定Q1评分`, value: 'Q1' },
      { label: `锁定Q2评分`, value: 'Q2' },
      { label: `锁定Q3评分`, value: 'Q3' },
      { label: `锁定Q4评分`, value: 'Q4' },
    ],
    []
  );

  if (!authorized) {
    return null;
  }

  return (
    <>
      <Button
        onClick={async () => {
          const { error, data } = await fetchDailyGradeLockInfos({ year });
          if (error) {
            message.error(error.message);
            return;
          }
          setSelectedPeriods(data.data);
          setControlModalOpen(true);
        }}
      >
        评分管控
      </Button>
      <Modal
        width={644}
        title={`${year}年度评分管控`}
        open={controlModalOpen}
        okButtonProps={{ loading: submitLoading }}
        cancelButtonProps={{ disabled: submitLoading }}
        onCancel={() => {
          setSelectedPeriods([]);
          setControlModalOpen(false);
        }}
        onOk={async () => {
          setSubmitLoading(true);
          const { error } = await updateDailyGradeLockInfos({
            kpiLockConfigs: selectedPeriods,
          });
          setSubmitLoading(false);
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('操作成功');
          setControlModalOpen(false);
          setSelectedPeriods([]);
          onSuccess?.();
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Alert
            showIcon
            message={
              <>
                评分锁定后，该考核周期内，季度加分、扣分均不可新增、修改或删除；
                <br />
                评分未锁定，可对未考核完成的任务，进行新增、修改或删除评分操作。
              </>
            }
            type="warning"
          />
          <Checkbox.Group
            options={options}
            value={selectedPeriods.filter(item => item.locked).map(item => item.period)}
            onChange={values => {
              const newSelectedPeriods: DailyGradeLockInfos[] = options.map(option => {
                const period = option.value;
                return {
                  period,
                  year,
                  locked: (values as string[]).includes(period),
                };
              });
              setSelectedPeriods(newSelectedPeriods);
            }}
          />
        </Space>
      </Modal>
    </>
  );
};
