import EllipsisOutlined from '@ant-design/icons/es/icons/EllipsisOutlined';
import omit from 'lodash.omit';
import moment from 'moment';
import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { withdrawTodo } from '@manyun/bpm.service.withdraw-todo';
import { ApproveModalButton } from '@manyun/bpm.ui.approve-modal-button';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { usePaginatedPerformanceGrades } from '@manyun/hrm.gql.client.hrm';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import type {
  AnnualPerformanceObjectiveLocales,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import { getDailyPerformanceGradeLocales } from '@manyun/hrm.model.daily-performance-grade';
import type {
  DailyPerformanceGradeJSON,
  DailyPerformanceGradeLocales,
} from '@manyun/hrm.model.daily-performance-grade';
import { exportPerformanceDailyGradeRecords } from '@manyun/hrm.service.export-performance-daily-grade-records';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-performance-daily-grade-records';
import { PERFORMANCE_CHANGED_YEAR, getPerformanceVersion } from '@manyun/hrm.util.performances';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { ApproveStatusTag } from './components/approve-status-tag';
import { DailyPerformanceGradeDeleteButton } from './components/daily-performance-grade-deleter';
import { DailyPerformanceGradeDetail } from './components/daily-performance-grade-detail';
import { DailyPerformanceGradeMutatorButton } from './components/daily-performance-grade-mutator';
import { FilterFormPopover } from './components/filter-form-popver';
import type { SearchValues } from './components/filter-form-popver';
import { ManageDailyGrade } from './components/manage-daily-grade';

const defaultPagination = { page: 1, pageSize: 10 };

type OrderField =
  | 'userId'
  | 'region'
  | 'idc'
  | 'name'
  | 'type'
  | 'subType'
  | 'occurTime'
  | 'position'
  | 'grade'
  | 'createdAt'
  | 'modifiedAt';

export type Field = Omit<SvcQuery, 'orderBy'> & {
  year?: string;
  orderField?: OrderField;
  orderType?: ColumnType['defaultSortOrder'];
  period?: BackendPerformancePeriod;
};

export function DailyPerformanceGradeRecords() {
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const locales = useMemo(() => getDailyPerformanceGradeLocales(), []);
  const planLocales = useMemo(() => getAnnualPerformancePlanLocales(), []);
  const objectiveLocales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);
  const { search } = useLocation();
  const { page, pageSize, ...restDefaultFields } = getLocationSearchMap<Partial<Field>>(search, {
    parseNumbers: true,
    arrayKeys: [
      'regionCodes',
      'idcTags',
      'createTimeRange',
      'occurTimeRange',
      'modifiedTimeRange',
      'blockGuids',
      'instStatusList',
    ],
  });

  const [fields, setFields] = useState<Field>({
    page: page ?? defaultPagination.page,
    pageSize: pageSize ?? defaultPagination.pageSize,
    year: moment().format('YYYY'),
    period: 'YEAR',
    ...restDefaultFields,
  });

  const getDefaultTableColumns = ({
    locales,
    objectiveLocals,
    defaultSortOrderMapper,
    currentUserId,
  }: {
    locales: DailyPerformanceGradeLocales;
    objectiveLocals: AnnualPerformanceObjectiveLocales;
    defaultSortOrderMapper?: Record<string, ColumnType['defaultSortOrder']>;
    currentUserId: number;
  }) => {
    return [
      {
        title: locales.gradedUser.__self,
        dataIndex: 'userId',
        fixed: 'left',
        disabled: true,
        defaultSortOrder: defaultSortOrderMapper?.userId,
        sorter: true,
        render: (_, record) => <UserLink id={record.user.id} name={record.user.name} />,
      },
      {
        title: locales.gradedUser.jobNo,
        dataIndex: 'jobNo',
        show: false,
        render: (_, record) => record.user.jobNo ?? '--',
      },
      {
        title: locales.gradedUser.region,
        dataIndex: 'region',
        show: false,
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.region,
        render: (_, record) => record.user.region?.label,
      },
      {
        title: locales.gradedUser.idc,
        dataIndex: 'idc',
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.idc,
        render: (_, record) =>
          record.user.idc?.value ? <SpaceText guid={record.user.idc.value} /> : '--',
      },
      {
        title: locales.gradedUser.block,
        dataIndex: 'blockGuid',
        render: (_, record) => record.user.block?.label ?? '--',
      },

      {
        title: locales.position,
        dataIndex: 'position',
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.position,
        render: (_, record) => record.performancePosition?.label,
      },
      {
        title: locales.type,
        dataIndex: 'type',
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.type,
        render: (_, record) => {
          const year = moment(record.occurTime).year();
          const version = getPerformanceVersion(year);
          return objectiveLocals.type.textMapper[version][record.type];
        },
      },
      {
        title: locales.name,
        dataIndex: 'name',
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.name,
      },
      {
        title: locales.measurements,
        dataIndex: 'measurements',
        render: val => (
          <Typography.Paragraph
            style={{ maxWidth: 344, marginBottom: 0 }}
            ellipsis={{ rows: 1, tooltip: val }}
          >
            {val}
          </Typography.Paragraph>
        ),
      },
      {
        title: locales.gradeDesc,
        dataIndex: 'gradeDesc',
        render: val => (
          <Typography.Paragraph
            style={{ maxWidth: 344, marginBottom: 0 }}
            ellipsis={{ rows: 1, tooltip: val }}
          >
            {val}
          </Typography.Paragraph>
        ),
      },
      {
        title: locales.occurTime,
        dataIndex: 'occurTime',
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.occurTime,
        render: val => moment(val).format('YYYY-MM-DD'),
      },

      {
        title: locales.grade,
        dataIndex: 'grade',
        sorter: true,
        disabled: true,
        defaultSortOrder: defaultSortOrderMapper?.grade,
        render: val => (
          <Typography.Text type={val >= 0 ? 'success' : 'danger'}>
            {val > 0 ? `+${val}` : val}
          </Typography.Text>
        ),
      },
      {
        title: locales.instStatus.__self,
        dataIndex: 'instStatus',
        render: (val, record) => {
          const year = moment(record.occurTime).year();
          if (year >= PERFORMANCE_CHANGED_YEAR) {
            // 扣分项默认生效
            return <ApproveStatusTag status={val ?? locales.instStatus.enum.PASS} />;
          }
          return val
            ? (locales.instStatus.enum[val as keyof typeof locales.instStatus.enum] ?? '--')
            : '--';
        },
      },

      {
        title: locales.gradedUser.superiors,
        dataIndex: 'superiors',
        show: false,
        render: (_, record) => record.user.superiors.map(({ name }) => name).join('｜'),
      },
      {
        title: locales.createdBy.__self,
        dataIndex: 'createUser',
        render: (_, record) => record.createdBy.name,
      },
      {
        title: locales.createdAt,
        dataIndex: 'createdAt',
        show: false,
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.createdAt,
        render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: locales.modifiedBy.__self,
        dataIndex: 'modifiedUser',
        show: false,
        render: (_, record) => record.modifiedBy?.name,
      },
      {
        title: locales.modifiedAt,
        dataIndex: 'modifiedAt',
        sorter: true,
        defaultSortOrder: defaultSortOrderMapper?.modifiedAt,
        render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        dataIndex: 'options',
        fixed: 'right',
        disabled: true,
        render: (_, record) => {
          const canEdit = record.canModified && currentUserId === record.createdBy.id;
          const canDelete = record.canModified && currentUserId === record.createdBy.id;
          const canRevoke =
            record.instStatus === 'APPROVING' &&
            currentUserId === record.createdBy.id &&
            record.instId;

          const canApprove =
            record.instStatus === 'APPROVING' &&
            record.needApproval &&
            record.instId &&
            record.taskId;

          return (
            <Space>
              <DailyPerformanceGradeDetail record={record} />
              {canRevoke && (
                <Button
                  type="link"
                  compact
                  onClick={() => {
                    Modal.confirm({
                      title: '确认要撤回' + record.instId + '的审批吗？',
                      content: '撤回后数据将不可恢复。',
                      okText: '确认',
                      cancelText: '取消',
                      onOk: async () => {
                        const { error } = await withdrawTodo({
                          operator: record.createdBy.id.toString(),
                          instId: record.instId!,
                        });
                        if (error) {
                          message.error(error.message);
                          return;
                        }
                        message.success('撤回成功！');
                        onReloadData();
                      },
                    });
                  }}
                >
                  撤回
                </Button>
              )}
              {canApprove && (
                <>
                  <ApproveModalButton
                    instId={record.instId!}
                    taskId={record.taskId!}
                    result={0}
                    text="同意"
                    type="link"
                    compact
                    onSuccess={() => {
                      onReloadData();
                    }}
                  />
                  <ApproveModalButton
                    instId={record.instId!}
                    taskId={record.taskId!}
                    result={1}
                    text="拒绝"
                    type="link"
                    compact
                    onSuccess={() => {
                      onReloadData();
                    }}
                  />
                </>
              )}
              {(canDelete || canEdit) && (
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'edit',
                        label: (
                          <DailyPerformanceGradeMutatorButton id={record.id} type="link" compact />
                        ),
                        show: canEdit,
                      },
                      {
                        key: 'delete',
                        label: (
                          <DailyPerformanceGradeDeleteButton
                            id={record.id}
                            onSuccess={() => {
                              onReloadData();
                            }}
                          />
                        ),
                        show: canDelete,
                      },
                    ].filter(item => item.show),
                  }}
                >
                  <EllipsisOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
                </Dropdown>
              )}
            </Space>
          );
        },
      },
    ] as ColumnType<DailyPerformanceGradeJSON>[];
  };

  const performanceVersion = React.useMemo(() => {
    const yearNum = moment(fields.year, 'YYYY').get('year');

    return getPerformanceVersion(yearNum);
  }, [fields]);

  const { loading, data, refetch } = usePaginatedPerformanceGrades({
    variables: {
      query: transformQuery({
        page: page ?? defaultPagination.page,
        pageSize: pageSize ?? defaultPagination.pageSize,
        year: fields.year,
        period: 'YEAR',
        ...restDefaultFields,
      }),
    },
  });

  const [tableColumns, setTableColumns] = useState<ColumnType<DailyPerformanceGradeJSON>[]>(
    getDefaultTableColumns({
      defaultSortOrderMapper: restDefaultFields.orderField
        ? { [restDefaultFields.orderField]: restDefaultFields.orderType }
        : undefined,
      locales,
      objectiveLocals: objectiveLocales,
      currentUserId: userId!,
    })
  );

  const onSearch = useCallback(
    (params: Field) => {
      refetch({
        query: transformQuery(params),
      });
      setFields(pre => params);
      setLocationSearch(params);
    },
    [refetch]
  );

  const onReloadData = useCallback(() => {
    onSearch({ ...fields, page: 1 });
  }, [fields, onSearch]);

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          日常评分记录
        </Typography.Title>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            <Explanation
              style={{ color: 'inherit' }}
              iconType="question"
              tooltip={{ title: '可快捷筛选某考核周期内，发生的所有评分记录' }}
            >
              发生日期:
            </Explanation>
            <Radio.Group
              value={fields.period}
              options={Object.keys(planLocales.performancePeriod.enum)
                .filter(key => !['FIR_HALF_YEAR', 'SEC_HALF_YEAR'].includes(key as string))
                .map(key => ({
                  label: planLocales.performancePeriod.enum[key as BackendPerformancePeriod],
                  value: key,
                }))}
              onChange={e => {
                onSearch({
                  ...fields,
                  ...defaultPagination,
                  period: e.target.value,
                });
              }}
            />
          </Space>
          <Space>
            <DatePicker
              style={{ width: 207 }}
              picker="year"
              format={value => `${value.format('YYYY')}年度`}
              value={fields.year ? moment().set('year', Number(fields.year)) : undefined}
              allowClear={false}
              disabledDate={current => current && current.diff(moment()) > 0}
              onChange={value => {
                onSearch({
                  ...fields,
                  ...defaultPagination,
                  year: value ? value.format('YYYY') : undefined,
                });
              }}
            />
          </Space>
        </Space>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space size="middle">
            <DailyPerformanceGradeMutatorButton type="primary" />
            <ManageDailyGrade
              year={fields.year!}
              onSuccess={() => {
                onReloadData();
              }}
            />
            <UserSelect
              style={{ width: 272 }}
              labelInValue={false}
              allowClear
              value={fields.staffId}
              onChange={value => {
                onSearch({ ...fields, ...defaultPagination, staffId: value });
              }}
            />
          </Space>
          <Space>
            <FilterFormPopover
              typeTextVersion={performanceVersion}
              values={
                omit(fields, [
                  'page',
                  'pageSize',
                  'orderField',
                  'orderType',
                  'year',
                  'period',
                  'staffId',
                ]) as SearchValues
              }
              onSearch={params => {
                onSearch({ ...fields, ...defaultPagination, ...params });
              }}
            />
            <FileExport
              text="导出"
              filename="日常评分记录"
              showExportFiltered={false}
              data={async type => {
                const { error, data } = await exportPerformanceDailyGradeRecords(
                  transformQuery(fields)
                );

                if (error) {
                  message.error(error.message);
                  return;
                }
                return data;
              }}
            />
            <EditColumns
              listsHeight={360}
              uniqKey="HRM_PAGE_DAILY_PERFORMANCE_GRADE_RECORDS"
              defaultValue={getDefaultTableColumns({
                locales,
                objectiveLocals: objectiveLocales,
                currentUserId: userId!,
              })}
              onChange={columns => {
                setTableColumns(columns);
              }}
            />
          </Space>
        </Space>
        <Table
          rowKey="id"
          scroll={{ x: 'max-content' }}
          loading={loading}
          dataSource={data?.paginatedPerformanceGrades?.data}
          columns={tableColumns}
          pagination={{
            total: data?.paginatedPerformanceGrades?.total,
            current: fields.page,
            pageSize: fields.pageSize,
          }}
          onChange={({ current, pageSize }, _, sorter, { action }) => {
            if (action === 'paginate') {
              const mergedValues = { ...fields, page: current!, pageSize: pageSize! };
              onSearch(mergedValues);
            } else if (action === 'sort') {
              if (sorter && !Array.isArray(sorter)) {
                const mergedValues = {
                  ...fields,
                  ...defaultPagination,
                  orderField: sorter.field as OrderField,
                  orderType: sorter.order,
                };
                onSearch(mergedValues);
              }
            }
          }}
        />
      </Space>
    </Card>
  );
}

export const transformQuery = (fields: Field) => {
  // 默认类型需要手动添加，因为要剔除红线记录
  const defaultTypes: BackendAnnualPerformanceObjectiveType[] =
    Number(fields.year) >= PERFORMANCE_CHANGED_YEAR ? ['DAILY', 'DAY_PLUS'] : ['DAILY', 'YEAR'];
  const _query: SvcQuery = {
    ...fields,
    type: fields.type,
    types: fields.type ? undefined : defaultTypes,
    occurTimeRange:
      fields.period && fields.year
        ? convertOccurTimeRange(Number(fields.year), fields.period)
        : undefined,
    orderBy: fields.orderField ? { [fields.orderField]: fields.orderType ?? undefined } : undefined,
  };

  return omit(_query, ['year', 'orderField', 'orderType', 'period']) as SvcQuery;
};

const convertOccurTimeRange = (year: number, period: BackendPerformancePeriod) => {
  let startIndex = 0;
  let endIndex = 0;
  switch (period) {
    case 'Q1':
      startIndex = 0;
      endIndex = 2;
      break;
    case 'Q2':
      startIndex = 3;
      endIndex = 5;
      break;
    case 'Q3':
      startIndex = 6;
      endIndex = 8;
      break;
    case 'Q4':
      startIndex = 9;
      endIndex = 11;
      break;
    // case 'FIR_HALF_YEAR':
    //   startIndex = 0;
    //   endIndex = 5;
    //   break;
    // case 'SEC_HALF_YEAR':
    //   startIndex = 6;
    //   endIndex = 11;
    //   break;
    case 'YEAR':
      startIndex = 0;
      endIndex = 11;
      break;
  }
  const _basicMoment = moment().set('year', year);

  return [
    _basicMoment.set('month', startIndex).startOf('month').valueOf(),
    _basicMoment.set('month', endIndex).endOf('month').valueOf(),
  ] as [number, number];
};
