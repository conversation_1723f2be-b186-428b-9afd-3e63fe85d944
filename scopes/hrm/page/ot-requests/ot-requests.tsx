import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { exportOtRequests } from '@manyun/hrm.service.export-ot-requests';
import type { StatisticOTRequest } from '@manyun/hrm.service.fetch-paged-ot-requests-statistics';
import { fetchPagedOtRequestsStatistics } from '@manyun/hrm.service.fetch-paged-ot-requests-statistics';

const formatStyle = 'YYYY-MM-DD HH:mm';

export type OtRequestsState = {
  page: number;
  pageSize: number;
  bizId?: string;
  apply?: number;
  applyName?: string;
  timeRange?: [number, number];
  activeTime?: [number, number];
  loading: boolean;
  ids: string[];
  entities: Record<string, StatisticOTRequest>;
  total: number;
};

export function OtRequests() {
  const { search } = useLocation();
  const { pageSize, page, bizId, apply, applyName, timeRange, activeTimeRange } =
    getLocationSearchMap<Partial<OtRequestsState & { activeTimeRange?: [number, number] }>>(
      search,
      {
        parseNumbers: true,
        arrayKeys: ['timeRange', 'activeTimeRange'],
      }
    );
  const [otRequests, setOtRequests] = useState<OtRequestsState>({
    page: page ?? 1,
    pageSize: pageSize ?? 10,
    bizId: bizId,
    apply: apply,
    applyName: applyName,
    timeRange: timeRange,
    activeTime: activeTimeRange,
    loading: false,
    ids: [],
    entities: {},
    total: 0,
  });

  const [exportLoading, setExportLoading] = useState(false);
  const [form] = Form.useForm();

  const selectedData = useCallback(
    (ids?: string[]) => {
      if (ids) {
        return ids.map(id => otRequests.entities[id]);
      }
      return otRequests.ids.map(id => otRequests.entities[id]);
    },
    [otRequests.entities, otRequests.ids]
  );

  const onLoadOts = useCallback(async () => {
    setOtRequests(pre => ({ ...pre, loading: true }));
    const searchParams = {
      page: otRequests.page,
      pageSize: otRequests.pageSize,
      timeRange: otRequests.timeRange,
      apply: otRequests.apply,
      activeTimeRange: otRequests.activeTime,
      bizId: otRequests.bizId,
    };
    setLocationSearch({ ...searchParams, applyName: otRequests.applyName });
    const { data, error } = await fetchPagedOtRequestsStatistics({
      ...searchParams,
      /**需要转换timeRange */
      timeRange: transformTimeRange(searchParams.timeRange),
    });
    setOtRequests(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }
    const ids: string[] = [];
    const entities: Record<string, StatisticOTRequest> = {};
    data.data.forEach(ot => {
      ids.push(ot.id);
      entities[ot.id] = ot;
    });
    setOtRequests(pre => ({ ...pre, ids, entities, total: data.total }));
  }, [
    otRequests.page,
    otRequests.pageSize,
    otRequests.timeRange,
    otRequests.apply,
    otRequests.activeTime,
    otRequests.bizId,
    otRequests.applyName,
  ]);

  const onHandleClick = useCallback(
    (key: 'reset' | 'search') => {
      if (key === 'reset') {
        form.setFieldsValue({
          bizId: undefined,
          apply: undefined,
          timeRange: transformTimeRange(undefined)?.map(time => moment(time)),
          applyName: undefined,
          activeTime: undefined,
        });
        setOtRequests(pre => ({
          ...pre,
          page: 1,
          pageSize: 10,
          bizId: undefined,
          apply: undefined,
          applyName: undefined,
          timeRange: undefined,
          activeTime: undefined,
        }));
      } else {
        const values = form.getFieldsValue();
        setOtRequests(pre => ({
          ...pre,
          page: 1,
          pageSize: 10,
          bizId: values.bizId,
          apply: values.apply ? values.apply.value : undefined,
          applyName: values.apply ? values.apply.label : undefined,
          timeRange:
            values.timeRange && values.timeRange.length
              ? [
                  moment(values.timeRange[0]).startOf('day').valueOf(),
                  moment(values.timeRange[1]).endOf('day').valueOf(),
                ]
              : [0, 0],
          activeTime:
            values.activeTime && values.activeTime.length
              ? [
                  moment(values.activeTime[0]).startOf('day').valueOf(),
                  moment(values.activeTime[1]).endOf('day').valueOf(),
                ]
              : undefined,
        }));
      }
    },
    [form]
  );

  useEffect(() => {
    onLoadOts();
  }, [onLoadOts]);

  const handleFileExport = async (type: 'all' | 'selected' | 'filtered') => {
    let params = undefined;
    if (type !== 'all') {
      params = {
        timeRange: transformTimeRange(otRequests.timeRange),
        apply: otRequests.apply,
        activeTimeRange: otRequests.activeTime,
        bizId: otRequests.bizId,
      };
    }
    setExportLoading(true);
    const { error, data } = await exportOtRequests(params);
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  };

  const defaultTimeRangeMoment = useMemo(() => {
    const timeRange = transformTimeRange(otRequests.timeRange);
    if (!timeRange) {
      return undefined;
    }
    return timeRange.map(time => moment(time));
  }, [otRequests.timeRange]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card bordered={false}>
        <Form
          form={form}
          colon={false}
          initialValues={{
            timeRange: defaultTimeRangeMoment,
            bizId: bizId,
            apply: apply && applyName ? { label: applyName, value: apply } : undefined,
            activeTime: activeTimeRange?.map(time => moment(time)),
          }}
        >
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Form.Item label="业务单号" name="bizId">
                <Input style={{ width: '90%' }} allowClear />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="申请人" name="apply">
                <UserSelect style={{ width: '90%' }} allowClear labelInValue />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="加班时间" name="timeRange">
                <DatePicker.RangePicker placeholder={['开始时间', '结束时间']} allowClear />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="生效时间" name="activeTime">
                <DatePicker.RangePicker
                  placeholder={['开始时间', '结束时间']}
                  allowClear
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={18} />
            <Col span={6}>
              <Space style={{ width: '100%', justifyContent: 'flex-end' }} align="end">
                <Button
                  type="primary"
                  loading={otRequests.loading}
                  disabled={otRequests.loading}
                  onClick={() => {
                    onHandleClick('search');
                  }}
                >
                  搜索
                </Button>
                <Button
                  loading={otRequests.loading}
                  disabled={otRequests.loading}
                  onClick={() => {
                    onHandleClick('reset');
                  }}
                >
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card bordered={false}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <FileExport
            text="导出"
            filename="加班记录.xls"
            disabled={exportLoading || !selectedData().length}
            showExportFiltered
            data={type => {
              return handleFileExport(type);
            }}
          />
          <Table
            rowKey="id"
            loading={otRequests.loading}
            columns={[
              {
                ellipsis: true,
                title: '业务单号',
                dataIndex: 'id',
                fixed: 'left',
                render(id, record) {
                  return (
                    <Link to={generateBPMRoutePath({ id: record.overtimeConfirmId })}>{id}</Link>
                  );
                },
              },
              {
                ellipsis: true,
                title: '申请人',
                dataIndex: 'apply',
                render(apply) {
                  return <User.Link id={apply} />;
                },
              },
              {
                ellipsis: true,
                title: '加班时长(小时)',
                dataIndex: 'totalTime',
              },
              {
                title: '加班时间',
                dataIndex: '_time',
                render(_, { startTime, endTime }) {
                  return `${moment(startTime).format(formatStyle)} 至 ${moment(endTime).format(
                    formatStyle
                  )}`;
                },
              },
              {
                title: '上班打卡',
                dataIndex: 'onCheckTime',
                render(onCheckTime) {
                  return `${onCheckTime ? moment(onCheckTime).format('HH:mm') : '--'}`;
                },
              },
              {
                title: '下班打卡',
                dataIndex: 'offCheckTime',
                render(offCheckTime) {
                  return `${offCheckTime ? moment(offCheckTime).format('HH:mm') : '--'}`;
                },
              },
              {
                ellipsis: true,
                title: '生效时间',
                dataIndex: 'gmtModified',
                render(gmtModified) {
                  return `${moment(gmtModified).format('YYYY-MM-DD HH:mm:ss')}`;
                },
              },
            ]}
            scroll={{ x: 'max-content' }}
            dataSource={selectedData()}
            pagination={{
              total: otRequests.total,
              current: otRequests.page,
              pageSize: otRequests.pageSize,
            }}
            onChange={pagination => {
              setOtRequests(pre => ({
                ...pre,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}

function transformTimeRange(timeRange: [number, number] | undefined): [number, number] | undefined {
  /**timeRange 为undefined，默认返回上月的开始时间、结束时间 */
  if (!timeRange) {
    const defaultStartTime = moment().add(-1, 'M').startOf('month');
    const defaultEndTime = moment().add(-1, 'M').endOf('month');
    return [defaultStartTime.valueOf(), defaultEndTime.valueOf()];
  }
  /**timeRange =[0,0],加班时间清空 */
  if (timeRange && timeRange[0] === 0 && timeRange[1] === 0) {
    return undefined;
  }
  return timeRange;
}
