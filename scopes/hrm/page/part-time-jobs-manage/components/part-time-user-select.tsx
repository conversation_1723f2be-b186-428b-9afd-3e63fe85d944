import debounce from 'lodash.debounce';
import React from 'react';

import { fetchDeptUsersByKey } from '@manyun/auth-hub.service.fetch-dept-users-by-key';
import type { SimperUSer } from '@manyun/auth-hub.service.fetch-dept-users-by-key';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

export type PartTimeUserSelectProps = {
  trigger?: 'onDidMount' | 'onFocus';
} & SelectProps;
export const PartTimeUserSelect = ({
  trigger = 'onDidMount',
  ...restProps
}: PartTimeUserSelectProps) => {
  const [users, setUser] = React.useState<SimperUSer[]>([]);
  const [loading, setLoading] = React.useState(false);

  const onLoadData = async (key?: string) => {
    if (!key) {
      return;
    }
    setLoading(true);
    const { data, error } = await fetchDeptUsersByKey({
      key,
    });
    setLoading(false);

    if (error) {
      message.error(error.message);
      setUser([]);
      return;
    }

    setUser(data.data);
  };

  const searchHandler = (keyword: string) => {
    onLoadData(keyword);
  };

  const onSearch = debounce(searchHandler, 200);

  React.useEffect(() => {
    if (trigger === 'onDidMount') {
      onLoadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trigger]);

  return (
    <Select
      {...restProps}
      loading={loading}
      showSearch
      filterOption={false}
      placeholder="请根据用户名搜索"
      onSearch={onSearch}
      onFocus={() => {
        if (trigger === 'onFocus') {
          onLoadData();
        }
      }}
    >
      {users.map(user => {
        return (
          <Select.Option key={user.id} value={user.id}>
            {defaultLabelRenderer(user.userName, user.loginName)}
          </Select.Option>
        );
      })}
    </Select>
  );
};

function defaultLabelRenderer(userName: string, loginName: string) {
  return (
    <div>
      <Typography.Text>{userName}</Typography.Text>
      {loginName && <br />}
      {loginName && <Typography.Text type="secondary">{loginName}</Typography.Text>}
    </div>
  );
}
