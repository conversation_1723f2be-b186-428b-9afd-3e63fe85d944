import moment from 'moment';
import React from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tag } from '@manyun/base-ui.ui.tag';
import styled, { css } from '@manyun/dc-brain.theme.theme';
import { createPartTimeJobs } from '@manyun/hrm.service.create-part-time-jobs';
import type { PartTimeJobInfos } from '@manyun/hrm.service.fetch-paged-part-time-jobs';
import { updatePartTimeJobs } from '@manyun/hrm.service.update-part-time-jobs';
import type { ApiArgs } from '@manyun/hrm.service.update-part-time-jobs';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { PartTimeUserSelect } from './part-time-user-select';

interface PartTimeJobsMutatorProps {
  record?: PartTimeJobInfos;
  onOk?: () => void;
}

export function PartTimeJobsMutator({ record, onOk }: PartTimeJobsMutatorProps) {
  const [form] = Form.useForm();
  const [open, setOpen] = React.useState(false);
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-part-time-jobs-operate',
  });
  const [pickerPlaceholder, setPickerPlaceholder] = React.useState<[string, string]>(['', '']);
  const [loading, setLoading] = React.useState(false);

  const isCreate = React.useMemo(() => {
    return !record;
  }, [record]);

  const handleOk = async () => {
    const values = await form.validateFields();
    const partTimeBlocks = (values.partTimePositions ?? [])
      .map(([_, subPosition]: [string, string][]) => subPosition)
      .filter((position: string): position is string => Boolean(position));
    const params: ApiArgs = {
      staffId: record ? record?.staffId : values.staffId,
      partTimeBlocks: partTimeBlocks,
      startTime: values.month[0] ? values.month[0].startOf('month').valueOf() : undefined,
      endTime: values.month[1]
        ? values.month[1].startOf('month').valueOf()
        : convertToLongTermValueOf(),
    };
    setLoading(true);
    const { error } = isCreate
      ? await createPartTimeJobs(params)
      : await updatePartTimeJobs({ ...params, id: record?.id });
    setLoading(false);
    if (error) {
      setLoading(false);
      message.error(error.message);
      return;
    }
    message.success('操作成功!');
    setOpen(false);
    form.resetFields();
    onOk?.();
  };

  if (!authorized) {
    return null;
  }
  return (
    <>
      <Button
        compact={!isCreate}
        type={isCreate ? 'primary' : 'link'}
        onClick={() => {
          if (record) {
            const _isLongTerm = isLongTerm(record?.endTime);
            form.setFieldsValue({
              staffId: record?.userName,
              partTimePositions:
                record?.partTimeBlocks?.map(blockGuid => {
                  const [idc] = blockGuid.split('.');
                  return [idc, blockGuid];
                }) || [],
              month: [
                moment(record?.startTime).startOf('month'),
                _isLongTerm ? null : moment(record?.endTime).startOf('month'),
              ],
            });
            setPickerPlaceholder(['', _isLongTerm ? '长期' : '']);
          } else {
            setPickerPlaceholder(['', '']);
          }
          setOpen(true);
        }}
      >
        {isCreate ? '添加兼岗记录' : '编辑'}
      </Button>
      <Modal
        title={isCreate ? '添加兼岗记录' : '编辑'}
        open={open}
        width={640}
        okText="提交"
        okButtonProps={{ loading: loading }}
        cancelButtonProps={{ disabled: loading }}
        onCancel={() => {
          form.resetFields();
          setOpen(false);
        }}
        onOk={handleOk}
      >
        <Form form={form} initialValues={{ record }} labelCol={{ span: 5 }}>
          <Form.Item
            label="人员姓名"
            name="staffId"
            rules={[{ required: true, message: '请选择人员' }]}
          >
            {isCreate ? (
              <PartTimeUserSelect
                style={{ width: 210 }}
                showSearch
                labelInValue={false}
                disabled={!isCreate}
              />
            ) : (
              <Input style={{ width: 210 }} disabled />
            )}
          </Form.Item>
          <Form.Item
            label="兼岗楼栋"
            name="partTimePositions"
            rules={[{ required: true, message: '请选择兼岗楼栋' }]}
          >
            <LocationCascader
              style={{ width: 472 }}
              changeOnSelect={false}
              multiple
              showCheckedStrategy="SHOW_CHILD"
              showSearch
              allowClear
            />
          </Form.Item>
          <CustomerRangePicker>
            <Form.Item
              label="兼岗起止时间"
              name="month"
              rules={[{ required: true, message: '请设置兼岗起止时间' }]}
            >
              <DatePicker.RangePicker
                style={{ width: 472 }}
                placeholder={pickerPlaceholder}
                picker="month"
                allowEmpty={[false, true]}
                renderExtraFooter={() => {
                  const [startDate] = form.getFieldValue('month') || [];
                  if (!startDate) {
                    return null;
                  }
                  return (
                    <Tag
                      color="processing"
                      onClick={() => {
                        setPickerPlaceholder(['', '长期']);
                        form.setFieldValue('month', [startDate || moment(), null]);
                      }}
                    >
                      长期
                    </Tag>
                  );
                }}
                onChange={val => {
                  if (!val || !val[1]) {
                    setPickerPlaceholder(['', '']);
                  }
                }}
              />
            </Form.Item>
          </CustomerRangePicker>
        </Form>
      </Modal>
    </>
  );
}

export const CustomerRangePicker = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-picker-input > input::placeholder {
        color: var(--text-color);
      }
    `;
  }}
`;

export const LONG_TERM = '2099-01-01';
// 是否为长期
export const isLongTerm = (endTime: number) => {
  const endStr = moment(endTime).startOf('month').format('YYYY-MM-DD');
  return endStr === LONG_TERM;
};

export const convertToLongTermValueOf = () => {
  return moment(LONG_TERM).startOf('month').valueOf();
};
