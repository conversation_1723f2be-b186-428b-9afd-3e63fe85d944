import { useApolloClient } from '@apollo/client';
import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { fetchPartTimeJobsLogs } from '@manyun/hrm.service.fetch-part-time-jobs-logs';
import type { PartTimeJobLogs } from '@manyun/hrm.service.fetch-part-time-jobs-logs';
import { UserLink } from '@manyun/iam.ui.user-link';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';

import { isLongTerm } from './part-time-jobs-mutator';

const useLazyQueryData = () => {
  const [data, setData] = useState<PartTimeJobLogs[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchData = async (id: number) => {
    setLoading(true);
    const { error, data } = await fetchPartTimeJobsLogs({ recordId: id });
    setLoading(false);

    if (error) {
      message.error(error.message);
      setData([]);
      return;
    }
    setData(data.data);
  };

  return [{ data, loading }, fetchData] as const;
};

export const OperationLogModelView: React.FC<{ id: number }> = ({ id }) => {
  const client = useApolloClient();
  const [visible, setVisible] = useState(false);
  const [{ data, loading }, fetchData] = useLazyQueryData();

  return (
    <>
      <Button
        type="link"
        onClick={() => {
          fetchData(id);
          setVisible(true);
        }}
      >
        操作日志
      </Button>
      <Modal
        title="操作日志"
        width={1024}
        bodyStyle={{ maxHeight: '75vh', overflowY: 'auto' }}
        open={visible}
        footer={null}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Table<PartTimeJobLogs>
          rowKey="targetId"
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '操作类型',
              dataIndex: 'modifyType',
              fixed: 'left',
              width: 90,
              render: (text: string) => {
                if (text === 'ADD') {
                  return '新增';
                }
                if (text === 'MODIFY') {
                  return '编辑';
                }
                return text;
              },
            },
            {
              title: '兼岗楼栋',
              dataIndex: 'partTimeBlocks',
              render: (_, record) =>
                (record.partTimeBlocks ?? []).length > 0 ? (
                  <Space size={0} split="｜">
                    {(record.partTimeBlocks ?? []).map(guid => {
                      const blockLabel = readSpace(client, guid);
                      return blockLabel?.label;
                    })}
                  </Space>
                ) : (
                  '--'
                ),
            },
            {
              title: '兼岗开始时间',
              width: 120,
              dataIndex: 'startDate',
              render: (_, record) => {
                const startDate = moment(record.startTime);
                return startDate.isValid() ? startDate.format('YYYY-MM') : '--';
              },
            },
            {
              title: '兼岗结束时间',
              width: 120,
              dataIndex: 'endDate',
              render: (_, record) => {
                const endDate = moment(record.endTime);
                return endDate.isValid()
                  ? isLongTerm(record.endTime)
                    ? '长期'
                    : endDate.format('YYYY-MM')
                  : '--';
              },
            },
            {
              title: '操作人',
              dataIndex: 'modifierId',
              render: (_, record) => (
                <UserLink target="_blank" userId={Number(record.modifierId)} />
              ),
            },
            {
              title: '操作时间',
              dataIndex: 'gmtModified',
              render: (_, record) => {
                const updateTime = moment(record.gmtModified);
                return updateTime.isValid() ? updateTime.format('YYYY-MM-DD HH:mm:ss') : '--';
              },
            },
          ]}
          dataSource={data}
        />
      </Modal>
    </>
  );
};
