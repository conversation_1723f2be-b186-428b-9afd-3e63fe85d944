/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-28
 *
 * @packageDocumentation
 */
import { useApolloClient } from '@apollo/client';
import moment from 'moment';
import React, { useState } from 'react';

import { fetchUsersByIds } from '@manyun/auth-hub.service.fetch-users-by-ids';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { exportPartTimeJobs } from '@manyun/hrm.service.export-part-time-jobs';
import { fetchPagedPartTimeJobs } from '@manyun/hrm.service.fetch-paged-part-time-jobs';
import type { PartTimeJobInfos } from '@manyun/hrm.service.fetch-paged-part-time-jobs';
import { UserLink } from '@manyun/iam.ui.user-link';
import { readSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { OperationLogModelView } from './components/operation-log-modal-view';
import {
  PartTimeJobsMutator,
  convertToLongTermValueOf,
  isLongTerm,
} from './components/part-time-jobs-mutator';
import { PartTimeUserSelect } from './components/part-time-user-select';
import { QueryFilterWrapper } from './styles';

type PaginationParams = {
  current: number;
  pageSize: number;
};

type FilterFormValues = {
  userId?: number;
  partTimeBlocks?: string[];
  startDate?: moment.Moment;
  endDate?: moment.Moment;
  orderByModifyDesc?: boolean;
  endTimePickerPlaceholder?: string;
};

type QueryParams = PaginationParams & FilterFormValues;

type PartTimeJobInfosWithModifyUserName = PartTimeJobInfos & {
  modifierName?: string;
};

const useQueryList = (params: QueryParams) => {
  const [data, setData] = useState<PartTimeJobInfosWithModifyUserName[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  useSpaces({
    variables: {
      nodeTypes: ['IDC', 'BLOCK'],
    },
    fetchPolicy: 'network-only',
  });

  const fetchData = async (params: QueryParams) => {
    setLoading(true);
    const { error, data } = await fetchPagedPartTimeJobs({
      pageNum: params.current,
      pageSize: params.pageSize,
      staffId: params.userId,
      partTimeBlocks: params.partTimeBlocks?.filter(item => item.split('.')[1]),
      orderByModifyDesc: params.orderByModifyDesc,
      startTime: params.startDate ? params.startDate.startOf('month').valueOf() : undefined,
      endTime: params.endTimePickerPlaceholder
        ? convertToLongTermValueOf()
        : params.endDate
          ? params.endDate.startOf('month').valueOf()
          : undefined,
    });

    if (error) {
      setLoading(false);
      message.error(error.message);
      setData([]);
      setTotal(0);
      return;
    }
    let _data: PartTimeJobInfosWithModifyUserName[] = data.data;
    const userIds = Array.from(
      new Set(
        data?.data.flatMap(item =>
          item.userName ? [item.modifierId] : [item.staffId, item.modifierId]
        ) ?? []
      )
    );

    if (userIds.length > 0) {
      const { data: userData } = await fetchUsersByIds({
        ids: userIds,
      });
      const userMap = new Map(userData.map(user => [user.id, user]));
      _data = data.data.map(item => {
        return {
          ...item,
          modifierName: userMap.get(item.modifierId)?.name,
          userName: item.userName ?? userMap.get(item.staffId)?.name,
        };
      });
    }
    setLoading(false);
    setData(_data);
    setTotal(data.total);
  };

  React.useEffect(() => {
    fetchData(params);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return [{ data, total, loading }, fetchData] as const;
};

export type PartTimeJobsManageProps = {};

export function PartTimeJobsManage() {
  const client = useApolloClient();
  const [form] = Form.useForm<FilterFormValues>();
  const [queryParams, setQueryParams] = useState<FilterFormValues>({});
  const [pagination, setPagination] = useState<PaginationParams>({
    current: 1,
    pageSize: 10,
  });
  const [{ data, loading, total }, onReloadData] = useQueryList(pagination);
  const [exportLoading, setExportLoading] = useState(false);

  const onExport = React.useCallback(async () => {
    setExportLoading(true);
    const { data: exportData, error } = await exportPartTimeJobs({
      staffId: queryParams.userId,
      partTimeBlocks: queryParams.partTimeBlocks?.filter(item => item.split('.')[1]),
      startTime: queryParams.startDate
        ? queryParams.startDate.startOf('month').valueOf()
        : undefined,
      endTime: queryParams.endTimePickerPlaceholder
        ? convertToLongTermValueOf()
        : queryParams.endDate
          ? queryParams.endDate.startOf('month').valueOf()
          : undefined,
      orderByModifyDesc: queryParams.orderByModifyDesc,
    });
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    return exportData;
  }, [
    queryParams.endDate,
    queryParams.endTimePickerPlaceholder,
    queryParams.orderByModifyDesc,
    queryParams.partTimeBlocks,
    queryParams.startDate,
    queryParams.userId,
  ]);

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <QueryFilterWrapper>
          <QueryFilter<FilterFormValues>
            form={form}
            items={[
              {
                label: '姓名',
                name: 'userId',
                span: 1,
                control: <PartTimeUserSelect style={{ width: 240 }} showSearch allowClear />,
              },
              {
                label: '兼岗楼栋',
                name: 'partTimeBlocks',
                span: 1,
                control: <LocationCascader changeOnSelect={false} showSearch allowClear />,
              },
              {
                label: '兼岗开始时间',
                name: 'startDate',
                span: 1,
                control: <DatePicker.MonthPicker placeholder="" allowClear />,
              },
              {
                label: '兼岗结束时间',
                name: 'endDate',
                span: 1,
                control: (
                  <DatePicker.MonthPicker
                    placeholder={queryParams.endTimePickerPlaceholder ?? ''}
                    allowClear
                    renderExtraFooter={() => {
                      return (
                        <Tag
                          color="processing"
                          onClick={() => {
                            setQueryParams(pre => {
                              return {
                                ...pre,
                                endTimePickerPlaceholder: pre.endTimePickerPlaceholder
                                  ? undefined
                                  : '长期',
                              };
                            });
                            form.setFieldValue('endDate', null);
                          }}
                        >
                          长期
                        </Tag>
                      );
                    }}
                    onChange={value => {
                      if (!value) {
                        setQueryParams(pre => {
                          return {
                            ...pre,
                            endTimePickerPlaceholder: undefined,
                          };
                        });
                      }
                    }}
                  />
                ),
              },
            ]}
            onSearch={() => {
              const values = form.getFieldsValue();
              setQueryParams(pre => ({ ...pre, ...values }));
              setPagination(pre => ({
                ...pre,
                current: 1,
              }));
              onReloadData({ ...pagination, ...queryParams, ...values, current: 1 });
            }}
            onReset={() => {
              form.resetFields();
              setQueryParams(pre => ({
                orderByModifyDesc: pre.orderByModifyDesc,
              }));
              setPagination(pre => ({
                ...pre,
                current: 1,
              }));
              onReloadData({
                ...pagination,
                current: 1,
              });
            }}
          />
        </QueryFilterWrapper>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <PartTimeJobsMutator
            onOk={() => {
              onReloadData({
                ...pagination,
                ...queryParams,
              });
            }}
          />
          <FileExport
            text=""
            filename="兼岗记录管理"
            disabled={exportLoading}
            data={() => {
              return onExport();
            }}
          />
        </Space>
        <Table<PartTimeJobInfosWithModifyUserName>
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '员工号',
              dataIndex: 'jobNumber',
              fixed: 'left',
            },
            {
              title: '姓名',
              dataIndex: 'name',
              render: (_, record) => (
                <UserLink
                  target="_blank"
                  userId={Number(record.staffId)}
                  userName={record.userName}
                />
              ),
            },
            {
              title: '所属机房',
              dataIndex: 'idc',
              render: (_, record) => (record.idc ? <SpaceText guid={record.idc} /> : '--'),
            },
            {
              title: '主楼栋',
              dataIndex: 'blockGuid',
              render: (_, record) => {
                if (record.blockGuid) {
                  const blockLabel = readSpace(client, record.blockGuid);
                  return blockLabel?.label ?? '--';
                }
                return '--';
              },
            },
            {
              title: '兼岗楼栋',
              dataIndex: 'partTimeBlocks',
              render: (_, record) =>
                (record.partTimeBlocks ?? []).length > 0 ? (
                  <Space size={0} split="｜">
                    {(record.partTimeBlocks ?? []).map(guid => {
                      const blockLabel = readSpace(client, guid);
                      return blockLabel?.label;
                    })}
                  </Space>
                ) : (
                  '--'
                ),
            },
            {
              title: '兼岗开始时间',
              dataIndex: 'startDate',
              width: 120,
              render: (_, record) => {
                const startDate = moment(record.startTime);
                return startDate.isValid() ? startDate.format('YYYY-MM') : '--';
              },
            },
            {
              title: '兼岗结束时间',
              width: 120,
              dataIndex: 'endDate',
              render: (_, record) => {
                const endDate = moment(record.endTime);
                return endDate.isValid()
                  ? isLongTerm(record.endTime)
                    ? '长期'
                    : endDate.format('YYYY-MM')
                  : '--';
              },
            },
            {
              title: '更新人',
              dataIndex: 'modifierName',
            },
            {
              title: '更新时间',
              dataIndex: 'updateTime',
              width: 220,
              sorter: true,
              render: (_, record) => {
                const updateTime = moment(record.gmtModified);
                return updateTime.isValid() ? updateTime.format('YYYY-MM-DD HH:mm:ss') : '--';
              },
            },
            {
              title: '操作',
              key: 'action',
              width: 130,
              fixed: 'right',
              render: (_, record) => (
                <Space size={4}>
                  <PartTimeJobsMutator
                    record={record}
                    onOk={() => {
                      onReloadData({
                        ...pagination,
                        ...queryParams,
                      });
                    }}
                  />
                  <OperationLogModelView id={record.id} />
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                current: page,
                pageSize,
              }));
              onReloadData({
                ...pagination,
                ...queryParams,
                current: page,
                pageSize,
              });
            },
          }}
          onChange={(_, __, sorter, { action }) => {
            if (action === 'sort') {
              const { order } = sorter as { order: 'ascend' | 'descend' | undefined };
              const orderByModifyDesc =
                order === 'descend' ? true : order === 'ascend' ? false : undefined;
              setQueryParams(prev => ({
                ...prev,
                orderByModifyDesc,
              }));
              setPagination(prev => ({
                ...prev,
                current: 1,
              }));
              onReloadData({
                ...pagination,
                ...queryParams,
                orderByModifyDesc,
                current: 1,
              });
            }
          }}
        />
      </Space>
    </Card>
  );
}
