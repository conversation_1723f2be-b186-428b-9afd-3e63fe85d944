import styled, { css } from '@manyun/dc-brain.theme.theme';

export const QueryFilterWrapper = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-form-item:first-child {
        .${prefixCls}-form-item-label {
          > label {
            width: 50px;
          }
        }
        .${prefixCls}-input-affix-wrapper {
          width: 240px !important;
        }
      }
      .${prefixCls}-picker-input > input::placeholder {
        color: var(--text-color);
      }
    `;
  }}
`;
