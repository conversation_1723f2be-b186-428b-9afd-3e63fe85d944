import dayjs from 'dayjs';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyPerformanceBlockEvaluationChangedLogs } from '@manyun/hrm.gql.client.hrm';

import { AttachmentModalView } from './attachment-modal-view';
import { TableContainer } from './styles';

export type OperationModalViewProps = {
  id: number;
};
export const OperationModalView = ({ id }: OperationModalViewProps) => {
  const [open, setOpen] = React.useState(false);
  const [fetchPerformanceBlockEvaluationChangedLogs, { data, loading }] =
    useLazyPerformanceBlockEvaluationChangedLogs();

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          fetchPerformanceBlockEvaluationChangedLogs({
            variables: {
              blockEvalId: id,
            },
          });
        }}
      >
        操作记录
      </Button>
      <Modal
        title="操作记录"
        width={1280}
        open={open}
        footer={null}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <TableContainer>
          <Table
            rowKey="bizId"
            size="middle"
            loading={loading}
            scroll={{ x: 'max-content' }}
            dataSource={data?.performanceBlockEvaluationChangedLogs.data ?? []}
            columns={[
              {
                title: '操作类型',
                fixed: 'left',
                dataIndex: 'changeType',
                render: val => (
                  <>
                    {val === 'GRADE' && '填写飞检评分'}
                    {val === 'ACCIDENTS' && '填写事故数量'}
                    {val === 'CONFIRM' && '确认'}
                    {val === 'UPDATE' && '更正'}
                  </>
                ),
              },
              {
                title: '操作内容',
                dataIndex: 'context',
                render: (_, record) => {
                  if ((record.evaluationCompare ?? []).length === 0) {
                    return '--';
                  }
                  return (record.evaluationCompare ?? [])
                    .map(
                      item =>
                        `${item.desc}: ${item.oldContent ? `${item.oldContent}修改为` : ''}${item.newContent}`
                    )
                    .join('；');
                },
              },
              {
                title: '备注',
                className: 'max-width-notes',
                dataIndex: 'reason',
                render: val => (
                  <Typography.Paragraph
                    style={{ maxWidth: 280, marginBottom: 0 }}
                    ellipsis={{ rows: 1, tooltip: val }}
                  >
                    {val}
                  </Typography.Paragraph>
                ),
              },
              {
                title: '附件',
                dataIndex: 'context',
                render: (_, record) => (
                  <AttachmentModalView
                    bizId={record.bizId}
                    extensionType="BLOCK_EVAL_FILE"
                    bizType="BLOCK_EVAL"
                  />
                ),
              },
              {
                title: '操作人',
                dataIndex: 'modifierBy',
                render: val => val?.name ?? '--',
              },
              {
                title: '操作时间',
                dataIndex: 'gmtModified',
                render: val => (val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
              },
            ]}
          />
        </TableContainer>
      </Modal>
    </>
  );
};
