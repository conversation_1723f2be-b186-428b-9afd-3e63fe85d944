/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-28
 *
 * @packageDocumentation
 */
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchIdcMatchDepartmentList } from '@manyun/hrm.service.fetch-idc-match-department-list';
import type {
  ApiArgs,
  IdcMatchDepartments,
} from '@manyun/hrm.service.fetch-idc-match-department-list';
import { updateIdcMatchDepartment } from '@manyun/hrm.service.update-idc-match-department';
import type { ApiArgs as UpdateApiArgs } from '@manyun/hrm.service.update-idc-match-department';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import {
  DeptTreeSelect,
  generateFullDeptName,
  useDeptTreeData,
} from './component/dept-tree-select';

export type IdcMatchDepartProps = {};

type IdcMatchDepartmentsWithDeptIds = IdcMatchDepartments & {
  deptIds: string[] | undefined;
};

const useLazyQueryIdcMatchDepartmentList = () => {
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState<IdcMatchDepartmentsWithDeptIds[]>([]);

  const onFetchData = React.useCallback(async (queryParams: ApiArgs) => {
    setLoading(true);
    const { data, error } = await fetchIdcMatchDepartmentList({
      idc: queryParams.idc,
      deptId: queryParams.deptId,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      setData([]);
      return;
    }
    setData(
      data.data.map(item => {
        const deptIds = item.deptInfos.map(dept => dept.deptId);
        return { ...item, deptIds: deptIds.length > 0 ? deptIds : undefined };
      })
    );
  }, []);

  return [
    { loading, data },
    { onFetchData, setData },
  ] as const;
};

const useUpdateIdcMatchDepartment = () => {
  const [loading, setLoading] = React.useState(false);

  const onUpdate = React.useCallback(
    async (params: UpdateApiArgs, callback?: (success: boolean) => void) => {
      setLoading(true);
      const { error } = await updateIdcMatchDepartment({
        idc: params.idc,
        deptIds: params.deptIds,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        callback?.(false);

        return;
      }
      callback?.(true);
    },
    []
  );

  return [{ loading }, onUpdate] as const;
};
export const IdcMatchDepart: React.FC = () => {
  const [form] = Form.useForm();
  const [editableKeys, setEditableKeys] = React.useState<React.Key[]>([]);

  const [{ loading, data }, { onFetchData }] = useLazyQueryIdcMatchDepartmentList();
  const [{ loading: treeLoading, treeData }, onLoadDeptTreeData] = useDeptTreeData();
  const [, onUpdate] = useUpdateIdcMatchDepartment();
  const [fields, setFields] = React.useState<{ idc?: string; department?: string }>({});

  const onReloadData = React.useCallback(() => {
    onFetchData({
      idc: fields.idc ? (Array.isArray(fields.idc) ? fields.idc[0] : undefined) : undefined,
      deptId: fields.department,
    });
  }, [fields.department, fields.idc, onFetchData]);

  React.useEffect(() => {
    onFetchData({});
    onLoadDeptTreeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          机房与部门映射
        </Typography.Title>
        <Form form={form} colon={false}>
          <Space size="middle">
            <Form.Item style={{ marginBottom: 0 }} label="机房" name="idc">
              <LocationCascader style={{ width: 278 }} nodeTypes={['IDC']} allowClear showSearch />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }} label="部门" name="department">
              <DeptTreeSelect
                style={{ width: 278 }}
                loading={treeLoading}
                treeData={treeData}
                allowClear
                showSearch
                treeDefaultExpandAll
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    const { idc, department } = form.getFieldsValue();
                    setFields({ idc, department });
                    setEditableKeys([]);
                    onFetchData({ idc: idc && idc[0] ? idc[0] : undefined, deptId: department });
                  }}
                >
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setFields({});
                    onFetchData({});
                    setEditableKeys([]);
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Space>
        </Form>
        <EditableProTable<IdcMatchDepartmentsWithDeptIds>
          rowKey="idc"
          recordCreatorProps={false}
          loading={loading}
          columns={[
            {
              title: '机房',
              dataIndex: 'idc',
              readonly: true,
              width: 250,
              render: (_, record) => (record.idc ? <SpaceText guid={record.idc} /> : '--'),
              renderFormItem: (_, { record }) =>
                record?.idc ? <SpaceText guid={record.idc} /> : '--',
            },
            {
              title: '部门',
              dataIndex: 'deptIds',
              render: (_, record) => {
                const { deptInfos } = record;
                return (deptInfos ?? []).length > 0 ? (
                  <Space size={0} split={<Divider type="vertical" />} wrap>
                    {deptInfos?.map(dept => (
                      <React.Fragment key={dept.id}>
                        {generateFullDeptName(dept.fullDeptName)}
                      </React.Fragment>
                    ))}
                  </Space>
                ) : (
                  '--'
                );
              },
              renderFormItem: (_, { record }) => {
                const otherSelectedDeptIds = data
                  .filter(item => item.idc !== record?.idc)
                  .flatMap(item => item.deptIds || []);

                return (
                  <DeptTreeSelect
                    style={{ width: '100%' }}
                    loading={treeLoading}
                    treeData={treeData}
                    disabledKeys={otherSelectedDeptIds}
                    multiple
                    allowClear
                    showSearch
                    treeDefaultExpandAll
                    disabledRoot
                  />
                );
              },
            },
            {
              title: '操作',
              valueType: 'option',
              width: 110,
              render: (_, record: IdcMatchDepartments, __, action: any) => {
                return [
                  <Button
                    key="edit"
                    type="link"
                    onClick={() => {
                      action?.startEditable?.(record.idc);
                    }}
                  >
                    编辑
                  </Button>,
                ];
              },
            },
          ]}
          value={data}
          editable={{
            type: 'single',
            editableKeys,
            onChange: setEditableKeys,
            actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
            onSave: async (rowKey, data, row) => {
              return new Promise((resolve, reject) => {
                onUpdate(
                  {
                    idc: rowKey as string,
                    deptIds: data.deptIds ?? [],
                  },
                  success => {
                    if (success) {
                      message.success('保存成功');
                      setEditableKeys([]);
                      onReloadData();
                      resolve(true);
                    } else {
                      reject(new Error('保存失败'));
                    }
                  }
                );
              });
            },
          }}
          pagination={{
            defaultPageSize: 10,
            showTotal: total => `共 ${total} 条`,
            pageSizeOptions: ['10', '20', '30', '50', '100', '200'],
            showSizeChanger: true,
            showQuickJumper: data.length > 10,
          }}
        />
      </Space>
    </Card>
  );
};
