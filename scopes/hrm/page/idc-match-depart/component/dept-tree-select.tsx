/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import type { DeptInfo } from '@manyun/hrm.service.fetch-involved-performance-staff-department';
import { fetchInvolvedPerformanceStaffDepartment } from '@manyun/hrm.service.fetch-involved-performance-staff-department';

type RefProps = {
  focus: () => void;
  blur: () => void;
  scrollTo: () => void;
};

export type DeptTreeSelectProps = TreeSelectProps<DeptInfo[]>;

export type DeptTreeNode = Pick<DeptInfo, 'id' | 'nameZh' | 'fullDeptName'> & {
  children?: DeptTreeNode[];
  disabled?: boolean;
};

export const DeptTreeSelect = React.forwardRef(
  (
    {
      placeholder,
      disabledKeys = [],
      disabledRoot,
      ...otherProps
    }: DeptTreeSelectProps & {
      treeData?: DeptTreeNode[];
      disabledKeys?: string[];
      disabledRoot?: boolean;
    },
    ref: React.Ref<RefProps>
  ) => {
    const [{ loading, treeData }, onLoadData] = useDeptTreeData();
    const _treeData = React.useMemo(() => {
      if ('treeData' in otherProps) {
        return processTreeData(
          (otherProps?.treeData as Pick<
            DeptInfo,
            'id' | 'nameZh' | 'fullDeptName' | 'children'
          >[]) ?? [],
          disabledKeys,
          false,
          disabledRoot
        );
      }

      return treeData;
    }, [otherProps, treeData, disabledKeys, disabledRoot]);

    useEffect(() => {
      if ('treeData' in otherProps) {
        return;
      }
      onLoadData(disabledKeys, disabledRoot);
    }, [disabledKeys, disabledRoot, onLoadData, otherProps]);

    return (
      <TreeSelect
        ref={ref}
        loading={loading}
        style={{ width: '100%' }}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder={placeholder}
        allowClear
        {...otherProps}
        filterTreeNode={(input, treeNode) => {
          // 使用 nameZh 和 fullDeptName 进行搜索匹配
          const nameMatch = treeNode?.nameZh?.toLowerCase().includes(input.toLowerCase());
          const fullNameMatch = treeNode?.fullDeptName?.toLowerCase().includes(input.toLowerCase());
          return nameMatch || fullNameMatch;
        }}
        fieldNames={{
          label: 'nameZh',
          value: 'id',
          children: 'children',
        }}
        treeData={_treeData}
        treeNodeLabelProp="fullDeptName"
      />
    );
  }
);

DeptTreeSelect.displayName = 'DeptTreeSelect';

export const useDeptTreeData = () => {
  const [treeData, setTreeData] = useState<DeptTreeNode[]>([]);
  const [loading, setLoading] = useState(false);

  const onLoadData = React.useCallback(async (disabledKeys?: string[], disableRoot?: boolean) => {
    setLoading(true);
    const { error, data } = await fetchInvolvedPerformanceStaffDepartment({});
    setLoading(false);
    if (error) {
      message.error(error.message);
      setTreeData([]);
      return;
    }
    const processedData = processTreeData(data.data, disabledKeys, true, disableRoot);
    setTreeData(processedData);
  }, []);

  return [{ loading, treeData }, onLoadData] as const;
};

export const processTreeData = (
  nodes: Pick<DeptInfo, 'id' | 'nameZh' | 'fullDeptName' | 'children'>[],
  disabledKeys?: string[],
  excludeFirstDept = false,
  disableRoot = false
): DeptTreeNode[] => {
  return nodes.map(node => ({
    id: node.id,
    deptId: node.id,
    nameZh: node.nameZh,
    // 产品要求：同步数据则排除第一个部门（即数据中心）
    fullDeptName: excludeFirstDept ? generateFullDeptName(node.fullDeptName) : node.fullDeptName,
    disabled: (disabledKeys ?? []).includes(node.id) || disableRoot,
    children: node.children
      ? processTreeData(node.children, disabledKeys, excludeFirstDept)
      : undefined,
  }));
};

export const generateFullDeptName = (fullDeptName: string): string => {
  return fullDeptName.split('-').slice(1).join('-');
};
