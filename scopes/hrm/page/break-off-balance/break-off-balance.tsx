import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import { fetchUsersByIdsWeb } from '@manyun/auth-hub.service.pm.fetch-users-by-ids';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BreakOffBalacneRouteParams } from '@manyun/hrm.route.hrm-routes';
import { fetchUserBreakOffBalance } from '@manyun/hrm.service.fetch-user-break-off-balance';
import type { BreakOffBalance } from '@manyun/hrm.service.fetch-user-break-off-balance';
import { AdjustBalanceModalView } from '@manyun/hrm.ui.adjust-balance-modal-view';
import { BalanceChangeRecordTable } from '@manyun/hrm.ui.balance-change-record-table';

import styles from './break-off-balance.module.less';

export type BreakoffBalanceStore = {
  loading: boolean;
  selectedKey: string | null;
  keys: string[];
  entities: Record<string, BreakOffBalance>;
  totalBalance: number;
  totalAvailableLeave: number;
  totalApprovingBalance: number;
  userName: string;
};

export const defalueValues: BreakoffBalanceStore = {
  loading: false,
  selectedKey: null,
  keys: [],
  entities: {},
  totalBalance: 0,
  totalApprovingBalance: 0,
  totalAvailableLeave: 0,
  userName: '',
};

export function BreakOffBalancePage() {
  const { id } = useParams<BreakOffBalacneRouteParams>();
  const [state, setState] = useState<BreakoffBalanceStore>(defalueValues);

  const _fetchUserBreakoutBalance = useCallback(
    async (expireDate?: string) => {
      setState(pre => ({ ...pre, loading: true }));
      const { error, data } = await fetchUserBreakOffBalance({ staffId: Number(id) });
      setState(pre => ({ ...pre, loading: false }));
      if (error) {
        message.error(error.message);
        return;
      }
      const keys: string[] = [];
      const entities: Record<string, BreakOffBalance> = {};
      let totalBalance = 0,
        approvingBalance = 0,
        availableLeave = 0;
      data.data.forEach(d => {
        keys.push(d.expireDate);
        entities[d.expireDate] = d;
        totalBalance += Number(d.paidLeaveBalance);
        availableLeave += Number(d.availablePaidLeave);
        approvingBalance += Number(d.freezePaidLeave);
      });
      setState(pre => ({
        ...pre,
        entities: entities,
        keys: keys,
        selectedKey: expireDate ?? keys[0],
        totalBalance,
        totalApprovingBalance: approvingBalance,
        totalAvailableLeave: availableLeave,
      }));
    },
    [id]
  );

  const _fetchUserNameById = useCallback(async () => {
    const { data, error } = await fetchUsersByIdsWeb({ userIds: [Number(id)] });
    if (error) {
      message.error(error.message);
      return;
    }
    const userInfo = data.data[0];
    if (userInfo) {
      setState(pre => ({ ...pre, userName: userInfo.name }));
    }
  }, [id]);

  useEffect(() => {
    _fetchUserBreakoutBalance();
    _fetchUserNameById();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const dataList = useMemo(() => {
    return state.keys.map(key => state.entities[key]);
  }, [state.entities, state.keys]);

  const dataSource = useMemo(() => {
    const entity = state.entities[state.selectedKey as string];
    return entity ? entity.changeRecordDtoList : [];
  }, [state.entities, state.selectedKey]);

  return (
    <Space align="start" className={styles.breakOffBalaceContainer} size={16}>
      <Card style={{ height: '100%', overflowY: 'auto' }}>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }} align="center" size={0}>
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              {`${state.userName}的调休余额`}
            </Typography.Title>
            <AdjustBalanceModalView
              className={styles.goSetting}
              type="PAID_LEAVE"
              userId={Number(id)}
              currentBalance={state.totalBalance}
              onSuccess={expireDate => {
                _fetchUserBreakoutBalance(expireDate);
              }}
            />
          </Space>
          <Space direction="vertical" size={16} style={{ width: '100%' }}>
            <Space align="baseline">
              <Typography.Title style={{ fontWeight: 400 }} level={5}>
                当前余额：
                <span style={{ color: `var(--${prefixCls}-warning-color)`, fontWeight: 500 }}>
                  {state.totalBalance}
                </span>
                小时
              </Typography.Title>
              <Typography.Text type="secondary">
                (可用{state.totalAvailableLeave}小时，审批中{state.totalApprovingBalance}小时)
              </Typography.Text>
            </Space>
            <BalanceList
              userId={Number(id)}
              activeKey={state.selectedKey}
              data={dataList}
              onChange={item => {
                setState(pre => ({ ...pre, selectedKey: item.expireDate }));
              }}
              onRefresh={expireDate => {
                _fetchUserBreakoutBalance(expireDate);
              }}
            />
          </Space>
        </Space>
      </Card>
      <Card title="额度变更记录" bordered={false} style={{ height: '100%', overflowY: 'auto' }}>
        <BalanceChangeRecordTable
          loading={state.loading}
          breakOffOnly
          rowKey="id"
          dataSource={dataSource}
          showColumns={[
            'startBalance',
            'endBalance',
            'reason',
            'createTime',
            'operatorId',
            'fileList',
          ]}
          pagination={false}
        />
      </Card>
    </Space>
  );
}

type BalanceListProps = {
  activeKey: string | null;
  userId: number;
  data: BreakOffBalance[];
  onRefresh: (expireDate?: string) => void;
  onChange?: (value: BreakOffBalance) => void;
};

const BalanceList = ({ activeKey, userId, data, onRefresh, onChange }: BalanceListProps) => {
  if (data.length === 0) {
    return <Empty description="暂无数据" />;
  }
  return (
    <Space direction="vertical" size={0} className={styles.balanceList}>
      {data.map(d => (
        <div
          key={d.expireDate}
          onClick={() => {
            onChange && onChange(d);
          }}
        >
          <Descriptions
            layout="vertical"
            column={2}
            colon={false}
            size="small"
            className={classNames(
              styles.descriptionContainer,
              activeKey === d.expireDate && styles.activeDescriptionContainer
            )}
          >
            <Descriptions.Item
              label="额度有效期"
              labelStyle={{ color: 'var(--text-color-secondary)' }}
            >
              {d.expireDate}
              {d.closeExpire && (
                <Tooltip placement="topLeft" title="超过有效期，未使用的额度将自动作废">
                  <Tag color="error" style={{ marginLeft: '5px' }}>
                    快过期
                  </Tag>
                </Tooltip>
              )}
            </Descriptions.Item>
            <Descriptions.Item
              label="可用额度"
              labelStyle={{ color: 'var(--text-color-secondary)' }}
            >
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Space size={0}>
                  {d.availablePaidLeave}小时
                  {d.freezePaidLeave > 0 ? `(审批中${d.freezePaidLeave}小时)` : ''}
                </Space>
                <AdjustBalanceModalView
                  className={styles.goSetting}
                  type="PAID_LEAVE"
                  userId={userId}
                  expireDate={d.expireDate}
                  currentBalance={d.availablePaidLeave}
                  onSuccess={onRefresh}
                />
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </div>
      ))}
    </Space>
  );
};
