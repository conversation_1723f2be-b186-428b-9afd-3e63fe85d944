/* eslint-disable @typescript-eslint/no-explicit-any */
import { useApolloClient } from '@apollo/client';
import flatten from 'lodash.flatten';
import get from 'lodash.get';
import uniq from 'lodash.uniq';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { ThemeProvider as ChartThemeProvider } from '@manyun/base-ui.chart.theme';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { ColumnType } from '@manyun/base-ui.ui.edit-columns';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { SheetHeader } from '@manyun/base-ui.util.xlsx';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import { Link } from '@manyun/dc-brain.navigation.link';
import {
  useAnnualPerformancesStatisticsChartInfos,
  useAnnualPerformancesUsersStatistics,
  usePerformancePlanById,
} from '@manyun/hrm.gql.client.hrm';
import type { AnnualPerformanceUsersGrades } from '@manyun/hrm.gql.client.hrm';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';
import type { BackendGrade } from '@manyun/hrm.model.performance-objective';
import { generateAnnualPerformanceDetail } from '@manyun/hrm.route.hrm-routes';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { StatisticGradeBar } from './components/statistic-grade-bar';
import { StatisticPeriodBar } from './components/statistic-period-bar';

type ColumnRecordType = ColumnType<AnnualPerformanceUsersGrades> & {
  stringify: SheetHeader<AnnualPerformanceUsersGrades>['stringify'];
};

export function AnnualPerformancesStatistics() {
  const client = useApolloClient();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [year, setYear] = useState(moment().get('year').toString());
  const [period, setPeriod] = useState<BackendPerformancePeriod | 'all'>('all');
  const [locationType, setLocationType] = useState<'idc' | 'region'>('idc');
  const [fields, setFields] = useState<{
    userId?: number;
    regionTags?: string[];
    idcTags?: string[];
    position?: string;
    hrIds?: number[];
    blockGuids?: string[];
  }>({});
  const { data: performancePlanResult, refetch: refetchPerformancePlan } = usePerformancePlanById({
    variables: { year: year },
  });

  useEffect(() => {
    refetchPerformancePlan({ year: year });
  }, [refetchPerformancePlan, year]);

  const performancePlan = useMemo(() => {
    return performancePlanResult?.performancePlanById;
  }, [performancePlanResult?.performancePlanById]);

  useEffect(() => {
    if (performancePlan) {
      let currentPeriod: BackendPerformancePeriod | 'all' = 'Q1';
      const lastStartIndex = (performancePlan.subConfigs ?? []).findLastIndex(
        config => moment(config.evalStartTimeRange[0]).diff(moment()) < 0
      );
      if (lastStartIndex !== -1) {
        currentPeriod = performancePlan.subConfigs[lastStartIndex].period;
      }
      setPeriod(currentPeriod);
    }
  }, [performancePlan]);

  const {
    data: usersStatistics,
    loading: usersStatisticsLoading,
    refetch: refetchUsersStatistics,
  } = useAnnualPerformancesUsersStatistics({
    variables: {
      query: { year: year },
    },
  });
  const performanceObjectiveLocales = useMemo(() => getPerformanceObjectiveLocales(), []);
  const currentLoading = useMemo(
    () => usersStatisticsLoading || loading,
    [loading, usersStatisticsLoading]
  );

  const generateLink = useCallback(
    (id: number, period: string, record: AnnualPerformanceUsersGrades) => {
      return generateAnnualPerformanceDetail('team', {
        id: id.toString(),
        year: record.year,
        position: record.evaluationJob.value,
        user: record.user.id.toString(),
        period: period,
      });
    },
    []
  );
  const defaultColumns = useMemo<ColumnRecordType[]>(
    () => [
      {
        title: '人员',
        dataIndex: 'userId',
        disabled: true,
        fixed: 'left',
        render: (_, record) => (
          <UserLink userId={record.user.id} userName={record.user.name} target="_blank" />
        ),
        stringify: (_, record) => record.user.name,
      },
      {
        title: '员工工号',
        dataIndex: 'jobNo',
        render: (_, record) => record.user.jobNo ?? '--',
        stringify: (_, record) => record.user.jobNo ?? '--',
      },
      {
        title: '区域',
        dataIndex: 'region',
        render: (_, record) => record.user?.region?.label,
        stringify: (_, record) => record.user?.region?.label ?? '',
      },
      {
        title: '所属机房',
        dataIndex: 'idc',
        disabled: true,
        render: (_, record) => {
          return record.idc.value ? <SpaceText guid={record.idc.value} /> : '--';
        },
        stringify: (_, record) => {
          if (record.idc.value) {
            const idcLabel = readSpace(client, record.idc.value);
            return idcLabel?.label ?? '--';
          }
          return '--';
        },
      },
      {
        title: '主楼栋',
        dataIndex: 'blockGuid',
        render: (_, record) => {
          if (record.blockGuid) {
            const blockLabel = readSpace(client, record.blockGuid);
            return blockLabel?.label ?? '--';
          }
          return '--';
        },
        stringify: (_, record) => {
          let blockGuidText = '--';
          if (record.blockGuid) {
            const blockLabel = readSpace(client, record.blockGuid);
            blockGuidText = `${blockLabel?.label ?? '--'}`;
          }
          return blockGuidText;
        },
      },
      {
        title: '考核岗位',
        dataIndex: 'performancePosition',
        sorter: (a, b) =>
          a.evaluationJob.label && b.evaluationJob.label
            ? a.evaluationJob.label.localeCompare(b.evaluationJob.label)
            : 0,
        render: (_, record) => record.evaluationJob?.label,
        stringify: (_, record) => record.evaluationJob?.label,
      },
      {
        title: '直线经理',
        dataIndex: 'lineManagers',
        sorter: (a, b) =>
          a?.lineManagers && a.lineManagers[0] && b?.lineManagers && b.lineManagers[0]
            ? a.lineManagers[0].name.localeCompare(b.lineManagers[0].name)
            : 0,
        render: (_, record) =>
          record.lineManagers.length > 0
            ? record.lineManagers?.map(user => user.name).join('｜')
            : '--',
        stringify: (_, record) => record.lineManagers?.map(user => user.name).join('｜'),
      },
      {
        title: '二级经理',
        dataIndex: 'secondLineManagers',
        sorter: (a, b) =>
          a?.secondLineManagers &&
          a.secondLineManagers[0] &&
          b?.secondLineManagers &&
          b.secondLineManagers[0]
            ? a.secondLineManagers[0].name.localeCompare(b.secondLineManagers[0].name)
            : 0,
        render: (_, record) =>
          record.secondLineManagers.length > 0
            ? record.secondLineManagers?.map(user => user.name).join('｜')
            : '--',
        stringify: (_, record) =>
          record.secondLineManagers?.map(user => user.name).join('｜') ?? '--',
      },
      {
        title: 'HRBP',
        dataIndex: 'hrs',
        sorter: (a, b) =>
          a?.hrs && a.hrs[0] && b?.hrs && b.hrs[0] ? a.hrs[0].name.localeCompare(b.hrs[0].name) : 0,
        render: hrs => hrs.map(({ name }: { name: string }) => name).join('|'),
        stringify: hrs => hrs.map(({ name }: { name: string }) => name).join('|'),
      },
      {
        title: 'Q1得分',
        dataIndex: 'q1Grade',
        sorter: (a, b) => Number(a?.q1Grade ?? 0) - Number(b?.q1Grade ?? 0),
        render: (val, record) =>
          record.q1Id && val !== null && val >= 0 && record.q1Finished ? (
            <Link href={generateLink(record.q1Id, 'Q1', record)} target="_blank">
              {val}
            </Link>
          ) : typeof val === 'number' ? (
            val < 0 ? (
              '/'
            ) : (
              val
            )
          ) : (
            '--'
          ),
        stringify: val => (typeof val === 'number' ? (val < 0 ? '/' : val.toString()) : '--'),
      },
      {
        title: 'Q2得分',
        dataIndex: 'q2Grade',
        sorter: (a, b) => Number(a?.q2Grade ?? 0) - Number(b?.q2Grade ?? 0),
        stringify: val => (typeof val === 'number' ? (val < 0 ? '/' : val.toString()) : '--'),
        render: (val, record) =>
          record.q2Id && val !== null && val >= 0 && record.q2Finished ? (
            <Link href={generateLink(record.q2Id, 'Q2', record)} target="_blank">
              {val}
            </Link>
          ) : typeof val === 'number' ? (
            val < 0 ? (
              '/'
            ) : (
              val
            )
          ) : (
            '--'
          ),
      },
      {
        title: 'Q3得分',
        dataIndex: 'q3Grade',
        sorter: (a, b) => Number(a?.q3Grade ?? 0) - Number(b?.q3Grade ?? 0),
        stringify: val => (typeof val === 'number' ? (val < 0 ? '/' : val.toString()) : '--'),
        render: (val, record) =>
          record.q3Id && val !== null && val >= 0 && record.q3Finished ? (
            <Link href={generateLink(record.q3Id, 'Q3', record)} target="_blank">
              {val}
            </Link>
          ) : typeof val === 'number' ? (
            val < 0 ? (
              '/'
            ) : (
              val
            )
          ) : (
            '--'
          ),
      },
      {
        title: 'Q4得分',
        dataIndex: 'q4Grade',
        sorter: (a, b) => Number(a?.q4Grade ?? 0) - Number(b?.q4Grade ?? 0),
        stringify: val => (typeof val === 'number' ? (val < 0 ? '/' : val.toString()) : '--'),
        render: (val, record) =>
          record.q4Id && val !== null && val >= 0 && record.q4Finished ? (
            <Link href={generateLink(record.q4Id, 'Q4', record)} target="_blank">
              {val}
            </Link>
          ) : typeof val === 'number' ? (
            val < 0 ? (
              '/'
            ) : (
              val
            )
          ) : (
            '--'
          ),
      },
      {
        title: '年度最终得分',
        dataIndex: 'yearGrade',
        sorter: (a, b) => Number(a?.yearGrade ?? 0) - Number(b?.yearGrade ?? 0),
        disabled: true,
        stringify: val => (val >= 0 ? val : '--'),
        render: (val, record) =>
          record.yearId && val !== null && val >= 0 && record.yearFinished ? (
            <Link href={generateLink(record.yearId, 'YEAR', record)} target="_blank">
              {val}
            </Link>
          ) : (
            (val ?? '--')
          ),
      },
      {
        title: '考核结果',
        dataIndex: 'result',
        sorter: (a, b) => Number(a?.result ?? 0) - Number(b?.result ?? 0),
        disabled: true,
        render: val =>
          val
            ? performanceObjectiveLocales.grade.annual[PERFORMANCE_SECOND_VERSION][
                val as BackendGrade
              ]
            : '--',
        stringify: val =>
          val ? performanceObjectiveLocales.grade.enum[val as BackendGrade] : '--',
      },
    ],
    [
      client,
      generateLink,
      performanceObjectiveLocales.grade.annual,
      performanceObjectiveLocales.grade.enum,
    ]
  );

  const [tableColumns, setTableColumns] = useState<ColumnRecordType[]>(defaultColumns);

  useShallowCompareEffect(() => {
    setLoading(true);
    refetchUsersStatistics({
      query: {
        year,
        period: period === 'all' ? undefined : period,
        ...fields,
      },
    })
      .then(() => setLoading(false))
      .catch(() => setLoading(false));
  }, [fields, period, refetchUsersStatistics, year]);

  const usersStatisticList = useMemo(() => {
    // const _data = [
    //   ...((usersStatistics?.annualPerformanceUsersStatistics.data ??
    //     []) as AnnualPerformanceUsersGrades[]),
    // ];
    // /**第一排序.区域字母正序，第二排序.机房字母正序 */
    // return _data.sort((a, b) => {
    //   return (
    //     a.user.region!.label.localeCompare(b.user.region!.label) ||
    //     a.idc.label.localeCompare(b.idc.label)
    //   );
    // });
    return usersStatistics?.annualPerformanceUsersStatistics.data ?? [];
  }, [usersStatistics?.annualPerformanceUsersStatistics.data]);

  const { data, loading: chartLoading } = useAnnualPerformancesStatisticsChartInfos({
    variables: {
      query: {
        year: year,
        period: period === 'all' ? undefined : period,
        idcTags: fields.idcTags,
        regionTags: fields.regionTags,
        hrIds: fields.hrIds,
        evalPosition: fields.position,
      },
    },
  });

  const planLocales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  const usersGradeIntervalRecords = useMemo(() => {
    return data?.annualPerformanceStatistics?.gradeIntervalResult ?? [];
  }, [data?.annualPerformanceStatistics?.gradeIntervalResult]);

  const usersPeriodIntervalRecords = useMemo(() => {
    return data?.annualPerformanceStatistics?.statusIntervalResult ?? [];
  }, [data?.annualPerformanceStatistics?.statusIntervalResult]);

  const chartLocations = useMemo(
    () =>
      uniq(
        usersGradeIntervalRecords.flatMap(item => item.data).map(record => get(record, 'label', ''))
      ) as string[],
    [usersGradeIntervalRecords]
  );
  const maxLocationLength = useMemo(
    () =>
      chartLocations.reduce((len, location) => {
        const current = Math.max(len, location.length);
        return current;
      }, 0),
    [chartLocations]
  );
  const chartLeft = useMemo(() => Math.min(maxLocationLength * 20, 216), [maxLocationLength]);

  useEffect(() => {
    setTableColumns(() => {
      const columns = [...defaultColumns].filter(({ dataIndex }) => {
        let hiddenColumns: string[] = [];
        if (period === 'Q1') {
          hiddenColumns = ['q2Grade', 'q3Grade', 'q4Grade', 'yearGrade', 'result'];
        } else if (period === 'Q2') {
          hiddenColumns = ['q1Grade', 'q3Grade', 'q4Grade', 'yearGrade', 'result'];
        } else if (period === 'Q3') {
          hiddenColumns = ['q1Grade', 'q2Grade', 'q4Grade', 'yearGrade', 'result'];
        } else if (period === 'Q4') {
          hiddenColumns = ['q1Grade', 'q2Grade', 'q3Grade', 'yearGrade', 'result'];
        } else if (period === 'YEAR') {
          hiddenColumns = ['q1Grade', 'q2Grade', 'q3Grade', 'q4Grade'];
        }
        return !hiddenColumns.includes(dataIndex as string);
      });

      return columns;
    });
  }, [defaultColumns, period]);

  return (
    <ChartThemeProvider theme="datav_light">
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Container style={{ padding: '16px 8px 16px 24px' }}>
          <Space
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
              padding: 0,
            }}
            direction="horizontal"
          >
            <Typography.Title style={{ margin: '4px 0', fontWeight: 500 }} level={4}>
              年度绩效报表
            </Typography.Title>
            <Space size={24}>
              <DatePicker
                style={{ width: 140 }}
                picker="year"
                format={value => `${value.format('YYYY')}年度`}
                value={moment().set('year', Number(year))}
                allowClear={false}
                disabledDate={current => current && current.diff(moment(), 'year') > 0}
                onChange={value => {
                  value && setYear(moment(value).get('year').toString());
                }}
              />
              <Radio.Group
                value={period}
                optionType="button"
                options={[
                  { label: '全部', value: 'all' },
                  ...Object.keys(planLocales.performancePeriod.enum)
                    .filter(key => !['FIR_HALF_YEAR', 'SEC_HALF_YEAR'].includes(key as string))
                    .map(key => ({
                      label: planLocales.performancePeriod.enum[key as BackendPerformancePeriod],
                      value: key,
                    })),
                ]}
                onChange={e => {
                  if (e.target.value) {
                    setPeriod(e.target.value);
                  }
                }}
              />
              <Space size={16}>
                <Radio.Group
                  value={locationType}
                  optionType="button"
                  options={[
                    { label: '机房', value: 'idc' },
                    {
                      label: '区域',
                      value: 'region',
                    },
                  ]}
                  onChange={({ target }) => {
                    const locationType = target.value;
                    setLocationType(locationType);
                    const locationTypeIsIdc = locationType === 'idc';
                    setFields(prev => ({
                      ...prev,
                      idcTags: locationTypeIsIdc ? [] : undefined,
                      regionTags: locationTypeIsIdc ? undefined : [],
                    }));
                  }}
                />
                <Form form={form} layout="inline">
                  {locationType === 'region' ? (
                    <Form.Item name="region">
                      <Select
                        style={{ width: 300 }}
                        maxTagCount="responsive"
                        showArrow
                        mode="multiple"
                        allowClear
                        placeholder="请选择区域"
                        options={
                          usersStatistics?.annualPerformanceUsersStatistics.userRegions ?? []
                        }
                        onChange={value => {
                          setFields(pre => ({
                            ...pre,
                            regionTags:
                              Array.isArray(value) && value.length > 0 //清空时需传递用户授权的所有区域
                                ? value
                                : [],
                          }));
                        }}
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item name="idc">
                      <LocationCascader
                        style={{ width: 300 }}
                        placeholder="请选择机房"
                        nodeTypes={['IDC']}
                        changeOnSelect
                        maxTagCount="responsive"
                        authorizedOnly
                        showArrow
                        multiple
                        allowClear
                        onChange={value => {
                          setFields(pre => ({
                            ...pre,
                            idcTags: Array.isArray(value)
                              ? (flatten(value) as string[])
                              : undefined,
                          }));
                        }}
                      />
                    </Form.Item>
                  )}
                </Form>
              </Space>
            </Space>
          </Space>
        </Container>
        <Card>
          {period !== 'all' && (
            <Space style={{ width: '100%', paddingBottom: 48 }} direction="vertical" size="middle">
              <Space
                style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}
                direction="horizontal"
              >
                <Typography.Title style={{ margin: '4px 0' }} showBadge level={5}>
                  绩效报表概览
                </Typography.Title>
              </Space>
              <Container style={{ padding: 24 }} bordered>
                <Spin style={{ width: '100%', height: '100%' }} spinning={chartLoading}>
                  <StatisticGradeBar
                    usersGradeIntervalRecords={usersGradeIntervalRecords}
                    chartLocations={chartLocations}
                    chartLeft={chartLeft}
                    period={period}
                    locationType={locationType}
                  />
                </Spin>
              </Container>
              <Container style={{ padding: 24 }} bordered>
                <Spin style={{ width: '100%', height: '100%' }} spinning={chartLoading}>
                  <StatisticPeriodBar
                    usersPeriodIntervalRecords={usersPeriodIntervalRecords}
                    chartLocations={chartLocations}
                    chartLeft={chartLeft}
                    period={period}
                    locationType={locationType}
                  />
                </Spin>
              </Container>
            </Space>
          )}
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ margin: '4px 0' }} showBadge level={5}>
              人员绩效明细
              <Typography.Text style={{ paddingLeft: 4, fontSize: 12 }} type="secondary">
                “未开始”状态的考核，得分为预估分数
              </Typography.Text>
            </Typography.Title>
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Form form={form} layout="inline">
                <Form.Item name="user">
                  <UserSelect
                    style={{ width: 192 }}
                    placeholder="请选择人员"
                    allowClear
                    labelInValue={false}
                    onChange={value => {
                      setFields(pre => ({
                        ...pre,
                        userId: value,
                      }));
                    }}
                  />
                </Form.Item>
                <Form.Item name="position">
                  <PerformancePositionSelect
                    style={{ width: 192 }}
                    placeholder="请选择岗位"
                    allowClear
                    showDelete
                    onChange={value => {
                      setFields(pre => ({
                        ...pre,
                        position: value,
                      }));
                    }}
                  />
                </Form.Item>
                <Form.Item name="hr">
                  <Select
                    style={{ width: 192 }}
                    placeholder="请选择HRBP"
                    mode="multiple"
                    showArrow
                    showSearch={false}
                    maxTagCount="responsive"
                    options={(usersStatistics?.annualPerformanceUsersStatistics.hrbps ?? []).map(
                      user => ({
                        label: user.name,
                        value: user.id,
                      })
                    )}
                    allowClear
                    getPopupContainer={trigger => trigger.parentNode}
                    onChange={value => {
                      setFields(pre => ({
                        ...pre,
                        hrIds: value?.length > 0 ? value : undefined,
                      }));
                    }}
                  />
                </Form.Item>
              </Form>
              <Space>
                <FileExport
                  showExportFiltered={false}
                  data={async () => {
                    const newHeaders: SheetHeader<AnnualPerformanceUsersGrades>[] = tableColumns
                      .filter(cln => cln.show !== false)
                      .map(column => ({
                        dataIndex: column.dataIndex as string,
                        title: column.title as string,
                        stringify: column.stringify,
                      }));
                    saveJSONAsXLSX<AnnualPerformanceUsersGrades>(
                      [
                        {
                          data: usersStatisticList,
                          headers: newHeaders,
                        },
                      ],
                      '人员绩效明细'
                    );
                  }}
                />
              </Space>
            </Space>
            <Table
              rowKey={record => record.user.id}
              scroll={{ x: 'max-content' }}
              loading={currentLoading}
              dataSource={usersStatisticList}
              columns={tableColumns}
            />
          </Space>
        </Card>
      </Space>
    </ChartThemeProvider>
  );
}
