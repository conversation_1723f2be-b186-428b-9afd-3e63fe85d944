import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import groupby from 'lodash.groupby';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory, useParams } from 'react-router';

import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import {
  useLazyAnnualPerformanceObjective,
  useLazyPerformanceGrade,
  useLazyUserPerformanceAlreadyAddedScore,
} from '@manyun/hrm.gql.client.hrm';
import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import { getDailyPerformanceGradeLocales } from '@manyun/hrm.model.daily-performance-grade';
import type { AnnualPerformanceObjectiveEditParams } from '@manyun/hrm.route.hrm-routes';
import { createPerformanceDailyGrade } from '@manyun/hrm.service.create-performance-daily-grade';
import { fetchUserBindPerformancePositionByYear } from '@manyun/hrm.service.fetch-user-bind-performance-position-by-year';
import { updatePerformanceDailyGrade } from '@manyun/hrm.service.update-performance-daily-grade';
import { whetherCanAddDailyPerformanceGrade } from '@manyun/hrm.service.whether-can-add-daily-performance-grade';
import { PerformanceUserSelect } from '@manyun/hrm.ui.performance-user-select';
import { PERFORMANCE_SECOND_VERSION, getPerformanceVersion } from '@manyun/hrm.util.performances';

import { AnnualPerformanceObjectivesSelect } from './components/annual-performance-objectives-select';
import { ErrorStatusContainer, SpaceContainer, SpaceWarper } from './styles';

const formItem = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 20,
  },
};

const GRADE_CRITERIA_CONTEXT = 'context';
const GRADE_CRITERIA_SCORE = 'score';

type GradeCriteria = {
  id: number;
  defaultGrade: number[];
  gradeCriteria: string;
  ceiling?: number;
};
export function DailyPerformanceGradeMutator() {
  const params = useParams<AnnualPerformanceObjectiveEditParams>();
  const [preOccurTime, setPreOccurTime] = useState<moment.Moment | null>(null);
  const { id } = params;
  const locales = useMemo(() => getDailyPerformanceGradeLocales(), []);
  const objectivesLocales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);
  const history = useHistory();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [userEvalPosition, setUserEvalPosition] = useState<string | null>(null);
  const [validateCanAddErrorCode, setValidateCanAddErrorCode] = useState<number>(0);
  const [userHireDate, setUserHireDate] = useState<number | undefined>(undefined);
  const [gradeCriteriaList, setGradeCriteriaList] = useState<{ id: number; context: string }[]>([]);

  const [form] = Form.useForm();
  const user = Form.useWatch('user', form);
  const occurTime = Form.useWatch('occurTime', form);
  const type = Form.useWatch('type', form);
  const attachments = Form.useWatch('attachments', form);

  const [fetch, { loading, data: detail }] = useLazyPerformanceGrade();
  const [fetchRelatedObjective] = useLazyAnnualPerformanceObjective();
  const [getPerformanceAlreadyAddedScore] = useLazyUserPerformanceAlreadyAddedScore();

  useEffect(() => {
    if (id) {
      fetch({ variables: { id: Number(id) } })
        .then(result => {
          if (result.data?.performanceGradeById) {
            const record = result.data.performanceGradeById;
            form.setFieldsValue({
              ...record,
              user: {
                label: record.user.name,
                value: record.user.id,
              },
              occurTime: moment(record.occurTime),
              name: {
                label: record.name,
                value: record.relatedObjectiveId,
              },
              grade: Math.abs(record.grade),
            });
            fetchRelatedObjective({
              variables: { id: result.data.performanceGradeById.relatedObjectiveId },
            }).then(res => {
              const objectiveRecord = res.data?.annualPerformanceObjectiveById;
              if (objectiveRecord) {
                const _mergedMeasurements = groupby(objectiveRecord.measurements, 'context');
                const gradeCriteriaList = _mergedMeasurements[record.measurements];
                setGradeCriteriaList(
                  gradeCriteriaList.map(item => ({ id: item.id!, context: item.gradeCriteria! }))
                );
                form.setFieldsValue({
                  /**指标名称 */
                  name: {
                    measurements: objectiveRecord.measurements,
                    label: objectiveRecord.name,
                    value: objectiveRecord.id,
                  },
                  /**衡量标准 */
                  measurements: {
                    gradeCriteriaList: gradeCriteriaList,
                    label: record.measurements,
                    value: record.measurements,
                  },
                });
                /**动态生成评分标准 */
                record.gradeCriteria.forEach(item => {
                  form.setFieldsValue({
                    [generateFiledName(item.id, GRADE_CRITERIA_CONTEXT)]: true,
                    [generateFiledName(item.id, GRADE_CRITERIA_SCORE)]: Math.abs(item.defaultGrade),
                  });
                });
              }
            });
          }
        })
        .catch(console.error);
    }
  }, [fetch, fetchRelatedObjective, form, id]);

  useEffect(() => {
    if (user?.value) {
      (async () => {
        const { error, data } = await fetchUser({ id: user.value, needValid: false });
        if (error) {
          message.error(error.message);
          return;
        }
        setUserHireDate(data?.hiredDate);
      })();
    }
  }, [id, user?.value]);

  useEffect(() => {
    if (user && user.value && occurTime && type) {
      (async () => {
        const { error, data } = await whetherCanAddDailyPerformanceGrade({
          staffId: user.value,
          occurTime: moment(occurTime).startOf('day').valueOf(),
          type: type,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        setValidateCanAddErrorCode(data);
        form.validateFields(['occurTime']);
        const { error: err, data: evalPosition } = await fetchUserBindPerformancePositionByYear({
          userId: user.value,
          year: moment(occurTime).year().toString(),
        });

        if (err) {
          message.error(err.message);
          return;
        }
        setUserEvalPosition(evalPosition);
      })();
    }
  }, [form, occurTime, type, user]);

  const calcTotalGradeCriteria = useCallback(() => {
    const totalGradeCriteria = gradeCriteriaList.reduce(
      (acc, item) => {
        const scoreFieldName = generateFiledName(item.id, GRADE_CRITERIA_SCORE);
        const checkedFieldName = generateFiledName(item.id, GRADE_CRITERIA_CONTEXT);

        const _score = form.getFieldValue(scoreFieldName);
        const _checked = form.getFieldValue(checkedFieldName);

        if (_checked && _score) {
          acc.totalGrade += Math.abs(Number(_score));
          acc.gradeCriteria.push({
            id: item.id,
            context: item.context,
            grade: Math.abs(Number(_score)),
          });
        }

        return acc;
      },
      { totalGrade: 0, gradeCriteria: [] as { id: number; context: string; grade: number }[] }
    );

    return totalGradeCriteria;
  }, [form, gradeCriteriaList]);

  const onSubmit = useCallback(() => {
    form
      .validateFields()
      .then(async values => {
        const totalGradeCriteria = calcTotalGradeCriteria();
        if (totalGradeCriteria.totalGrade === 0) {
          return message.error('请先选择评分标准');
        }
        setSubmitLoading(true);
        const _values = {
          type: values.type,
          measurements: values.measurements?.value,
          subType: 'BIZ' as BackendAnnualPerformanceObjectiveSubType,
          staffId: values.user.value,
          staffName: values.user.label,
          relatedObjectiveId: values.name.value,
          name: values.name.label,
          occurTime: moment(values.occurTime).startOf('day').valueOf(),
          grade:
            values.type === 'DAILY'
              ? -totalGradeCriteria.totalGrade
              : totalGradeCriteria.totalGrade,
          attachments: values.attachments,
          gradeCriteria: totalGradeCriteria.gradeCriteria.map(item => ({
            id: item.id,
            criteria: item.context,
            defaultGrade: values.type === 'DAILY' ? -item.grade : item.grade,
          })),
          gradeDesc: values.gradeDesc,
        };
        const { error, data } = id
          ? await updatePerformanceDailyGrade({ ..._values, id: Number(id) })
          : await createPerformanceDailyGrade(_values);
        setSubmitLoading(false);

        if (error) {
          message.error(error.message);
          return;
        }
        if (type === 'DAY_PLUS' && data) {
          message.success(`${id ? '修改' : '添加'}评分记录成功`);
          history.push(generateBPMRoutePath({ id: data.toString() }));
        } else {
          message.success(`${id ? '修改' : '添加'}评分记录成功`);
          history.goBack();
        }
      })
      .catch(console.error);
  }, [calcTotalGradeCriteria, form, history, id, type]);

  const uploadLoading = useDeepCompareMemo(() => {
    return (attachments ?? []).some(
      (attachment: { status: string }) => attachment.status === 'uploading'
    );
  }, [attachments]);

  const typeTypeVersion = React.useMemo(() => {
    const year = moment(occurTime).year();

    return getPerformanceVersion(year);
  }, [occurTime]);

  const shouldApprove = React.useMemo(() => {
    return type === 'DAY_PLUS' && typeTypeVersion === PERFORMANCE_SECOND_VERSION;
  }, [type, typeTypeVersion]);

  const isGradePlus = React.useMemo(() => {
    return ['DAY_PLUS', 'YEAR'].includes(type);
  }, [type]);

  return (
    <Card loading={loading}>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          {id ? '修改' : '添加'}评分记录
        </Typography.Title>
        <Form
          form={form}
          {...formItem}
          initialValues={
            id
              ? {}
              : {
                  type: 'DAILY',
                }
          }
        >
          <Form.Item
            name="user"
            label={locales.gradedUser.__self}
            rules={[
              {
                required: true,
                message: `${locales.gradedUser.__self}必填`,
              },
            ]}
          >
            <PerformanceUserSelect
              style={{ width: 216 }}
              includeCurrentUser={false}
              labelInValue
              disabled={!!id}
              onChange={() => {
                setGradeCriteriaList([]);
                form.setFieldsValue({
                  occurTime: undefined,
                  name: undefined,
                  measurements: undefined,
                });
              }}
            />
          </Form.Item>
          <Form.Item label={locales.type}>
            <Space size={0} align="center">
              <Form.Item
                name="type"
                noStyle
                rules={[
                  {
                    required: true,
                    message: `${locales.type}必选`,
                  },
                ]}
              >
                <Radio.Group
                  options={Object.keys(objectivesLocales.type.textMapper[typeTypeVersion])
                    .filter(key => key !== 'RED_LINE')
                    .map(key => ({
                      label:
                        objectivesLocales.type.textMapper[typeTypeVersion][
                          key as BackendAnnualPerformanceObjectiveType
                        ],
                      value: key,
                    }))}
                  onChange={() => {
                    setGradeCriteriaList([]);
                    form.setFieldsValue({ name: undefined, measurements: undefined });
                  }}
                />
              </Form.Item>
              {shouldApprove && (
                <Typography.Text type="secondary">
                  季度加分录入后，需绩效管理员审批确认后方可生效
                </Typography.Text>
              )}
            </Space>
          </Form.Item>
          <Form.Item
            name="occurTime"
            label={locales.occurTime}
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value) {
                    return Promise.reject(new Error(`${locales.occurTime}必选`));
                  }
                  if (validateCanAddErrorCode !== 0 && value) {
                    return Promise.reject(
                      new Error(generateValidErrorMessage(validateCanAddErrorCode, occurTime))
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={current =>
                current &&
                ((userHireDate && current.diff(moment(userHireDate).startOf('day')) < 0) ||
                  current.diff(moment().endOf('day')) > 0 ||
                  /**编辑时不可跨年 */
                  (!!id && !current.isBetween(moment().startOf('year'), moment().endOf('year'))))
              }
              onChange={val => {
                /**跨年清空指标内容 */
                if (val && preOccurTime && !val.isSame(preOccurTime, 'year')) {
                  form.setFieldsValue({ name: undefined, measurements: undefined });
                }
                setPreOccurTime(val);
              }}
            />
          </Form.Item>
          <Form.Item
            shouldUpdate={(pre, next) => pre.type !== next.type || pre.subType !== next.subType}
            noStyle
          >
            {({ getFieldValue }) => {
              return (
                <Form.Item
                  name="name"
                  label={locales.name}
                  rules={[
                    {
                      required: true,
                      message: `${locales.name}必填`,
                    },
                  ]}
                >
                  <AnnualPerformanceObjectivesSelect
                    style={{ width: 344 }}
                    disabled={!user}
                    labelInValue
                    trigger="onFocus"
                    showSearch
                    staffId={user?.value}
                    occurTime={occurTime ? moment(occurTime).startOf('day').valueOf() : undefined}
                    performancePosition={userEvalPosition}
                    type={getFieldValue('type')}
                    filterOption={(input, option) =>
                      (option?.label as string).toLowerCase().includes(input.toLowerCase())
                    }
                    onChange={() => {
                      setGradeCriteriaList([]);
                      form.setFieldsValue({ measurements: undefined });
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item shouldUpdate={(pre, next) => pre.name !== next.name} noStyle>
            {({ getFieldValue }) => {
              const _mergedMeasurements = getFieldValue('name')?.measurements
                ? groupby(getFieldValue('name').measurements, 'context')
                : {};

              return (
                <Form.Item
                  name="measurements"
                  label={locales.measurements}
                  getValueFromEvent={(value, _option) => {
                    return typeof value === 'object'
                      ? {
                          ...value,
                          gradeCriteriaList: _option.gradeCriteriaList,
                        }
                      : {
                          value,
                          gradeCriteriaList: _option.gradeCriteriaList,
                        };
                  }}
                  rules={[
                    {
                      required: true,
                      message: `${locales.measurements}必选`,
                    },
                  ]}
                >
                  <Select
                    style={{ width: 766 }}
                    labelInValue
                    disabled={!getFieldValue('name')}
                    options={Object.keys(_mergedMeasurements).map(context => ({
                      gradeCriteriaList: _mergedMeasurements[context],
                      value: context,
                      label: context,
                    }))}
                    onChange={(_, option) => {
                      const _option = option as {
                        gradeCriteriaList: GradeCriteria[];
                      };
                      setGradeCriteriaList(
                        (_option?.gradeCriteriaList ?? []).map(item => ({
                          id: item.id,
                          context: item.gradeCriteria,
                        }))
                      );

                      /**
                       * 当仅有一条评分标准需要默认选中该评分标准
                       */
                      if (
                        (_option.gradeCriteriaList ?? []).length === 1 &&
                        _option?.gradeCriteriaList?.[0].id
                      ) {
                        const selectedId = _option.gradeCriteriaList[0].id;
                        const SCORE_FIELD_NAME = generateFiledName(
                          selectedId,
                          GRADE_CRITERIA_SCORE
                        );
                        form.setFieldValue(
                          generateFiledName(selectedId, GRADE_CRITERIA_CONTEXT),
                          true
                        );
                        if (_option.gradeCriteriaList[0].defaultGrade.length === 1) {
                          form.setFieldValue(
                            SCORE_FIELD_NAME,
                            _option.gradeCriteriaList[0].defaultGrade[0]
                          );
                          setTimeout(() => {
                            form.validateFields([SCORE_FIELD_NAME]);
                          }, 200);
                        }
                      }
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item
            shouldUpdate={(pre, next) =>
              pre.name !== next.name || pre.measurements !== next.measurements
            }
            noStyle
          >
            {({ getFieldValue }) => {
              // const type = getFieldValue('type');
              const measurements = getFieldValue('measurements');
              if (!measurements) {
                return null;
              }
              const data: GradeCriteria[] = measurements?.gradeCriteriaList ?? [];
              return (
                <Form.Item style={{ marginBottom: 0 }} label="评分标准" required>
                  <SpaceContainer>
                    <Space
                      style={{ width: 766, marginTop: 2 }}
                      align="start"
                      direction="vertical"
                      size={0}
                      split={
                        <Divider
                          style={{ margin: '0 0 16px', padding: 0, width: 766 }}
                          type="horizontal"
                        />
                      }
                    >
                      {data.map(item => {
                        const filedNameContext = generateFiledName(item.id, GRADE_CRITERIA_CONTEXT);
                        const filedNameScore = generateFiledName(item.id, GRADE_CRITERIA_SCORE);
                        return (
                          <SpaceWarper key={item.id}>
                            <Space style={{ width: '100%', marginBottom: 16 }} align="start">
                              <Form.Item
                                style={{ marginBottom: 0 }}
                                name={filedNameContext}
                                valuePropName="checked"
                              >
                                <Checkbox
                                  disabled={data.length === 1}
                                  onChange={e => {
                                    const checked = e.target.checked;
                                    if (checked) {
                                      if (item.defaultGrade.length === 1) {
                                        form.setFieldsValue({
                                          [filedNameScore]: Math.abs(item.defaultGrade[0]),
                                        });
                                        setTimeout(() => {
                                          form.validateFields([filedNameScore]);
                                        }, 300);
                                      }
                                    }
                                  }}
                                >
                                  <div
                                    style={{ whiteSpace: 'pre-line', color: 'var(--text-color)' }}
                                  >
                                    {item.gradeCriteria}
                                  </div>
                                </Checkbox>
                              </Form.Item>
                              <Form.Item
                                style={{ marginBottom: 0 }}
                                shouldUpdate={(pre, next) =>
                                  pre[filedNameContext] !== next[filedNameContext] ||
                                  pre.measurements !== next.measurements
                                }
                              >
                                {({ getFieldValue }) => {
                                  const checked = getFieldValue(filedNameContext);
                                  if (!checked) {
                                    return;
                                  }
                                  return (
                                    <Space align="start" size={0}>
                                      <Button style={{ paddingLeft: 8, paddingRight: 8 }}>
                                        {isGradePlus ? '加' : '减'}
                                      </Button>
                                      <ErrorStatusContainer>
                                        <Form.Item
                                          style={{ marginBottom: 0 }}
                                          name={filedNameScore}
                                          className={isGradePlus ? 'customerErrorItem' : undefined}
                                          rules={[
                                            {
                                              required: true,
                                              message: `分值必填`,
                                            },
                                            {
                                              validator: async (_, value) => {
                                                if (!isGradePlus) {
                                                  return Promise.resolve();
                                                }
                                                if (!user?.value || !item.ceiling || !occurTime) {
                                                  return Promise.resolve();
                                                }
                                                const { data } =
                                                  await getPerformanceAlreadyAddedScore({
                                                    variables: {
                                                      userId: user.value as number,
                                                      metricsId: item.id,
                                                      occurTime: moment(occurTime).valueOf(),
                                                    },
                                                  });

                                                if (
                                                  data?.userPerformanceAlreadyAddedScore.data !==
                                                  null
                                                ) {
                                                  const addedScore =
                                                    data?.userPerformanceAlreadyAddedScore.data ??
                                                    0;
                                                  const alreadyAddedScore: number = detail
                                                    ? addedScore -
                                                      (detail?.performanceGradeById?.grade ?? 0)
                                                    : (addedScore ?? 0);
                                                  if (value > item.ceiling - alreadyAddedScore) {
                                                    return Promise.reject(
                                                      new Error(
                                                        `同一员工此项加分封顶为${item.ceiling}分,累计已加${alreadyAddedScore}分`
                                                      )
                                                    );
                                                  }
                                                }
                                                return Promise.resolve();
                                              },
                                            },
                                          ]}
                                        >
                                          <Select
                                            style={{ width: isGradePlus ? 135 : 100 }}
                                            disabled={item.defaultGrade.length === 1}
                                            options={item.defaultGrade.map(number => ({
                                              label: `${Math.abs(number)}分`,
                                              value: Math.abs(number),
                                            }))}
                                          />
                                        </Form.Item>
                                      </ErrorStatusContainer>
                                    </Space>
                                  );
                                }}
                              </Form.Item>
                            </Space>
                          </SpaceWarper>
                        );
                      })}
                    </Space>
                  </SpaceContainer>
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item shouldUpdate noStyle>
            {({ getFieldValue }) => {
              const totalGrade = calcTotalGradeCriteria().totalGrade;
              return (
                <Form.Item label={locales.grade}>
                  <InputNumber
                    style={{ width: 216 }}
                    disabled
                    value={Math.abs(totalGrade)}
                    formatter={value => `${value}分`}
                    parser={value => value!.replace('分', '')}
                    addonBefore={
                      getFieldValue('type')
                        ? ['YEAR', 'DAY_PLUS'].includes(getFieldValue('type'))
                          ? '加'
                          : '减'
                        : undefined
                    }
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item
            name="gradeDesc"
            label={locales.gradeDesc}
            rules={[
              {
                required: true,
                whitespace: true,
                message: `${locales.gradeDesc}必填`,
              },
              {
                max: 200,
                message: '最多输入 200 个字符！',
              },
              {
                //See http://chandao.manyun-local.com/zentao/bug-view-11174.html
                pattern: /^((?!~|\.|@|#|¥|&|\*|\||\\|\$|<|>).){1,}$/,
                message: '请勿输入特殊字符',
              },
            ]}
          >
            <Input.TextArea
              style={{ width: 584 }}
              autoSize={{ minRows: 3, maxRows: 5 }}
              maxLength={201}
            />
          </Form.Item>
          <Form.Item
            name="attachments"
            label="附件"
            wrapperCol={{ span: 16 }}
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <Upload
              accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
              showUploadList
              allowDelete
              maxFileSize={20}
              maxCount={3}
              showAccept
              disabled={(attachments ?? []).length >= 3}
            >
              <Button disabled={(attachments ?? []).length >= 3} icon={<UploadOutlined />}>
                上传
              </Button>
            </Upload>
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Space size="middle">
              <Button
                type="primary"
                loading={submitLoading}
                disabled={uploadLoading}
                onClick={() => {
                  onSubmit();
                }}
              >
                {shouldApprove ? '提交审批' : '提交'}
              </Button>
              <Button
                disabled={submitLoading || uploadLoading}
                onClick={() => {
                  history.goBack();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>
    </Card>
  );
}

export const generateFiledName = (id: number, key: string) => `${id}_${key}`;

const generateValidErrorMessage = (code: number, occurTime: moment.Moment) => {
  switch (code) {
    case 1:
      return `该员工${occurTime.format('YYYY')}年度目标暂未审批通过，请通过后再添加评分`;
    case 2:
      return '所选日期对应考核已考核完成，不支持新增评分';
    case 3:
      return '该周期绩效得分已锁定，不支持改动评分';
    default:
      return '';
  }
};
