import uniq from 'lodash.uniq';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import type { UserShifts } from '@manyun/auth-hub.model.user';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserStateSelect } from '@manyun/auth-hub.ui.user-state';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { Space as SpaceModel } from '@manyun/dc-brain.gql.client';
import { generateBreakOffBalanceRoutePath } from '@manyun/hrm.route.hrm-routes';
import { exportHolidayBalance } from '@manyun/hrm.service.export-holiday-balance';
import type { SvcQuery } from '@manyun/hrm.service.export-holiday-balance';
import { fetchPagedBalance } from '@manyun/hrm.service.fetch-paged-balance';
import type { UserBalance } from '@manyun/hrm.service.fetch-paged-balance';
import { AdjustBalanceModalView } from '@manyun/hrm.ui.adjust-balance-modal-view';
import { UserShiftsSelect, UserShiftsText } from '@manyun/hrm.ui.user-shifts';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { BalanceChangeRecord } from './components/balance-change-record';
import { ImportModalView } from './components/import-modal-view';
import styles from './holiday-balances.module.less';

const DEFAULT_USER_SHIFTS: UserShifts[] = ['SIMPLE_SHIFTS', 'QUARTER_SHIFTS'];

export type HolidayBalanceState = {
  loading: boolean;
  page: number;
  pageSize: number;
  total: number;
  dataSource: UserBalance[];
  onlyOperationsStaff: boolean;
  exportLoading: boolean;
  fields: {
    userName?: { value: string | undefined } | undefined;
    userId?: number;
    blockGuids?: string[];
    userShifts?: UserShifts[];
    userState?: string;
    year?: moment.Moment;
  };
};

export function HolidayBalances() {
  const [form] = Form.useForm();
  const [state, setState] = useState<HolidayBalanceState>({
    loading: false,
    page: 1,
    pageSize: 10,
    total: 0,
    dataSource: [],
    onlyOperationsStaff: true,
    exportLoading: false,
    fields: {
      userShifts: DEFAULT_USER_SHIFTS,
      userState: 'in-service',
      year: moment(),
    },
  });

  const _fetchBalance = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const { error, data } = await fetchPagedBalance({
      page: state.page,
      pageSize: state.pageSize,
      onlyMaintenanceUser: state.onlyOperationsStaff,
      staffId: state.fields.userName?.value,
      userShifts: state.fields.userShifts ?? [...DEFAULT_USER_SHIFTS, 'FLEXIBLE_SHIFTS'],
      blockGuids: state.fields.blockGuids,
      yearDate: state.fields.year?.get('year'),
      userState: state.fields.userState,
    });
    setState(pre => ({ ...pre, loading: false }));

    if (error) {
      message.error(error.message);
      return;
    }
    setState(pre => ({ ...pre, dataSource: data.data, total: data.total }));
  }, [
    state.fields.blockGuids,
    state.fields.userName?.value,
    state.fields.userShifts,
    state.fields.userState,
    state.fields.year,
    state.onlyOperationsStaff,
    state.page,
    state.pageSize,
  ]);

  useEffect(() => {
    _fetchBalance();
  }, [_fetchBalance]);

  const handleFileExport = useCallback(async () => {
    const params: SvcQuery = {
      onlyMaintenanceUser: state.onlyOperationsStaff,
      staffId: state.fields.userName?.value,
      userShifts: state.fields.userShifts ?? [...DEFAULT_USER_SHIFTS, 'FLEXIBLE_SHIFTS'],
      blockGuids: state.fields.blockGuids,
      yearDate: state.fields.year?.get('year'),
      userState: state.fields.userState,
    };

    setState(pre => ({ ...pre, exportLoading: true }));
    const { error, data } = await exportHolidayBalance({ ...params });
    setState(pre => ({ ...pre, exportLoading: false }));
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  }, [
    state.fields.blockGuids,
    state.fields.userName?.value,
    state.fields.userShifts,
    state.fields.userState,
    state.fields.year,
    state.onlyOperationsStaff,
  ]);

  return (
    <Card title="假期余额" bordered={false}>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Space style={{ justifyContent: 'space-between', width: '100%' }} align="center">
          <Space style={{ width: '100%' }} align="baseline" size="middle">
            <ImportModalView
              onSuccess={() => {
                _fetchBalance();
              }}
            />
            <Form
              style={{ width: '100%' }}
              form={form}
              layout="inline"
              colon={false}
              initialValues={{ ...state.fields }}
            >
              <Row gutter={[16, 24]}>
                <Form.Item name="year">
                  <DatePicker
                    style={{ width: 160 }}
                    picker="year"
                    disabledDate={current => current && current.year() > moment().year()}
                    allowClear={false}
                  />
                </Form.Item>
                <Form.Item name="userName">
                  <UserSelect style={{ width: 210 }} allowClear />
                </Form.Item>
                <Form.Item
                  name="location"
                  getValueFromEvent={(value, selectOptions: SpaceModel[] | undefined) => {
                    if (!selectOptions) {
                      return {
                        value,
                      };
                    }

                    let blockGuids: string[] = [];
                    const isIdcSelect =
                      selectOptions.length === 1 &&
                      selectOptions.every(node => node.type === 'IDC');
                    if (isIdcSelect) {
                      blockGuids = (selectOptions[0].children ?? []).map(block => block.value);
                    } else {
                      blockGuids.push(selectOptions[1].value);
                    }
                    return {
                      value,
                      blockGuids: uniq(blockGuids),
                    };
                  }}
                  getValueProps={value => {
                    return { value: value?.value };
                  }}
                >
                  <LocationCascader
                    style={{ width: 210 }}
                    placeholder="位置"
                    authorizedOnly
                    nodeTypes={['IDC', 'BLOCK']}
                    maxTagCount="responsive"
                    allowClear
                  />
                </Form.Item>
                <Form.Item name="userShifts">
                  <UserShiftsSelect
                    style={{ width: 210 }}
                    placeholder="工时类型"
                    mode="multiple"
                    maxTagCount={1}
                    allowClear
                  />
                </Form.Item>
                <Form.Item name="userState">
                  <UserStateSelect style={{ width: 160 }} placeholder="用户状态" allowClear />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      onClick={() => {
                        const searchKeys = form.getFieldsValue();
                        setState(pre => ({
                          ...pre,
                          fields: {
                            ...pre.fields,
                            page: 1,
                            ...searchKeys,
                            blockGuids: searchKeys?.location?.blockGuids,
                            userShifts:
                              Array.isArray(searchKeys.userShifts) &&
                              searchKeys.userShifts.length === 0
                                ? undefined
                                : searchKeys.userShifts,
                          },
                        }));
                      }}
                    >
                      搜索
                    </Button>
                    <Button
                      onClick={() => {
                        setState(pre => ({
                          ...pre,
                          fields: {
                            userShifts: DEFAULT_USER_SHIFTS,
                            userState: 'in-service',
                            year: moment(),
                          },
                        }));
                        form.setFieldsValue({
                          userShifts: DEFAULT_USER_SHIFTS,
                          userState: 'in-service',
                          year: moment(),
                          location: undefined,
                          userName: undefined,
                        });
                      }}
                    >
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Row>
            </Form>
          </Space>
          <FileExport
            text="导出"
            filename={`假期余额明细_${moment().format('YYYY-MM-DD')}.xls`}
            disabled={state.exportLoading}
            data={() => {
              return handleFileExport();
            }}
            showExportFiltered={false}
          />
        </Space>
        <Table
          scroll={{ x: 'max-content' }}
          loading={state.loading}
          rowKey="staffId"
          columns={[
            {
              title: '姓名',
              dataIndex: 'userName',
              fixed: 'left',
              render: (_val, record) => {
                return <User.Link id={record.userId} name={record.userName} />;
              },
            },
            {
              title: '状态',
              dataIndex: 'onJob',
              render: (val, record) =>
                val ? (
                  '在职'
                ) : (
                  <Tooltip
                    title={`离职日期：${
                      record.leaveDate ? moment(record.leaveDate).format('YYYY-MM-DD') : '--'
                    }`}
                  >
                    离职
                  </Tooltip>
                ),
            },
            {
              title: '位置',
              dataIndex: 'idcTag',
            },
            {
              title: '工时类型',
              dataIndex: 'userShifts',
              render: val => <UserShiftsText userShifts={val} />,
            },
            {
              title: '入职日期',
              dataIndex: 'hireDate',
              render: (value, record) => {
                return value ? <Space className={styles.workDate}>{value}</Space> : '--';
              },
            },
            {
              title: '参加工作日期',
              dataIndex: 'workDate',
              render: (value, record) => {
                return value ? <Space className={styles.workDate}>{value}</Space> : '--';
              },
            },
            {
              title: '年份',
              dataIndex: 'year',
              render: val => val ?? '--',
            },
            {
              title: '当年享受年假(天)',
              dataIndex: 'totalBalance',
              render: (val, record) => {
                return (
                  <Tooltip
                    title={
                      <Space direction="vertical">
                        <Typography.Text style={{ color: 'white' }}>当年享受年假</Typography.Text>
                        <Typography.Text style={{ color: 'white' }}>
                          上年结转年假(天)：{record.carryOverBalance}
                        </Typography.Text>
                        <Typography.Text style={{ color: 'white' }}>
                          当年享受法定年假（天）： {record.statutoryBalance}
                        </Typography.Text>
                        <Typography.Text style={{ color: 'white' }}>
                          当年享受公司年假（天）：{record.companyBalance}
                        </Typography.Text>
                      </Space>
                    }
                  >
                    {val ?? '--'}
                  </Tooltip>
                );
              },
            },
            {
              title: '当年已请年假(天)',
              dataIndex: 'totalUsedBalance',
              render: (val, record) => {
                return (
                  <Tooltip
                    title={
                      <Space direction="vertical">
                        <Typography.Text style={{ color: 'white' }}>当年已请年假</Typography.Text>
                        <Typography.Text style={{ color: 'white' }}>
                          当前已请上年结转年假(天)：{record.carryOverUsedBalance}
                        </Typography.Text>
                        <Typography.Text style={{ color: 'white' }}>
                          当年已请法定年假(天)：{record.statutoryUsedBalance}
                        </Typography.Text>
                        <Typography.Text style={{ color: 'white' }}>
                          当年已请公司年假(天)：{record.companyUsedBalance}
                        </Typography.Text>
                      </Space>
                    }
                  >
                    {val ?? '--'}
                  </Tooltip>
                );
              },
            },
            {
              title: '当年可请年假(天)',
              dataIndex: 'totalAvailableBalance',
              render: (val, record) => {
                return (
                  <Space className={styles.balanceWarper}>
                    <Tooltip
                      title={
                        <Space direction="vertical">
                          <Typography.Text style={{ color: 'white' }}>当年可请年假</Typography.Text>
                          <Typography.Text style={{ color: 'white' }}>
                            当年可请上年结转年假(天)：{record.carryOverAvailableBalance}
                          </Typography.Text>
                          <Typography.Text style={{ color: 'white' }}>
                            当年可请法定年假(天)：{record.statutoryAvailableBalance}
                          </Typography.Text>
                          <Typography.Text style={{ color: 'white' }}>
                            当年可请公司年假(天)：{record.companyAvailableBalance}
                          </Typography.Text>
                        </Space>
                      }
                    >
                      {val ?? '--'}
                    </Tooltip>
                    {record.year?.toString() === moment().get('year').toString() && (
                      <AdjustBalanceModalView
                        className={styles.goSetting}
                        userBalance={record}
                        type="ANNUAL_LEAVE"
                        userId={record.userId}
                        currentBalance={val}
                        onSuccess={() => {
                          _fetchBalance();
                        }}
                      />
                    )}
                  </Space>
                );
              },
            },
            {
              title: '当年享受全薪病假(天)',
              dataIndex: 'sickSalaryBalance',
              render: val => val ?? '--',
            },
            {
              title: '当年已请全薪病假(天)',
              dataIndex: 'sickSalaryUsedBalance',
              render: val => val ?? '--',
            },
            {
              title: '当年可请全薪病假(天)',
              dataIndex: 'sickSalaryAvailableBalance',
              render: (val, record) => {
                return (
                  <Space className={styles.balanceWarper}>
                    {val ?? '--'}
                    {record.year?.toString() === moment().get('year').toString() && (
                      <AdjustBalanceModalView
                        className={styles.goSetting}
                        userBalance={record}
                        type="SICK_SALARY"
                        userId={record.userId}
                        currentBalance={val}
                        onSuccess={() => {
                          _fetchBalance();
                        }}
                      />
                    )}
                  </Space>
                );
              },
            },
            {
              title: '调休(小时)',
              dataIndex: 'paidLeaveAvailableBalance',
              render: (val, record) => (
                <Space>
                  <Link
                    target="_blank"
                    to={generateBreakOffBalanceRoutePath({ id: record.userId.toString() })}
                  >
                    {val ?? '--'}
                  </Link>
                  {record.closeExpire && <Tag color="error">快过期</Tag>}
                </Space>
              ),
            },
            {
              title: '操作',
              dataIndex: 'options',
              fixed: 'right',
              render: (_val, record: UserBalance) => (
                <BalanceChangeRecord userId={record.userId} year={record.year} />
              ),
            },
          ]}
          dataSource={state.dataSource}
          pagination={{
            total: state.total,
            current: state.page,
            pageSize: state.pageSize,
          }}
          onChange={pagination => {
            setState(pre => ({
              ...pre,
              page: pagination.current as number,
              pageSize: pagination.pageSize as number,
            }));
          }}
        />
      </Space>
    </Card>
  );
}
