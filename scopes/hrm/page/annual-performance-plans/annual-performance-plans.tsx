import omit from 'lodash.omit';
import moment from 'moment';
import React, { useCallback, useMemo, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { usePaginatedPerformancePlans } from '@manyun/hrm.gql.client.hrm';
import type { PaginatedPerformancePlansQuery } from '@manyun/hrm.gql.client.hrm';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { AnnualPerformancePlanJSON } from '@manyun/hrm.model.annual-performance-plan';
import { generateAnnualPerformancePlanObjectiveDetail } from '@manyun/hrm.route.hrm-routes';

import { AnnualPerformanceObjectivesConfigButton } from './components/annual-performance-objectives-config';
import { AnnualPerformancePlanDetailButton } from './components/annual-performane-plan-detail-drawer';
import { AnnualPerformancePlanMutatorButton } from './components/annula-performance-plan-mutator';
import { FilterFormPopover } from './components/filter-form-popver';
import { RePublicAnnualPerformancePlan } from './components/re-public-annual-performance-plan';
import { WhiteListConfiguration } from './components/white-list-configuration';

type OrderField = 'createdAt' | 'modifiedAt';

export type Fields = Omit<PaginatedPerformancePlansQuery, 'orderBy'> & {
  orderField?: OrderField;
  orderType?: ColumnType['defaultSortOrder'];
};

const defaultPagination = { page: 1, pageSize: 10 };

export function AnnualPerformancePlans() {
  const { search } = useLocation();
  const { page, pageSize, ...restDefaultFields } = getLocationSearchMap<
    Partial<PaginatedPerformancePlansQuery>
  >(search, {
    parseNumbers: true,
    arrayKeys: ['resourceCodes'],
  });
  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);
  const [fields, setFields] = useState<Fields>({
    page: page ?? defaultPagination.page,
    pageSize: pageSize ?? defaultPagination.pageSize,
    ...restDefaultFields,
  });

  const { loading, data, refetch } = usePaginatedPerformancePlans({
    variables: {
      query: transformQuery({
        page: page ?? defaultPagination.page,
        pageSize: pageSize ?? defaultPagination.pageSize,
        ...restDefaultFields,
      }),
    },
  });

  const onSearch = useCallback(
    (params: Fields) => {
      setFields(params);
      setLocationSearch({ ...params });
      refetch({ query: transformQuery(params) });
    },
    [refetch]
  );

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          年度绩效目标
        </Typography.Title>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space size="middle">
            <AnnualPerformancePlanMutatorButton type="primary" />
            <AnnualPerformanceObjectivesConfigButton />
            <WhiteListConfiguration />
            <Input.Search
              style={{ width: 272 }}
              placeholder="搜索考核计划名称"
              onChange={e => {
                const mergedValues = {
                  ...fields,
                  name: e.target.value,
                  ...defaultPagination,
                };
                onSearch(mergedValues);
              }}
            />
          </Space>
          <Space>
            <FilterFormPopover
              values={omit(fields, ['page', 'pageSize', 'name'])}
              onSearch={values => {
                const mergedValues = {
                  ...fields,
                  ...values,
                  ...defaultPagination,
                };
                onSearch(mergedValues);
              }}
            />
          </Space>
        </Space>
        <Table
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          dataSource={data?.paginatedPerformancePlans.data}
          columns={
            [
              {
                title: locales.name,
                dataIndex: 'name',
                fixed: 'left',
                render: (val, record) => (
                  <Link
                    to={generateAnnualPerformancePlanObjectiveDetail({ id: record.id.toString() })}
                  >
                    {val}
                  </Link>
                ),
              },
              {
                title: locales.year,
                dataIndex: 'year',
                render: val => `${val}年度`,
              },
              // {
              //   title: locales.splitRule.__self,
              //   dataIndex: 'splitRule',
              //   render: (_, record) => locales.splitRule.enum[record.splitRule],
              // },
              {
                title: locales.resourcesScope,
                dataIndex: 'resourcesScope',
                width: 334,
                ellipsis: { showTitle: false },
                render: (_, record) => (
                  <Tooltip
                    placement="topLeft"
                    title={record.resourcesScope.map(({ label }) => label).join('｜')}
                  >
                    <Typography.Text style={{ maxWidth: '334px' }} ellipsis>
                      {record.resourcesScope.map(({ label }) => label).join('｜')}
                    </Typography.Text>
                  </Tooltip>
                ),
              },
              {
                title: locales.positionScope,
                dataIndex: 'positionScope',
                ellipsis: { showTitle: false },
                width: 334,
                render: (_, record) => (
                  <Tooltip
                    placement="topLeft"
                    title={record.positionScope.map(({ label }) => label).join('｜')}
                  >
                    <Typography.Text style={{ maxWidth: '334px' }} ellipsis>
                      {record.positionScope.map(({ label }) => label).join('｜')}
                    </Typography.Text>
                  </Tooltip>
                ),
              },
              {
                title: locales.createdAt,
                dataIndex: 'createdAt',
                sorter: true,
                defaultSortOrder: fields.orderField === 'createdAt' ? fields.orderType : undefined,
                render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: locales.createdBy.__self,
                dataIndex: 'createUser',
                render: (_, record) => record.createdBy.name,
              },
              {
                title: locales.modifiedAt,
                dataIndex: 'modifiedAt',
                defaultSortOrder: fields.orderField === 'modifiedAt' ? fields.orderType : undefined,
                sorter: true,
                render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: '操作',
                dataIndex: 'action',
                width: 230,
                fixed: 'right',
                render: (_, record) => {
                  return (
                    <Space>
                      <AnnualPerformancePlanDetailButton
                        record={record}
                        onClose={() => {
                          refetch();
                        }}
                      />
                      {record.canModified && (
                        <>
                          <AnnualPerformancePlanMutatorButton record={record} type="link" compact />
                          <RePublicAnnualPerformancePlan record={record} />
                        </>
                      )}
                    </Space>
                  );
                },
              },
            ] as ColumnType<AnnualPerformancePlanJSON>[]
          }
          pagination={{
            total: data?.paginatedPerformancePlans.total,
            current: fields.page,
            pageSize: fields.pageSize,
          }}
          onChange={({ current, pageSize }, _, sorter, { action }) => {
            if (action === 'paginate') {
              const mergedValues = { ...fields, page: current!, pageSize: pageSize! };
              onSearch(mergedValues);
            } else if (action === 'sort') {
              if (sorter && !Array.isArray(sorter)) {
                const mergedValues = {
                  ...fields,
                  ...defaultPagination,
                  orderField: sorter.field as OrderField,
                  orderType: sorter.order,
                };
                onSearch(mergedValues);
              }
            }
          }}
        />
      </Space>
    </Card>
  );
}

export const transformQuery = (fields: Fields) => {
  const _query = {
    ...fields,
    year: fields.year?.toString(),
    orderBy: fields.orderField ? { [fields.orderField]: fields.orderType ?? undefined } : undefined,
  };

  return omit(_query, ['orderField', 'orderType']) as PaginatedPerformancePlansQuery;
};
