import React, { useContext } from 'react';
import { useHistory } from 'react-router-dom';

import classNames from 'classnames';

import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Container } from '@manyun/base-ui.ui.container';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';


import { HomeContext } from '../../home-context';
import type { RegionCenterList } from '../../home.type';
import styles from './region.module.less';

export type RegionCollapseProps = {
  region: RegionCenterList;
};

const { Panel } = Collapse;

export function RegionCollapse({ region }: RegionCollapseProps) {
  const [{ selectedIdc }, { setSelectedIdc }] = useContext(HomeContext);
  const history = useHistory();

  return (
    <Collapse
      defaultActiveKey={[region.regionName]}
      ghost
      expandIconPosition="end"
      className={styles.regionCollapse}
    >
      <Panel
        key={region.regionName}
        header={
          <Typography.Text className={styles.regionTitle}>
            {`${region.regionName} (${region.regionIdcChilds.length})`}
          </Typography.Text>
        }
      >
        <Row gutter={[10, 10]}>
          {region.regionIdcChilds.length
            ? region.regionIdcChilds.map(idcChild => {
                const title =
                  handleRemoveCitySuffix(
                    idcChild.provinceName === idcChild.cityName
                      ? idcChild.provinceName
                      : `${idcChild.provinceName}${idcChild.cityName}`
                  ) +
                  '-' +
                  idcChild.guid;
                return (
                  <Col
                    key={idcChild.guid}
                    span={12}
                    onClick={() => {
                      if (setSelectedIdc) {
                        setSelectedIdc({ ...idcChild, carouseLocked: true });
                      }
                    }}
                   
                  >
                    <Container
                      style={{
                        borderRadius: 2,
                        height: 80,
                        cursor: 'pointer',
                        padding: '12px 8px',
                      }}
                      className={classNames(
                        styles.collapseCard,
                        selectedIdc?.guid === idcChild.guid && styles.activeCard
                      )}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <div className={styles.containerTitle}>
                          <Typography.Text
                            ellipsis={{ tooltip: title }}
                            className={classNames(idcChild.alarm && styles.idcTitleAlarm)}
                           
                          >
                            {title}
                          </Typography.Text>
                        </div>
                        <div className={styles.containerExtra}>
                          {idcChild.permission && (
                            <Typography.Text
                              style={{
                                cursor: 'pointer',
                                color:
                                  selectedIdc?.guid !== idcChild.guid
                                    ? 'var(--manyun-primary-color)'
                                    : '',
                                fontSize: '12px',
                              }}
                              className={styles.extraDetail}
                             
                            >
                              监控中心
                            </Typography.Text>
                          )}
                        </div>
                      </div>
                      <div className={styles.containerContent}>
                        <Typography.Text
                          className={styles.regionContent}
                          ellipsis={{ tooltip: idcChild.name }}
                        >
                          {idcChild.name}
                        </Typography.Text>
                      </div>
                    </Container>
                  </Col>
                );
              })
            : null}
        </Row>
      </Panel>
    </Collapse>
  );
}

function handleRemoveCitySuffix(address?: string) {
  if (!address) {
    return '';
  }
  return address.replace(/省|市/g, '');
}
