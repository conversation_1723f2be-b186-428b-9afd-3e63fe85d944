import React, { useContext, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import RightOutlined from '@ant-design/icons/es/icons/RightOutlined';
import classNames from 'classnames';
import styled from 'styled-components';

import { datavThemePrefixCls } from '@manyun/base-ui.theme.datav-theme';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import IdcLinePng from '../../assets/idc-line.png';
import { HomeContext } from '../../home-context';
import type { IdcInfo } from '../../home.type';
import styles from './​idc-popover.module.less';

export type IdcPopoverProps = {
  idcs: IdcInfo[];
  children: React.ReactNode;
};

const CustomPopover = styled(Popover)`
  .${datavThemePrefixCls}-popover-inner {
    background: transparent;
    box-shadow: none;
  }

  .${datavThemePrefixCls}-popover-title {
    border-bottom: none;
  }
`;

export function IdcPopover({ idcs = [], children }: IdcPopoverProps) {
  const [{ selectedIdc }] = useContext(HomeContext);
  const [open, setOpen] = useState(false);

  useDeepCompareEffect(() => {
    setOpen(!!(selectedIdc && idcs.map(idc => idc.guid).includes(selectedIdc.guid)));
  }, [selectedIdc, idcs]);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };
  return (
    <CustomPopover
      placement="leftBottom"
      title={<PopContent idcs={idcs} />}
      showArrow={false}
      getPopupContainer={triggerNode => triggerNode}
      open={open}
      trigger="hover"
      onOpenChange={handleOpenChange}
    >
      {children}
    </CustomPopover>
  );
}

function PopContent({ idcs }: { idcs: IdcPopoverProps['idcs'] }) {
  const [activeIdc, setActiveIdc] = useState<string>('');
  const [{ selectedIdc }, { setSelectedIdc }] = useContext(HomeContext);
  const history = useHistory();

  React.useEffect(() => {
    if (selectedIdc?.guid) {
      setActiveIdc(selectedIdc.guid);
    }
  }, [selectedIdc?.guid]);

  return (
    <div className={styles.idcPopContent}>
      <img className={styles.idcLine} alt="IdcLinePng" src={IdcLinePng} />
      <div className={styles.idcContainer}>
        {idcs.map(idc => (
          <div
            key={idc.guid}
            className={classNames(
              activeIdc === idc.guid ? styles.idcSelectedContent : styles.idcUnSelectedContent
            )}
            onClick={() => {
              setActiveIdc(idc.guid);
              setSelectedIdc && setSelectedIdc(idc);
            }}
          >
            <Typography.Text ellipsis={{ tooltip: idc.name }}>{idc.name}</Typography.Text>
            <Tooltip title={`${idc.guid}-监控中心`}>
              <RightOutlined
                style={{ cursor: 'pointer' }}
              />
            </Tooltip>
          </div>
        ))}
      </div>
    </div>
  );
}
