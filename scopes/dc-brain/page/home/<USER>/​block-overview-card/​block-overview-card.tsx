import React, { use<PERSON>allback, useContext, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useDeepCompareEffect } from 'react-use';

import classNames from 'classnames';
import get from 'lodash.get';

import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { ComplexCircleGauge } from '@manyun/monitoring.datav.complex-circle-gauge';
import { EfficiencyStatistic } from '@manyun/monitoring.datav.efficiency-statistic';
import { useCueData } from '@manyun/monitoring.hook.use-cue-data';
import { useWueData } from '@manyun/monitoring.hook.use-wue-data';
import type { BackendRealtimePointData } from '@manyun/monitoring.model.monitoring-data';

import type { SvcQuery as PointValueSvcQuery } from '@manyun/monitoring.service.fetch-point-values';
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values';

import CornerMapPng from '../../assets/corner-map.png';
import { HomeContext } from '../../home-context';
import styles from '../../home.module.less';
import type { BlockInfo, IdcInfo } from '../../home.type';
import { BlockCarousel } from '../​block-carousel';

export function BlockOverviewCard({ idcInfo, loading }: { idcInfo: IdcInfo[]; loading: boolean }) {
  const { json } = React.useContext(ThemeContext);
  const valueTextColor = get(json, ['common', 'valueTextColor']);
  const [{ selectedIdc }] = useContext(HomeContext);
  const selectedIdcBlocks = useMemo(
    () => idcInfo?.find(idc => idc.guid === selectedIdc?.guid)?.blocks || [],
    [idcInfo, selectedIdc?.guid]
  );

  const [carouseBlock, setCarouseBlock] = useState<BlockInfo>();

  useDeepCompareEffect(() => {
    if (selectedIdcBlocks.length) {
      setCarouseBlock(selectedIdcBlocks[0]);
    }
  }, [selectedIdcBlocks]);

  const onBlockChange = (blockId: string) => {
    setCarouseBlock(selectedIdcBlocks.find(block => block.id === blockId));
  };

  return (
    <div style={{ position: 'relative' }}>
      <img className={styles.cornerMqp} alt="CornerMapPng" src={CornerMapPng} />
      <div
        className={classNames(
          styles.cardfixedRightContent,
          !selectedIdcBlocks.length && styles.cardfixedRightContentNoBlock
        )}
      >
        {selectedIdcBlocks.length ? (
          <Card
            style={{
              height: '100%',
            }}
            bodyStyle={{
              overflowY: 'auto',
              height: '90%',
            }}
            title={
              carouseBlock?.idcName && (
                <Typography.Text style={{ color: valueTextColor }}>{`${carouseBlock.idcName}${
                  carouseBlock.id ? '（' + carouseBlock.id + '楼）' : ''
                }`}</Typography.Text>
              )
            }
            loading={loading}
           
          >
            {/* 图片轮播 */}
            {carouseBlock && !!selectedIdcBlocks.length && (
              <BlockCarousel
                blocks={selectedIdcBlocks}
                carouseBlock={carouseBlock}
                onBlockChange={onBlockChange}
              />
            )}
            {carouseBlock?.permission && carouseBlock.guid && (
              <QuotaOverview block={carouseBlock} />
            )}
          </Card>
        ) : (
          <Card
            style={{
              height: '65%',
            }}
            bodyStyle={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '90%',
            }}
            title={
              selectedIdc?.name && (
                <Typography.Text style={{ color: valueTextColor }}>
                  {selectedIdc.name}
                </Typography.Text>
              )
            }
          >
            <Empty />
          </Card>
        )}
      </div>
    </div>
  );
}

// 指标概览
function QuotaOverview({ block }: { block: BlockInfo }) {
  const { id, idcGuid, guid: blockGuid } = block;
  const { blockWueList } = useWueData(idcGuid, [id]);
  const { blockCueList } = useCueData(idcGuid, [id]);

  const { json } = React.useContext(ThemeContext);
  const textColor = get(json, ['legend', 'pageTextStyle', 'color']);
  const colorList = get(json, ['color']) || [];
  const labelTextColor = get(json, ['common', 'labelTextColor']);
  const valueTextColor = get(json, ['common', 'labelTextColor']);

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => new ConfigUtil(config), [config]);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const [pointsData, setPointsData] = useState<BackendRealtimePointData>({});

  /** 市电总功率 */
  const electricPowerPointCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(
            blockDeviceType,
            ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_POWER_SUM
          )
        : undefined,
    [blockDeviceType, configUtil]
  );

  /** IT总功率 */
  const itPowerPointCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(
            blockDeviceType,
            ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM
          )
        : undefined,
    [blockDeviceType, configUtil]
  );

  /** 市电使用率 */
  const electricPowerPointRateCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(
            blockDeviceType,
            ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_LOAD_RATE_AVG
          )
        : undefined,
    [blockDeviceType, configUtil]
  );

  /** IT使用率 */
  const itPowerPointRateCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(blockDeviceType, ConfigUtil.constants.pointCodes.IT_LOAD_RATE_MAX)
        : undefined,
    [blockDeviceType, configUtil]
  );

  const getPointValue = useCallback(async () => {
    if (
      idcGuid &&
      blockGuid &&
      electricPowerPointCode &&
      itPowerPointCode &&
      electricPowerPointRateCode &&
      itPowerPointRateCode
    ) {
      const params: PointValueSvcQuery = {
        idc: idcGuid,
        pointGuids: {
          [blockGuid]: `${electricPowerPointCode},${itPowerPointCode},${electricPowerPointRateCode},${itPowerPointRateCode}`,
        },
      };
      const { error, data } = await fetchPointValues(params);
      if (error) {
        console.error(`QuotaOverview-fetchPointValues-error:${error.message}`);
        return;
      }
      setPointsData(data);
    }
  }, [
    idcGuid,
    electricPowerPointCode,
    itPowerPointCode,
    electricPowerPointRateCode,
    itPowerPointRateCode,
    blockGuid,
  ]);

  useEffect(() => {
    getPointValue();
    const currentInterval = window.setInterval(getPointValue, 15 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [getPointValue]);

  return (
    <div>
      <div style={{ margin: '20px 0' }}>
        <Typography.Text style={{ color: valueTextColor, fontSize: 16, fontWeight: 500 }}>
          能效指标
        </Typography.Text>
      </div>
      <Row gutter={[40, 40]}>
        <Col span={12}>
          {blockGuid && (
            <ComplexCircleGauge
              subscribeTarget={{
                spaceGuid: blockGuid,
                moduleId: 'QuotaOverview',
                predefinedCode: ConfigUtil.constants.pointCodes.PUE,
                subscribeMethod: 'fetch',
              }}
              title="PUE"
              precision={3}
              chartSize={window.innerWidth < 1600 ? 152 : 200}
              max={2}
              btnStyle={{
                position: 'absolute',
                top: '48%',
                fontSize: '24px',
              }}
            />
          )}
        </Col>
        <Col span={8} style={{ marginLeft: 55, paddingRight: 8 }}>
          <EfficiencyStatistic
            type="WUE"
            value={
              blockWueList?.length && blockWueList[0].value?.length
                ? blockWueList[0].value[0]?.wue!
                : '--'
            }
            title="水利用效率"
            unit="L/kWh"
            textColor={textColor}
            labelTextColor={labelTextColor}
            spaceGuid={blockGuid}
          />
          <EfficiencyStatistic
            style={{ marginTop: 32 }}
            type="CUE"
            value={
              blockCueList?.length && blockCueList[0].value?.length
                ? blockCueList[0].value[0]?.cue!
                : '--'
            }
            title="碳利用效率"
            unit="kg/kWh"
            textColor={textColor}
            labelTextColor={labelTextColor}
            spaceGuid={blockGuid}
          />
        </Col>
      </Row>
    </div>
  );
}
