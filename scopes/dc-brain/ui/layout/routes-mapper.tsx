import {
  AppstoreOutlined,
  AuditOutlined,
  BellOutlined,
  BookOutlined,
  GlobalOutlined,
  Loading3QuartersOutlined,
  ShopOutlined,
} from '@ant-design/icons';
import React from 'react';

import {
  AUDIT_LOG_LIST_ROUTE_PATH,
  CUSTOMER_WHITE_LIST_ROUTE_PATH,
  ROLES_ROUTE_PATH,
  USERS_ROUTE_PATH,
  USER_GROUPS_ROUTE_PATH,
  USER_PERMISSIONS_ROUTE_PATH,
  USER_ROLE_AUTHORIZATION_ROUTE_PATH,
} from '@manyun/auth-hub.route.auth-routes';
import { BillSettingsOutlined } from '@manyun/base-ui-web.icon.outlined.bill-settings';
import { CustomerOutlined } from '@manyun/base-ui-web.icon.outlined.customer';
import { FileCheckCircleOutlined } from '@manyun/base-ui-web.icon.outlined.file-check-circle';
import { KeyOutlined } from '@manyun/base-ui-web.icon.outlined.key';
import { LayersOutlined } from '@manyun/base-ui-web.icon.outlined.layers';
import { LineChartOutlined } from '@manyun/base-ui-web.icon.outlined.line-chart';
import { MonitorOutlined } from '@manyun/base-ui-web.icon.outlined.monitor';
import { PaySquareOutlined } from '@manyun/base-ui-web.icon.outlined.pay-square';
import { ShieldSettingsOutlined } from '@manyun/base-ui-web.icon.outlined.shield-settings';
import { StepsCircleOutlined } from '@manyun/base-ui-web.icon.outlined.steps-circle';
import { UserSettingsOutlined } from '@manyun/base-ui-web.icon.outlined.user-settings';
import {
  //BPMS_ROUTE_PATH,
  BPM_INSTANCES_ROUTE_PATH,
  BPM_RELATIONSHIPS_ROUTE_PATH,
  REQUEST_RNTRY_ROUTE_PATH,
} from '@manyun/bpm.route.bpm-routes';
import {
  BILLS_ROUTE_PATH,
  CUSTOMERS_ROUTE_PATH,
  CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH,
  MODELS_ROUTE_PATH,
  SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH,
  VENDORS_ROUTE_PATH,
} from '@manyun/crm.route.crm-routes';
import {
  ALARM_TRANSMISSION_RECORD_LIST,
  CHANNEL_CONFIG_LIST,
  EXCEL_STATEMENT_LIST_ROUTE_PATH,
  NORTH_BOUND_MANAGE,
  NORTH_USER_LIST,
  SERVER_NODE_LIST,
  VERSION_MANAGE_DEATAIL,
} from '@manyun/dc-brain.route.admin-routes';
import {
  ALTER_INFO,
  ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH,
  ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH,
  ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH,
  ATT_GROUP_MANAGEMENT_LIST,
  CALENDAR_CONFIGURE_ROUTE_PATH,
  DUTY_GROUP_MANAGEMENT_LIST,
  GROUP_SCHEDULE_MANAGEMENT_LIST,
  HOLIDAY_BALANCES_ROUTE_PATH,
  HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH,
  HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH,
  HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH,
  OT_REQUESTS_ROUTE_PATH,
  PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH,
  PP_PERFORMANCES_ROUTE_PATH,
  PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH,
  SCHEDULE_STAFF_STATISTIC_ROUTE_PATH,
  SHIFTS_ROUTE_PATH,
  SHIFT_SYS_MANAGEMENT_LIST,
  SUPPLY_CHECKS_ROUTE_PATH,
  USER_ANNUAL_PERFORMANCES_ROUTE_PATH,
  generateAttendanceStatisticsLocation,
  generateTeamPerformancesLocation,
} from '@manyun/hrm.route.hrm-routes';
import {
  COURSES_ROUTE_PATH,
  COURSEWARES_ROUTE_PATH,
  EXAMS_ROUTE_PATH,
  MY_COURSES_ROUTE_PATH,
  MY_EXAMS_PAGE_URL,
  PAPERS_ROUTE_PATH,
  PENDING_MARK_EXAMS_ROUTE_PATH,
  QUESTIONS_ROUTE_PATH,
  SKILLS_PAGE_URL,
  TRAIN_PLANS_ROUTE_PATH,
  WIKIS_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import {
  AI_OPS_ALARM_ROUTE_PATH,
  AI_OPS_MONITORING_STATISTICS_ROUTE_PATH,
  ALARM_CONFIGURATION_TEMPLATE_LIST,
  ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
  ALARM_SHIELD_LIST_ROUTE_PATH,
  AREA_CONNECT_TEMPLATE,
  BATTERY_PACK,
  CUSTOM_POINT_ROUTE_PATH,
  DYNAMIC_BASELINE_SETTING_ROUTE_PATH,
  LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
  LOCAL_ALARM_RULE_PATH,
  MERGED_MONITOR_CONFIG_LIST,
  MERGES_PROCESSED_POINT_DETAIL,
  NON_POINT_ROUTE_PATH,
  PROFESSIONAL_RULE_LIST_ROUTE_PATH,
  PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH,
  TEMPLATE_GROUP_VIEW_LIST,
} from '@manyun/monitoring.route.monitoring-routes';
import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import {
  ALARM_NOTIFICATIONS_ROUTE_PATH,
  NOTICE_MANAGE_LIST,
  NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH,
  WEBHOOK_CONFIG_ROUTE_PATH,
  generateOnsiteMessageUrl,
} from '@manyun/notification-hub.route.notification-routes';
import {
  BASIC_RESOURCES_BUILDING,
  BASIC_RESOURCES_FLOOR_ROUTE_PATH,
  BASIC_RESOURCES_IDC,
  BORROWS_AND_RETURN_LIST,
  CABINET_LIST,
  DEVICE_TYPE_LIST,
  DOPOINT_LIST,
  EQUIPMENT_LIST,
  LOG_LIST,
  METADATA_CONFIGURATION,
  ROOM_LIST,
  SPARE_LIST,
  SPEC_LIST,
} from '@manyun/resource-hub.route.resource-routes';
import {
  ENTRANCE_GUARD_CARD_MANAGEMENT_ROUTE_PATH,
  VISITS_RECORDS_ROUTE_PATH,
  VISITS_STATISTIC_ROUTE_PATH,
} from '@manyun/sentry.route.routes';
import {
  ARRIVAL_ASSET_LIST,
  CHANGE_OFFLIENS_ROUTE_PATH,
  CHANGE_ONLINE_LIST_ROUTE_PATH,
  CHANGE_SHIFTS,
  CHANGE_TEMPLATE_LIST_ROUTE_PATH,
  DRILL_CONFIG_LIST_ROUTE_PATH,
  DRILL_LIST_ROUTE_PATH,
  DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  EMERGENCY_PROCEE_LIST_ROUTE_PATH,
  EVENTS_ROUTE_PATH,
  EVENT_LIST_ROUTE_PATH,
  INSPECTION_CONFIG_LIST,
  INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  INVENTORY_ANALYTICS_LIST,
  INVENTORY_CONFIG_LIST,
  INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  MAINTAIN_CONFIG_LIST,
  MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  MINE_TICKET,
  OPERATION_CHECK_CONFIG_LIST,
  RISK_CHECK_TASK_LIST_ROUTE_PATH,
  RISK_POOL_ROUTE_PATH,
  RISK_REGISTERS,
  STANDARD_CHANGE_LIBRARIES_ROUTE_PATH,
  TASK_CENTER_LIST,
  TASK_CENTER_SCHEDULE,
  VISITOR_BLACKLIST,
  WARRANTY_MANAGE_LIST,
  WARRANTY_POOL_LIST,
  generateTicketsRoutePath,
} from '@manyun/ticket.route.ticket-routes';

export const ANY_REPORTS_MENU_CODE_PREFIX = 'menu_reports--';
export const ANY_REPORTS_MENU_CODE = ANY_REPORTS_MENU_CODE_PREFIX + '*';

export type RouteConfig = {
  /** only menu level 1 can have `icon` */
  icon?: React.ReactElement;
  /** only menu level 3 can have `link` */
  link?: string | ((...args: any[]) => string);
};

export const routesMapper: Record<string, RouteConfig> = {
  /** 客户中心 */
  menu_crm: {
    icon: <CustomerOutlined />,
  },
  lord: {
    icon: <KeyOutlined />,
  },
  'operation-center': {
    icon: <Loading3QuartersOutlined />,
  },
  configuration_center: {
    icon: <LayersOutlined />,
  },
  task_center: {
    icon: <StepsCircleOutlined />,
  },
  'security-center': {
    icon: <ShieldSettingsOutlined />,
  },
  contract_center: {
    icon: <BillSettingsOutlined />,
  },
  sys_manager: {
    icon: <LayersOutlined />,
  },
  'menu_sys-config': {
    icon: <ShopOutlined />,
  },
  personnel_management: {
    icon: <UserSettingsOutlined />,
  },
  resource_center: {
    icon: <AppstoreOutlined />,
  },
  'notification-center': {
    icon: <BellOutlined />,
  },
  'menu_supplier-center': {
    icon: <ShopOutlined />,
  },
  'ticket-center': {
    icon: <FileCheckCircleOutlined />,
  },
  service_center: {
    icon: <GlobalOutlined />,
  },
  approve_center: {
    icon: <AuditOutlined />,
  },
  'menu_cost-center': {
    icon: <BillSettingsOutlined />,
  },
  'menu_finance-cost-center': {
    icon: <PaySquareOutlined />,
  },
  /** 报表中心 */
  'menu_reports-hub': {
    icon: <LineChartOutlined />,
  },
  /** 知识中心 */
  menu_knowledge_center: {
    icon: <BookOutlined />,
  },
  //#endregion menu level 1

  menu_roles: {
    link: ROLES_ROUTE_PATH,
  },
  'menu_user-groups': {
    link: USER_GROUPS_ROUTE_PATH,
  },
  menu_users: {
    link: USERS_ROUTE_PATH,
  },
  menu_permissions: {
    link: USER_PERMISSIONS_ROUTE_PATH,
  },
  basic_resource_idc_management: {
    link: BASIC_RESOURCES_IDC,
  },
  basic_resource_building_management: {
    link: BASIC_RESOURCES_BUILDING,
  },
  basic_resource_room_management: {
    link: ROOM_LIST,
  },
  basic_resource_cabinet_management: {
    link: CABINET_LIST,
  },
  basic_resource_equipment_management: {
    link: EQUIPMENT_LIST,
  },
  infrastructure_point: {
    link: DOPOINT_LIST,
  },
  mergerd_processed_point: {
    link: MERGES_PROCESSED_POINT_DETAIL,
  },
  spec_management: {
    link: SPEC_LIST,
  },
  'change_template--list': {
    link: '/page/change/template/list',
  },
  'change_ticket--list': {
    link: '/page/change/ticket/list',
  },
  menu_events: {
    link: EVENTS_ROUTE_PATH,
  },
  metadata_configuration: {
    link: METADATA_CONFIGURATION,
  },

  inspection_configuration: {
    link: INSPECTION_CONFIG_LIST,
  },
  inventory_configuration: {
    link: INVENTORY_CONFIG_LIST,
  },
  operation_check_configuration: {
    link: OPERATION_CHECK_CONFIG_LIST,
  },
  maintain_configuration: {
    link: MAINTAIN_CONFIG_LIST,
  },
  alarm_template_configuration: {
    link: ALARM_CONFIGURATION_TEMPLATE_LIST,
  },
  alarm_template_group_configuration: {
    link: TEMPLATE_GROUP_VIEW_LIST,
  },
  convergence_configuration: {
    link: MERGED_MONITOR_CONFIG_LIST,
  },
  device_type_management: {
    link: DEVICE_TYPE_LIST,
  },
  model_management: {
    link: MODELS_ROUTE_PATH,
  },
  shift_management: {
    link: SHIFTS_ROUTE_PATH,
  },
  shift_sys_management: {
    link: SHIFT_SYS_MANAGEMENT_LIST,
  },
  duty_group_management: {
    link: DUTY_GROUP_MANAGEMENT_LIST,
  },
  group_schedule_management: {
    link: GROUP_SCHEDULE_MANAGEMENT_LIST,
  },
  att_group_management: {
    link: ATT_GROUP_MANAGEMENT_LIST,
  },
  att_statistics_byMonth: {
    link: generateAttendanceStatisticsLocation({ byFunc: 'byMonth' }),
  },
  att_statistics_byDay: {
    link: generateAttendanceStatisticsLocation({ byFunc: 'byDay' }),
  },
  record_management: {
    link: PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH,
  },
  notice: {
    link: NOTICE_MANAGE_LIST,
  },
  'menu_operational-log': {
    link: LOG_LIST,
  },
  /** 审计日志 */
  'menu_audit-log-list': {
    link: AUDIT_LOG_LIST_ROUTE_PATH,
  },
  'menu_customer-white-list': {
    link: CUSTOMER_WHITE_LIST_ROUTE_PATH,
  },
  vendor_management: {
    link: VENDORS_ROUTE_PATH,
  },
  'ticket-center_chores--it_service': {
    link: generateTicketsRoutePath({ type: 'it_service' }),
  },
  'ticket-center_chores--risk_register': {
    link: generateTicketsRoutePath({ type: 'risk_register' }),
  },
  'ticket-center_chores--risk_check': {
    link: generateTicketsRoutePath({ type: 'risk_check' }),
  },
  'ticket-center_chores--repair': {
    link: generateTicketsRoutePath({ type: 'repair' }),
  },
  'ticket-center_chores--inspection': {
    link: generateTicketsRoutePath({ type: 'inspection' }),
  },
  'ticket-center_chores--visitor': {
    link: generateTicketsRoutePath({ type: 'visitor' }),
  },
  'ticket-center_assets--warehouse': {
    link: generateTicketsRoutePath({ type: 'warehouse' }),
  },
  'ticket-center_assets--inventory': {
    link: generateTicketsRoutePath({ type: 'inventory' }),
  },
  'ticket-center_assets--inventory_analysis': {
    link: INVENTORY_ANALYTICS_LIST,
  },
  'ticket-center_devices--power': {
    link: generateTicketsRoutePath({ type: 'power' }),
  },
  'ticket-center_devices--on_off': {
    link: generateTicketsRoutePath({ type: 'on_off' }),
  },
  'ticket-center_devices--maintenance': {
    link: generateTicketsRoutePath({ type: 'maintenance' }),
  },
  'ticket-center_currency--access_card_auth': {
    link: generateTicketsRoutePath({ type: 'access_card_auth' }),
  },
  'ticket-center_chores--emergency_drill': {
    link: generateTicketsRoutePath({ type: 'emergency_drill' }),
  },
  task_list: {
    link: TASK_CENTER_LIST,
  },
  task_schedule: {
    link: TASK_CENTER_SCHEDULE,
  },
  page_customer_service: {
    link: CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH,
  },

  'page_srm_service-entries': {
    link: SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH,
  },
  'ticket-center_assets--access': {
    link: generateTicketsRoutePath({ type: 'access' }),
  },
  'schedule-alter': {
    link: ALTER_INFO,
  },
  'supply-check': {
    link: SUPPLY_CHECKS_ROUTE_PATH,
  },
  version_manage: {
    link: `${VERSION_MANAGE_DEATAIL}?idc=`,
  },
  mine_ticket: {
    link: MINE_TICKET,
  },
  area_connect_template: {
    link: AREA_CONNECT_TEMPLATE,
  },
  'security-center_chores--visitor_record': {
    link: VISITS_RECORDS_ROUTE_PATH,
  },
  'security-center_chores--visitor_blacklist': {
    link: VISITOR_BLACKLIST,
  },
  approve_center_mine_process_list: {
    link: BPM_INSTANCES_ROUTE_PATH,
  },
  spare_management: {
    link: SPARE_LIST,
  },
  // process_config: {
  //   link: BPMS_ROUTE_PATH,
  // },
  biz_scenes: {
    link: BPM_RELATIONSHIPS_ROUTE_PATH,
  },
  battery_unit_view: {
    link: BATTERY_PACK,
  },
  server_node_list: {
    link: SERVER_NODE_LIST,
  },
  north_user_list: {
    link: NORTH_USER_LIST,
  },
  channel_config_list: {
    link: CHANNEL_CONFIG_LIST,
  },
  page_inside_notice_list: {
    link: generateOnsiteMessageUrl({
      state: OnSiteMessageState.Unread,
      type: OnSiteMessageType.All,
    }),
  },
  'page_warranty-order': {
    link: WARRANTY_MANAGE_LIST,
  },
  'page_warranty-pool': {
    link: WARRANTY_POOL_LIST,
  },
  page_borrow_return: {
    link: BORROWS_AND_RETURN_LIST,
  },

  'ticket-center_devices--device_general': {
    link: generateTicketsRoutePath({ type: 'device_general' }),
  },
  'ticket-center_assets--accept': {
    link: generateTicketsRoutePath({ type: 'accept' }),
  },
  page_arrival_asset: {
    link: ARRIVAL_ASSET_LIST,
  },
  page_alarm_transmission_record: {
    link: ALARM_TRANSMISSION_RECORD_LIST,
  },
  /**题库管理 */
  'page_knowledge_hub-questions': {
    link: QUESTIONS_ROUTE_PATH,
  },
  /**试卷管理 */
  'page_knowledge_hub-papers': {
    link: PAPERS_ROUTE_PATH,
  },
  /**考试管理 */
  'page_knowledge_hub-exams': {
    link: EXAMS_ROUTE_PATH,
  },
  /**可判卷列表 */
  'page_knowledge_hub-pending-mark-exams': {
    link: PENDING_MARK_EXAMS_ROUTE_PATH,
  },
  /**我的考试 */
  'page_knowledge_hub-my-exams': {
    link: MY_EXAMS_PAGE_URL,
  },
  /**课程列表 */
  'page_knowledge_hub-courses': {
    link: COURSES_ROUTE_PATH,
  },
  /**培训计划管理 */
  'menu_knowledge_hub-train-plan-manage': {
    link: TRAIN_PLANS_ROUTE_PATH,
  },
  /**我的课程 */
  'page_knowledge_hub-my-courses': {
    link: MY_COURSES_ROUTE_PATH,
  },
  /**课件列表 */
  'page_knowledge_hub-coursewares': {
    link: COURSEWARES_ROUTE_PATH,
  },
  /**知识库 */
  'page_knowledge_hub-wikis': {
    link: WIKIS_ROUTE_PATH,
  },
  /** 技能列表 */
  'page_knowledge_hub-skills': {
    link: SKILLS_PAGE_URL,
  },
  [ANY_REPORTS_MENU_CODE]: {
    // TODO @Jerry 使用 `redash dashboard page route`
    link: ({ dashboardId }) => '/page/reports/dashboard/:id'.replace(':id', dashboardId),
  },
  /** 客户列表 */
  page_crm_customers: {
    link: CUSTOMERS_ROUTE_PATH,
  },
  /** 客户用量确认 */
  'menu_customer-confirm': {
    link: BILLS_ROUTE_PATH,
  },
  /** 消息接收配置 */
  'page_notification-hub_notification-channels-config': {
    link: NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH,
  },
  /**通用审批 */
  'page_bpm-requests-entry': {
    link: REQUEST_RNTRY_ROUTE_PATH,
  },
  /**库存报表 */

  'menu_independent-reports--stock': {
    link: '/page/reports/stock',
  },

  /**加班记录 */
  'menu-ot_requests': {
    link: OT_REQUESTS_ROUTE_PATH,
  },
  /** 线下文档变更 */
  'menu_change-shifts': {
    link: CHANGE_SHIFTS,
  },
  /** 维护任务 */
  'menu_maintenance-task': {
    link: MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  },

  /** 巡检任务 */
  'menu_inspect-task': {
    link: INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  },

  /** 盘点任务 */
  'menu_inventory-task': {
    link: INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  },
  /** 演练任务 */
  'menu_drill-task': {
    link: DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  },

  /**假期余额 */
  'menu_holiday-balances': {
    link: HOLIDAY_BALANCES_ROUTE_PATH,
  },
  'menu_alarm-notice-configuration-list': {
    link: ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
  },
  'menu_alarm-notification-records': {
    link: ALARM_NOTIFICATIONS_ROUTE_PATH,
  },
  'menu_notification-webhook-config': {
    link: WEBHOOK_CONFIG_ROUTE_PATH,
  },
  /** 北向管理 */
  menu_north: {
    link: NORTH_BOUND_MANAGE,
  },
  /** 专家规则列表 */
  'menu_professional-rule-list': {
    link: PROFESSIONAL_RULE_LIST_ROUTE_PATH,
  },
  /** 专家规则模版列表 */
  'menu_professional-rule-template-list': {
    link: PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH,
  },
  /**AI-OPS监测结果 */
  menu_monitoring_ai_ops_alarms: {
    link: AI_OPS_ALARM_ROUTE_PATH,
  },
  /**AI-OPS监测状态 */
  menu_monitoring_ai_ops_statistics: {
    link: AI_OPS_MONITORING_STATISTICS_ROUTE_PATH,
  },
  /** ai-ops 动态基线配置 */
  'menu_dynamic-baseline-setting': {
    link: DYNAMIC_BASELINE_SETTING_ROUTE_PATH,
  },
  'menu_monitoring_local-alarm-rule': {
    link: `${LOCAL_ALARM_RULE_PATH}?idc=`,
  },
  /** 属地监控配置-设备测点配置 */
  'menu_monitoring_non-point': {
    link: `${NON_POINT_ROUTE_PATH}?idc=`,
  },
  'menu_sentry-entrance-guard-card-management': {
    link: ENTRANCE_GUARD_CARD_MANAGEMENT_ROUTE_PATH,
  },
  /**团队试用期绩效 */
  'menu_hrm-pp-team-performances': {
    link: generateTeamPerformancesLocation({ type: 'test' }),
  },
  /**试用期个人绩效 */
  'menu_hrm-pp-user-performances': {
    link: PP_PERFORMANCES_ROUTE_PATH,
  },
  /**年度指标 */
  'menu_hrm-annual-performance-objectives': {
    link: ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH,
  },
  /** 楼层管理*/
  'menu_resource_floor-management': {
    link: BASIC_RESOURCES_FLOOR_ROUTE_PATH,
  },
  /**日常评分 */
  'menu_hrm-daily-performance-grade': {
    link: PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH,
  },
  /**年度计划 */
  'menu_hrm-annual-performance-plans': {
    link: ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH,
  },
  /**个人年度绩效 */
  'menu_hrm-user-annual-performances': {
    link: USER_ANNUAL_PERFORMANCES_ROUTE_PATH,
  },
  /**团队年度绩效*/
  'menu_hrm-team-annual-performances': {
    link: generateTeamPerformancesLocation({ type: 'annual' }),
  },
  /**年度绩效报表 */
  'menu_hrm-annual-performance-statistics': {
    link: ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH,
  },
  /**风险登记册 */
  'menu_ticket-risk-register': {
    link: RISK_REGISTERS,
  },

  'menu_user-role-authorization': {
    link: USER_ROLE_AUTHORIZATION_ROUTE_PATH,
  },
  'menu_risk-check-task': {
    link: RISK_CHECK_TASK_LIST_ROUTE_PATH,
  },
  /**风险库 */
  'menu_risk-pool': {
    link: RISK_POOL_ROUTE_PATH,
  },
  /**属地告警通报 */
  'menu_local-alarm-notice-configuration': {
    link: LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
  },
  /**演练列表 */
  'menu_drill-list': {
    link: DRILL_LIST_ROUTE_PATH,
  },

  /**演练配置 */
  'menu_ticket-drill-configuration': {
    link: DRILL_CONFIG_LIST_ROUTE_PATH,
  },
  /**告警屏蔽 */
  'menu_alarm-shield': {
    link: ALARM_SHIELD_LIST_ROUTE_PATH,
  },
  /**假期额度测算工具 */
  'menu_hrm-cal-holiday-balance': {
    link: HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH,
  },
  /**Excel 报表 */
  'menu_excel-statement': {
    link: EXCEL_STATEMENT_LIST_ROUTE_PATH,
  },
  'menu_hrm-scheduler-staffs': {
    link: SCHEDULE_STAFF_STATISTIC_ROUTE_PATH,
  },
  /**日历配置 */
  'menu_calendar-configure': {
    link: CALENDAR_CONFIGURE_ROUTE_PATH,
  },
  /**人员出入室统计 */
  'menu_personnel-entry-out-statistics': {
    link: VISITS_STATISTIC_ROUTE_PATH,
  },
  /**标准变更库 */
  'menu_standard-change-library': {
    link: STANDARD_CHANGE_LIBRARIES_ROUTE_PATH,
  },
  /** 线下变更（内测版） */
  'menu_change-offline': {
    link: CHANGE_OFFLIENS_ROUTE_PATH,
  },
  /** 事件（内测版） */
  'menu_event-management': {
    link: EVENT_LIST_ROUTE_PATH,
  },
  /** 应急管理 */
  'menu_emergency-process': {
    link: EMERGENCY_PROCEE_LIST_ROUTE_PATH,
  },
  /**楼栋评优 */
  'menu_hrm-performance-block-evaluation': {
    link: HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH,
  },
  /** 红线指标管理 */
  'menu_hrm-red-line-records': {
    link: HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH,
  },
  /** 自定义测点 */
  'menu_monitoring_custom-points': {
    link: CUSTOM_POINT_ROUTE_PATH,
  },
  /** 简版线上变更模板 */
  'menu_ticket-change-template': {
    link: CHANGE_TEMPLATE_LIST_ROUTE_PATH,
  },
  /** 简版线上变更 */
  'menu_ticket-change-management': {
    link: CHANGE_ONLINE_LIST_ROUTE_PATH,
  },
  //#endregion menu level 3
};
