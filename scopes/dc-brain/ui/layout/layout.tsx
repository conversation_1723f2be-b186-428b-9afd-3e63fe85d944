/* eslint-disable @typescript-eslint/no-explicit-any */
import { url } from '@teammc/core';
import BaseLayout from 'antd/es/layout';
import classNames from 'classnames';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';

import { selectMe, selectMyMenus, userSliceActions } from '@manyun/auth-hub.state.user';
import { message } from '@manyun/base-ui.ui.message';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { Watermark } from '@manyun/dc-brain.ui.watermark';
import type { PermissionJSON } from '@manyun/iam.model.permission';
import { fetchPermissionsByRoleId } from '@manyun/iam.service.fetch-permissions-by-role-id';
import { AnnouncementRemindNotificationList } from '@manyun/notification-hub.ui.announcement-remind-notification';
import { useLazySpaces } from '@manyun/resource-hub.gql.client.resources';
import { useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import type { Space } from '@manyun/resource-hub.service.fetch-spaces';
import { UserProfilePromptModalView} from '@manyun/iam.ui.user-profile-prompt-modal-view'

import { Header } from './header';
import type { MenuTreeNode } from './header';
import { UpdateMyPasswordModal } from './header/update-my-password-modal';
import styles from './layout.module.less';
import { layoutRegistry } from './registry/layout-registry';
import { routesMapper } from './routes-mapper';
import { SideBar } from './sidebar';
import './styles/size.variables.less';

function replaceIdcParam(pathname: string, replacementValue: string) {
  const hasHost = pathname.includes('http');
  const urlObject = new URL(hasHost ? pathname : url.join('http://example.com', pathname));
  if (urlObject.searchParams.has('idc')) {
    urlObject.searchParams.delete('idc');
    urlObject.searchParams.set('idc', replacementValue);
    if (hasHost) {
      return urlObject.toString();
    }
    return `${urlObject.pathname}${urlObject.search}`;
  }
  return pathname;
}
export type LayoutProps = {
  style?: React.CSSProperties;
  className?: string;
  children: React.ReactNode;
  /** start @REFACTORME @Jerry 迁移这部分代码到此组件内部 */
  homeUrl: string;
  /** end */
};

export function Layout({ style, className, children, homeUrl }: LayoutProps) {
  const [mySpaces, setMySpaces] = useState<Space[] | undefined | null>([]);
  useSpaces({
    fetchPolicy: 'cache-and-network',
    variables: {
      nodeTypes: ['IDC', 'BLOCK', 'ROOM'],
      includeVirtualBlocks: true,
      authorizedOnly: false,
    },
  });
  const [getSpaces] = useLazySpaces({
    variables: {
      nodeTypes: ['IDC'],
      includeVirtualBlocks: false,
      authorizedOnly: true,
    },
  });

  const { selectedMenuCodes } = useLayout();
  const [showMenuLevel2s, setShowMenuLevel2s] = React.useState(false);
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const dispatch = useDispatch();
  const { pathname } = useLocation<undefined>();
  const roleCode = localStorage.getItem('roleCode');
  React.useEffect(() => {
    fetchPermissionsByRoleId({
      webSite: 'DCBASE',
      roleCode,
      permissionTypes: ['MENU', 'PAGE'],
    }).then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        setShowMenuLevel2s(true);
        return;
      }
      dispatch(userSliceActions.setMenus(data));

      setShowMenuLevel2s(true);
    });

    return () => {
      setShowMenuLevel2s(false);
    };
  }, [dispatch, pathname, roleCode]);

  React.useEffect(() => {
    if (!idc) {
      (async () => {
        const { data } = await getSpaces();
        const _idc = data?.spaces?.at(0)?.value;
        if (_idc) {
          setMySpaces(data?.spaces as unknown as Space[]);
          dispatch(userSliceActions.setUserIdc(_idc));
        }
      })();
    }
  }, [dispatch, getSpaces, idc]);
  const { entities, codes } = useSelector(selectMyMenus);
  const menuList = React.useMemo(() => {
    const permissionJSONs = codes.map(code => entities[code]);
    const menus = generateTreeData<MenuTreeNode, PermissionJSON>(permissionJSONs, {
      key: 'id',
      parentKey: 'parentId',
      isRootNode: permission => permission.code === 'menuRoot',
      filterNode: (permission, _, depth) =>
        (depth < 3 && permission.type === 'MENU') ||
        // 显示 3 级菜单，无论第 3 级的权限类型是菜单或页面
        (depth === 3 && ['MENU', 'PAGE'].includes(permission.type)),
      getNode: (permission, children) => ({
        metaCode: permission.code,
        metaName: permission.name,
        remarks: permission.remarks,
        routePath: permission.routePath,
        children: children === null ? undefined : children,
      }),
    });
    if (menus.length > 0 && menus[0].children) {
      return menus[0].children;
    }
    return [];
  }, [codes, entities]);
  let filterMenuList = menuList;

  if (!idc) {
    // 未在首页选中机房时不展示"监控中心"菜单
    filterMenuList = filterMenuList.filter(item => item.metaCode !== 'idc_monitoring');
  } else {
    // @ts-ignore value 存在，但类型中不存在
    const myIdcs = mySpaces?.map(space => space.value);
    // 选中过机房，判断机房在资源权限中是否存在
    const newIdc = !myIdcs || myIdcs.length === 0 ? null : myIdcs.includes(idc!) ? idc : myIdcs[0];
    dispatch(userSliceActions.setUserIdc(newIdc));
  }

  const getLinkTo = React.useCallback(
    (link: any, backendAuthItem: MenuTreeNode) => {
      const variables: any = {};
      if (backendAuthItem.remarks) {
        const dashboardVarsRegex = /dashboard-id=\S+/g;
        const dashboardIdVarStr = dashboardVarsRegex.exec(backendAuthItem.remarks);
        if (dashboardIdVarStr) {
          variables.dashboardId = dashboardIdVarStr[0].split('=')[1] || 'not-exists-id';
        }
      }
      if (typeof link == 'function') {
        return link({ idc, ...variables });
      }
      if (!idc) {
        return link ?? backendAuthItem.routePath;
      }
      const _link = link ?? backendAuthItem.routePath;

      return !_link ? '/' : replaceIdcParam(_link.replace(':idc', idc), idc);
    },
    [idc]
  );

  const getText = React.useCallback(
    (menuItem: any, fontSize?: string) => {
      let text = menuItem.metaName;
      const link = routesMapper[menuItem.metaCode]?.link;
      if (link) {
        text = (
          <Link
            style={{
              fontSize: fontSize,
              color: 'inherit',
            }}
            to={getLinkTo(link, menuItem)}
          >
            {menuItem.metaName}
          </Link>
        );
      }
      return text;
    },
    [getLinkTo]
  );

  return (
    <BaseLayout
      style={style}
      className={classNames(styles.layout, className)}
      prefixCls="manyun-layout"
    >
      <Watermark />
      <Header
        homeUrl={homeUrl}
        showMenuLevel2s={showMenuLevel2s}
        menus={filterMenuList}
        currentKeys={selectedMenuCodes}
        getLinkTo={getLinkTo}
        getText={getText}
      />
      {!pathname.includes('/page/graphix') && <AnnouncementRemindNotificationList />}
      <UpdateMyPasswordModal />
      <div style={{ height: 50 }} />
      <div className="dc-base-layout_popup-container" />
      {/* <GetSideBar /> */}
      {children}
      <UserProfilePromptModalView />
    </BaseLayout>
  );
}
//侧边栏现在禁用掉
// function GetSideBar() {
//   const { selectedMenuCodes } = useLayout();
//   const [nodes, setNodes] = React.useState<React.ReactNode[]>([]);
//   React.useEffect(() => {
//     const headMenuItems = layoutRegistry.getFabricsByVariant('side-bar');
//     const _nodes = headMenuItems.map(([key, [Fabric, { prepare }]]) => {
//       const _fabricProps = prepare?.({ selectedMenuCodes, SideBar });
//       return <Fabric key={key} {..._fabricProps} />;
//     });
//     setNodes(_nodes);
//   }, [selectedMenuCodes]);

//   return <>{nodes}</>;
// }
