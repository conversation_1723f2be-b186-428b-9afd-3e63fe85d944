import { generatePath } from 'react-router-dom';

import { qs } from '@manyun/base-ui.util.query-string';
import type { ExcelStatementSubjobType } from '@manyun/ticket.model.task';

// 版本管理
export const VERSION_MANAGE_DEATAIL = '/page/version-manage/detail';
export const SERVER_NODE_LIST = '/page/server-node/list';
export const SERVER_NODE_DETAIL_PATH = '/page/server-node/detail/:id';
export const SERVER_NODE_DETAIL_AUTH_CODE = 'page_server-node-detail';

export type VersionManageDetailTabType =
  | 'DEVICE'
  | 'SPEC'
  | 'POINT'
  | 'NON_POINT'
  | 'ALARM'
  | 'NON_ALARM'
  | 'SPACE'
  | 'CHANNEL'
  | 'SPACE_CNF'
  | 'CONFIG_DATA'
  | 'USER_DATA'
  | 'AI_SCENES';

export const generateVersionManageDetailRoutePath = ({
  tab,
  idc,
}: {
  tab?: VersionManageDetailTabType;
  idc?: string;
}) => {
  if (idc) {
    return `${VERSION_MANAGE_DEATAIL}?idc=${idc}${tab ? `&tab=${tab}` : ''}`;
  }
  if (tab) {
    return `${VERSION_MANAGE_DEATAIL}?${qs.stringify({ tab })}`;
  }
  return VERSION_MANAGE_DEATAIL;
};

// 通道配置
export const CHANNEL_CONFIG_LIST = '/page/channel-config/list';
export const CHANNEL_CONFIG_DETAIL_PATH = '/page/channel-config/detail/:id';
export const CHANNEL_CONFIG_DETAIL_AUTH_CODE = 'page_channel-config-info';
export const CHANNEL_CONFIG_NEW = '/page/channel-config/new';
export const CHANNEL_CONFIG_EDIT = '/page/channel-config/edit/:id';
export const CHANNEL_CONFIG_ASSOCIATE = '/page/channel-config/associate/:id';
export const CHANNEL_CONFIG_IMPORT = '/page/channel-config/import/:id';

// 北向用户管理
export const NORTH_USER_LIST = '/page/north-user/list';
export const NORTH_USER_DETAIL = '/page/north-user/detail/:id';
export const NORTH_USER_NEW = '/page/north-user/new';
//北向管理 menu_north
export const NORTH_BOUND_MANAGE = '/page/north-bound-manage/list';
export const NORTH_BOUND_MANAGE_CODE = 'page_north';
export const NORTH_BOUND_CREATE = '/page/north-bound-manage/create';
export const NORTH_BOUND_CREATE_CODE = 'page_north_create';
export const NORTH_BOUND_EDIT = '/page/north-bound-manage/edit/:id';
export const NORTH_BOUND_EDIT_CODE = 'page_north_edit';

// 告警传输
export const ALARM_TRANSMISSION_RECORD_LIST = '/page/alarm-transmission-record/list';

export const generateAlarmTransmissionUrl = ({ id }: { id: string }) => {
  return {
    pathname: ALARM_TRANSMISSION_RECORD_LIST,
    search: `?id=${id}`,
  };
};

// 生成通道详情页面url
export const generateChannelConfigDetailPath = ({ id }: { id: string }) =>
  generatePath(CHANNEL_CONFIG_DETAIL_PATH, { id });

//生成节点详情页面url
export const generateServerNodeDetailPath = ({ id }: { id: string }) =>
  generatePath(SERVER_NODE_DETAIL_PATH, { id });

export const DC_BRAIN_ROUTER_PATH_AUTH_CODE_MAPPER: Record<string, string> = {
  [CHANNEL_CONFIG_DETAIL_PATH]: CHANNEL_CONFIG_DETAIL_AUTH_CODE,
  [SERVER_NODE_DETAIL_PATH]: SERVER_NODE_DETAIL_AUTH_CODE,
};

// Excel 报表
export const EXCEL_STATEMENT_LIST_ROUTE_PATH = '/page/excel-statement/list';
export const EXCEL_STATEMENT_LIST_ROUTE_AUTH_CODE = 'page_excel-statement-list';

export const EXCEL_STATEMENT_DETAIL_ROUTE_PATH = '/page/excel-statement/detail/:id';
export const EXCEL_STATEMENT_DETAIL_ROUTE_AUTH_CODE = 'page_excel-statement-detail';

export const EXCEL_STATEMENT_EDIT_ROUTE_PATH = '/page/excel-statement/edit/:id/:type';
export const EXCEL_STATEMENT_EDIT_ROUTE_AUTH_CODE = 'page_excel-statement-edit';

export const EXCEL_STATEMENT_NEW_ROUTE_PATH = '/page/excel-statement/new/:type';
export const EXCEL_STATEMENT_NEW_ROUTE_AUTH_CODE = 'page_excel-statement-new';

export type ExcelStatementListParams = {
  type?: ExcelStatementSubjobType;
};
export const generateExcelStatementListLocation = (params: ExcelStatementListParams) => {
  if (params.type) {
    return `${EXCEL_STATEMENT_LIST_ROUTE_PATH}?type=${params.type}`;
  }
  return EXCEL_STATEMENT_LIST_ROUTE_PATH;
};

export type ExcelStatementDeteilParams = {
  id: string;
};
export const generateExcelStatementDeteilLocation = (params: ExcelStatementDeteilParams) =>
  generatePath(EXCEL_STATEMENT_DETAIL_ROUTE_PATH, params);

export type ExcelStatementNewParams = {
  type: ExcelStatementSubjobType;
};
export const generateExcelStatementNewLocation = (params: ExcelStatementNewParams) =>
  generatePath(EXCEL_STATEMENT_NEW_ROUTE_PATH, params);

export type ExcelStatementEditParams = {
  id: string;
  type: ExcelStatementSubjobType;
};
export const generateExcelStatementEditLocation = (params: ExcelStatementEditParams) =>
  generatePath(EXCEL_STATEMENT_EDIT_ROUTE_PATH, params);

export const ADMIN_ROUTER_PATH_AUTH_CODE_MAPPER = {
  [EXCEL_STATEMENT_LIST_ROUTE_PATH]: EXCEL_STATEMENT_LIST_ROUTE_AUTH_CODE,
  [EXCEL_STATEMENT_DETAIL_ROUTE_PATH]: EXCEL_STATEMENT_DETAIL_ROUTE_AUTH_CODE,
  [EXCEL_STATEMENT_EDIT_ROUTE_PATH]: EXCEL_STATEMENT_EDIT_ROUTE_AUTH_CODE,
  [EXCEL_STATEMENT_NEW_ROUTE_PATH]: EXCEL_STATEMENT_NEW_ROUTE_AUTH_CODE,
} as const;
